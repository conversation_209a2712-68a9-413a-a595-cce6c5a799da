"""
Simplified advanced performance tests for the MindLink Agent Core library.

These tests evaluate the library's performance under various advanced scenarios
to ensure it is fully operational and can handle complex workloads.
"""

import pytest
import os
import time
import tempfile
import shutil
import random
import string
from pathlib import Path

# Import the components we need to test
from mindlink.tools.file_tools import (
    create_file,
    read_file,
    path_exists,
    ReadFileTool
)

# Import other tools for cross-module testing
from mindlink.tools.graph_tools import GenerateCallGraphTool
from mindlink.tools.base import tool_registry


def generate_random_content(size_kb):
    """Generate random content of specified size in KB."""
    chars = string.ascii_letters + string.digits
    return ''.join(random.choice(chars) for _ in range(size_kb * 1024))


def test_large_file_performance():
    """
    Test 1: Large File Performance
    
    This test evaluates how the library performs when handling large files.
    """
    # Create a temporary directory for this test
    temp_dir = tempfile.mkdtemp()
    try:
        # File sizes to test (in KB)
        file_sizes = [100, 500, 1000]  # Up to 1MB
        
        start_time = time.time()
        for size in file_sizes:
            file_path = os.path.join(temp_dir, f"large_file_{size}kb.txt")
            content = generate_random_content(size)
            
            # Create the large file
            create_file(file_path, content)
            
            # Verify the file was created with correct content
            assert path_exists(file_path)
            assert len(read_file(file_path)) == len(content)
        
        end_time = time.time()
        execution_time = end_time - start_time
        print(f"Large file handling execution time: {execution_time:.2f} seconds")
        
        # Verify all files exist after the operation
        for size in file_sizes:
            file_path = os.path.join(temp_dir, f"large_file_{size}kb.txt")
            assert path_exists(file_path)
            
            # Verify file size
            file_size = os.path.getsize(file_path)
            assert file_size >= size * 1024  # Size should be at least the requested size
        
        # Performance assertion - should complete within a reasonable time
        assert execution_time < 30, f"Large file operations took too long: {execution_time:.2f} seconds"
    
    finally:
        # Clean up
        shutil.rmtree(temp_dir)


def test_multiple_file_operations_performance():
    """
    Test 2: Multiple File Operations Performance
    
    This test evaluates how the library performs when handling many small files.
    """
    # Create a temporary directory for this test
    temp_dir = tempfile.mkdtemp()
    try:
        # Number of files to create
        num_files = 100
        
        # Create many small files
        start_time = time.time()
        for i in range(num_files):
            file_path = os.path.join(temp_dir, f"file_{i}.txt")
            content = f"Content for file {i}"
            create_file(file_path, content)
        
        # Read all files
        for i in range(num_files):
            file_path = os.path.join(temp_dir, f"file_{i}.txt")
            content = read_file(file_path)
            assert f"Content for file {i}" == content
        
        end_time = time.time()
        execution_time = end_time - start_time
        print(f"Multiple file operations execution time: {execution_time:.2f} seconds")
        
        # Performance assertion - should complete within a reasonable time
        assert execution_time < 30, f"Multiple file operations took too long: {execution_time:.2f} seconds"
    
    finally:
        # Clean up
        shutil.rmtree(temp_dir)


def test_tool_performance():
    """
    Test 3: Tool Performance
    
    This test evaluates how the library's tools perform when handling files.
    """
    # Create a temporary directory for this test
    temp_dir = tempfile.mkdtemp()
    try:
        # Create a test file
        file_path = os.path.join(temp_dir, "test_file.txt")
        content = "Test content for tool performance"
        create_file(file_path, content)
        
        # Test ReadFileTool performance
        read_tool = ReadFileTool()
        
        start_time = time.time()
        for _ in range(50):  # Perform multiple reads to measure performance
            result = read_tool.execute(path=file_path)
            assert result["status"] == "success"
            assert result["observation"] == content
        
        end_time = time.time()
        execution_time = end_time - start_time
        print(f"ReadFileTool execution time for 50 reads: {execution_time:.2f} seconds")
        
        # Performance assertion - should complete within a reasonable time
        assert execution_time < 10, f"Tool operations took too long: {execution_time:.2f} seconds"
    
    finally:
        # Clean up
        shutil.rmtree(temp_dir)


def test_cross_module_tool_integration():
    """
    Test 4: Cross-Module Tool Integration
    
    This test evaluates how the library performs when integrating tools from
    different modules, testing both direct usage and through the tool registry.
    """
    # Create a temporary directory with Python files for testing
    temp_dir = tempfile.mkdtemp()
    try:
        # Create a Python file with functions for call graph analysis
        py_file_path = os.path.join(temp_dir, "module.py")
        py_content = """
def function_a():
    function_b()
    function_c()
    return True

def function_b():
    function_d()
    return "result"

def function_c():
    return function_d()

def function_d():
    return 42
"""
        # Create the Python file
        create_file(py_file_path, py_content)
        
        # Verify the file was created
        assert path_exists(py_file_path)
        
        # Test integration between file tools and graph tools
        start_time = time.time()
        
        # Use file tools to read the file
        read_tool = ReadFileTool()
        read_result = read_tool.execute(path=py_file_path)
        assert read_result["status"] == "success"
        assert "function_a" in read_result["observation"]
        
        # Use graph tools to analyze the file
        graph_tool = GenerateCallGraphTool()
        graph_result = graph_tool.execute(path=py_file_path)
        assert graph_result["status"] == "success"
        
        # Parse the graph result
        import json
        call_graph = json.loads(graph_result["observation"])
        
        # Verify the call graph is correct
        assert "function_a" in call_graph
        assert "function_b" in call_graph["function_a"]
        assert "function_c" in call_graph["function_a"]
        assert "function_d" in call_graph["function_b"]
        
        # Test accessing tools through the registry
        assert "generate_call_graph" in tool_registry
        registry_graph_tool = tool_registry["generate_call_graph"]()
        registry_result = registry_graph_tool.execute(path=py_file_path)
        assert registry_result["status"] == "success"
        
        end_time = time.time()
        execution_time = end_time - start_time
        print(f"Cross-module tool integration execution time: {execution_time:.2f} seconds")
        
        # Performance assertion
        assert execution_time < 10, f"Cross-module integration took too long: {execution_time:.2f} seconds"
    
    finally:
        # Clean up
        shutil.rmtree(temp_dir)


def test_error_handling_performance():
    """
    Test 5: Error Handling Performance
    
    This test evaluates how the library performs when encountering errors.
    """
    # Create a temporary directory for this test
    temp_dir = tempfile.mkdtemp()
    try:
        # Number of operations to perform
        num_operations = 50
        
        # Create a list of operations, some of which will fail
        operations = []
        for i in range(num_operations):
            file_path = os.path.join(temp_dir, f"file_{i}.txt")
            
            # Every 5th operation will be designed to fail
            if i % 5 == 0:
                # Invalid operation (trying to read a non-existent file)
                operations.append(("read", os.path.join(temp_dir, f"nonexistent_{i}.txt")))
            else:
                # Valid operation
                operations.append(("create", file_path, f"Content for file {i}"))
        
        # Execute operations and measure performance
        start_time = time.time()
        success_count = 0
        error_count = 0
        
        for op in operations:
            try:
                if op[0] == "create":
                    create_file(op[1], op[2])
                    success_count += 1
                elif op[0] == "read":
                    try:
                        content = read_file(op[1])
                        success_count += 1
                    except FileNotFoundError:
                        # Expected error for non-existent files
                        error_count += 1
            except Exception as e:
                error_count += 1
                print(f"Operation error: {e}")
        
        # Verify we can continue operations after errors
        additional_file = os.path.join(temp_dir, "after_errors.txt")
        create_file(additional_file, "Created after errors")
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        print(f"Error handling test execution time: {execution_time:.2f} seconds")
        print(f"Successful operations: {success_count}")
        print(f"Error operations: {error_count}")
        
        # Verify the expected number of errors
        expected_errors = num_operations // 5  # Every 5th operation should fail
        assert error_count == expected_errors, f"Expected {expected_errors} errors, got {error_count}"
        
        # Verify we could continue after errors
        assert path_exists(additional_file)
        content = read_file(additional_file)
        assert content == "Created after errors"
        
        # Performance assertion
        assert execution_time < 10, f"Error handling test took too long: {execution_time:.2f} seconds"
    
    finally:
        # Clean up
        shutil.rmtree(temp_dir)


if __name__ == "__main__":
    pytest.main(["-xvs", __file__])
