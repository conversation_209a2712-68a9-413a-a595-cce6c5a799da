"""
Advanced performance tests for the MindLink Agent Core library.

These tests evaluate the library's performance under various advanced scenarios
to ensure it is fully operational and can handle complex workloads.
"""

import pytest
import os
import time
import tempfile
import shutil
import random
import string
import threading
import concurrent.futures
import gc
import sys
from pathlib import Path

# Import the components we need to test
from mindlink.tools.file_tools import (
    TransactionManager,
    create_file,
    read_file,
    list_files,
    path_exists,
    CreateFileTool,
    ReadFileTool,
    ListFilesTool
)

# Import other tools for cross-module testing
from mindlink.tools.graph_tools import GenerateCallGraphTool
from mindlink.tools.base import Tool, tool_registry


@pytest.fixture
def large_test_dir():
    """Create a temporary directory with many files for performance testing."""
    temp_dir = tempfile.mkdtemp()

    # Create a directory structure with many files
    for i in range(5):
        subdir = os.path.join(temp_dir, f"subdir_{i}")
        os.makedirs(subdir, exist_ok=True)

        for j in range(20):
            file_path = os.path.join(subdir, f"file_{j}.txt")
            with open(file_path, "w") as f:
                f.write(f"Content for file {j} in subdir {i}")

    yield temp_dir
    # Clean up after the test
    shutil.rmtree(temp_dir)


def generate_random_content(size_kb):
    """Generate random content of specified size in KB."""
    chars = string.ascii_letters + string.digits
    return ''.join(random.choice(chars) for _ in range(size_kb * 1024))


def test_concurrent_file_operations_performance():
    """
    Test 1: Concurrent File Operations Performance

    This test evaluates how the library performs when multiple threads
    are creating and reading files, but using separate files for each thread
    to avoid concurrency issues.
    """
    # Create a temporary directory for this test
    temp_dir = tempfile.mkdtemp()
    try:
        # Number of concurrent operations
        num_threads = 10  # Reduced from 20 to avoid overwhelming the system
        # Number of operations per thread
        ops_per_thread = 20  # Reduced from 50 to make test faster

        # Function to be executed by each thread
        def worker(thread_id):
            thread_dir = os.path.join(temp_dir, f"thread_{thread_id}")
            os.makedirs(thread_dir, exist_ok=True)

            # Create files first (outside of concurrent execution)
            for i in range(0, ops_per_thread, 3):  # Only every 3rd operation is create
                file_path = os.path.join(thread_dir, f"file_{i}.txt")
                content = f"Content for file {i} from thread {thread_id}"
                # Create file directly without transaction to avoid thread safety issues
                with open(file_path, 'w') as f:
                    f.write(content)

            # Now perform read operations
            for i in range(ops_per_thread):
                if i % 3 != 0:  # Skip create operations, only do read and exists
                    file_path = os.path.join(thread_dir, f"file_{i-(i%3)}.txt")
                    if i % 3 == 1:  # Read
                        if os.path.exists(file_path):
                            with open(file_path, 'r') as f:
                                content = f.read()
                            assert "Content for file" in content
                    else:  # Check exists
                        exists = os.path.exists(file_path)

        # Create and start threads
        start_time = time.time()
        with concurrent.futures.ThreadPoolExecutor(max_workers=num_threads) as executor:
            futures = [executor.submit(worker, i) for i in range(num_threads)]
            # Wait for all threads to complete
            for future in concurrent.futures.as_completed(futures):
                future.result()  # This will raise any exceptions from the threads

        end_time = time.time()
        execution_time = end_time - start_time
        print(f"Concurrent file operations execution time: {execution_time:.2f} seconds")

        # Verify that files were created correctly
        for thread_id in range(num_threads):
            thread_dir = os.path.join(temp_dir, f"thread_{thread_id}")
            for i in range(0, ops_per_thread, 3):  # Check only created files
                file_path = os.path.join(thread_dir, f"file_{i}.txt")
                assert os.path.exists(file_path)
                with open(file_path, 'r') as f:
                    content = f.read()
                assert f"Content for file {i} from thread {thread_id}" == content

        # Performance assertion - should complete within a reasonable time
        assert execution_time < 30, f"Concurrent operations took too long: {execution_time:.2f} seconds"

    finally:
        # Clean up
        shutil.rmtree(temp_dir)


def test_memory_consumption_performance():
    """
    Test 2: Memory Consumption Performance

    This test evaluates how the library manages memory when handling large files
    and multiple transactions by measuring execution time and file sizes.
    """
    # Create a temporary directory for this test
    temp_dir = tempfile.mkdtemp()
    try:
        # File sizes to test (in KB)
        file_sizes = [100, 500, 1000]  # Up to 1MB
        num_files = 10  # Number of files per size

        # Create files of various sizes
        start_time = time.time()
        with TransactionManager():
            for size in file_sizes:
                for i in range(num_files):
                    file_path = os.path.join(temp_dir, f"large_file_{size}kb_{i}.txt")
                    content = generate_random_content(size)
                    create_file(file_path, content)

        # Force garbage collection
        gc.collect()

        # Read all files to test memory during read operations
        with TransactionManager():
            for size in file_sizes:
                for i in range(num_files):
                    file_path = os.path.join(temp_dir, f"large_file_{size}kb_{i}.txt")
                    content = read_file(file_path)
                    # Verify content length
                    assert len(content) >= size * 1024

        # Force garbage collection again
        gc.collect()

        end_time = time.time()
        execution_time = end_time - start_time

        # Print performance statistics
        print(f"Memory consumption test execution time: {execution_time:.2f} seconds")

        # Calculate total file size
        total_file_size_mb = sum(size * num_files for size in file_sizes) / 1024
        print(f"Total file size processed: {total_file_size_mb:.2f} MB")

        # Verify all files exist and have correct sizes
        for size in file_sizes:
            for i in range(num_files):
                file_path = os.path.join(temp_dir, f"large_file_{size}kb_{i}.txt")
                assert path_exists(file_path)
                file_size = os.path.getsize(file_path) / 1024  # KB
                assert file_size >= size, f"File size too small: {file_size:.2f} KB, expected at least {size} KB"

        # Performance assertion - should complete within a reasonable time
        assert execution_time < 30, f"Memory test took too long: {execution_time:.2f} seconds"

    finally:
        # Clean up
        shutil.rmtree(temp_dir)


def test_transaction_nesting_depth_performance():
    """
    Test 3: Transaction Nesting Depth Performance

    This test evaluates how the library performs with moderately nested transactions,
    testing successful commits at various nesting levels.
    """
    # Create a temporary directory for this test
    temp_dir = tempfile.mkdtemp()
    try:
        # Maximum nesting depth to test (reduced to avoid potential issues)
        max_depth = 5

        # Function to create nested transactions
        def create_nested_transaction(depth, max_depth, base_path):
            """Create a nested transaction with files at each level."""
            # Create a file at this level
            file_path = os.path.join(base_path, f"file_at_depth_{depth}.txt")
            content = f"Content at depth {depth}"
            create_file(file_path, content)

            # If we haven't reached the maximum depth, create a nested transaction
            if depth < max_depth:
                # Create a subdirectory for the next level
                next_level_dir = os.path.join(base_path, f"level_{depth}")
                os.makedirs(next_level_dir, exist_ok=True)

                # Create a nested transaction
                with TransactionManager():
                    create_nested_transaction(depth + 1, max_depth, next_level_dir)

        # Test successful nested transactions
        start_time = time.time()
        with TransactionManager():
            create_nested_transaction(0, max_depth, temp_dir)

        success_time = time.time() - start_time
        print(f"Successful nested transactions execution time: {success_time:.2f} seconds")

        # Verify files were created at each level
        for depth in range(max_depth + 1):  # +1 to include the max_depth level
            if depth == 0:
                # The root level file
                file_path = os.path.join(temp_dir, f"file_at_depth_{depth}.txt")
            else:
                # Build the path to the file at this depth
                path_components = ["level_" + str(i) for i in range(depth-1)]
                file_path = os.path.join(temp_dir, *path_components, f"file_at_depth_{depth}.txt")

            assert path_exists(file_path), f"File at depth {depth} not found: {file_path}"
            content = read_file(file_path)
            assert f"Content at depth {depth}" == content, f"Content mismatch at depth {depth}"

        # Performance assertions
        assert success_time < 10, f"Successful nested transactions took too long: {success_time:.2f} seconds"

    finally:
        # Clean up
        shutil.rmtree(temp_dir)


def test_cross_module_tool_integration_performance():
    """
    Test 4: Cross-Module Tool Integration Performance

    This test evaluates how the library performs when integrating tools from
    different modules, testing both direct usage and through the tool registry.
    """
    # Create a temporary directory with Python files for testing
    temp_dir = tempfile.mkdtemp()
    try:
        # Create a Python file with functions for call graph analysis
        py_file_path = os.path.join(temp_dir, "module.py")
        py_content = """
def function_a():
    function_b()
    function_c()
    return True

def function_b():
    function_d()
    return "result"

def function_c():
    return function_d()

def function_d():
    return 42
"""
        # Create the Python file using our file tools
        with TransactionManager():
            create_file(py_file_path, py_content)

        # Verify the file was created
        assert path_exists(py_file_path)

        # Test integration between file tools and graph tools
        start_time = time.time()

        # Use file tools to read the file
        read_tool = ReadFileTool()
        read_result = read_tool.execute(path=py_file_path)
        assert read_result["status"] == "success"
        assert "function_a" in read_result["observation"]

        # Use graph tools to analyze the file
        graph_tool = GenerateCallGraphTool()
        graph_result = graph_tool.execute(path=py_file_path)
        assert graph_result["status"] == "success"

        # Parse the graph result
        import json
        call_graph = json.loads(graph_result["observation"])

        # Verify the call graph is correct
        assert "function_a" in call_graph
        assert "function_b" in call_graph["function_a"]
        assert "function_c" in call_graph["function_a"]
        assert "function_d" in call_graph["function_b"]

        # Test accessing tools through the registry
        assert "generate_call_graph" in tool_registry
        registry_graph_tool = tool_registry["generate_call_graph"]()
        registry_result = registry_graph_tool.execute(path=py_file_path)
        assert registry_result["status"] == "success"

        end_time = time.time()
        execution_time = end_time - start_time
        print(f"Cross-module tool integration execution time: {execution_time:.2f} seconds")

        # Performance assertion
        assert execution_time < 5, f"Cross-module integration took too long: {execution_time:.2f} seconds"

    finally:
        # Clean up
        shutil.rmtree(temp_dir)


def test_error_recovery_and_resilience_performance():
    """
    Test 5: Error Recovery and Resilience Performance

    This test evaluates how the library performs when encountering errors,
    testing its ability to recover and continue operations after failures.
    """
    # Create a temporary directory for this test
    temp_dir = tempfile.mkdtemp()
    try:
        # Number of operations to perform
        num_operations = 100

        # Create a list of operations, some of which will fail
        operations = []
        for i in range(num_operations):
            file_path = os.path.join(temp_dir, f"file_{i}.txt")

            # Every 5th operation will be designed to fail
            if i % 5 == 0:
                # Invalid operation (trying to read a non-existent file)
                operations.append(("read", os.path.join(temp_dir, f"nonexistent_{i}.txt")))
            else:
                # Valid operation
                operations.append(("create", file_path, f"Content for file {i}"))

        # Execute operations and measure performance
        start_time = time.time()
        success_count = 0
        error_count = 0

        for op in operations:
            try:
                if op[0] == "create":
                    with TransactionManager():
                        create_file(op[1], op[2])
                    success_count += 1
                elif op[0] == "read":
                    try:
                        with TransactionManager():
                            content = read_file(op[1])
                        success_count += 1
                    except FileNotFoundError:
                        # Expected error for non-existent files
                        error_count += 1
            except Exception as e:
                error_count += 1
                print(f"Operation error: {e}")

        # Verify we can continue operations after errors
        additional_file = os.path.join(temp_dir, "after_errors.txt")
        with TransactionManager():
            create_file(additional_file, "Created after errors")

        end_time = time.time()
        execution_time = end_time - start_time

        print(f"Error recovery test execution time: {execution_time:.2f} seconds")
        print(f"Successful operations: {success_count}")
        print(f"Error operations: {error_count}")

        # Verify the expected number of errors
        expected_errors = num_operations // 5  # Every 5th operation should fail
        assert error_count == expected_errors, f"Expected {expected_errors} errors, got {error_count}"

        # Verify we could continue after errors
        assert path_exists(additional_file)
        content = read_file(additional_file)
        assert content == "Created after errors"

        # Performance assertion
        assert execution_time < 10, f"Error recovery test took too long: {execution_time:.2f} seconds"

    finally:
        # Clean up
        shutil.rmtree(temp_dir)


if __name__ == "__main__":
    pytest.main(["-xvs", __file__])
