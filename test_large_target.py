#!/usr/bin/env python3
"""
Test script specifically for large target line counts
"""

import os
import sys
import logging
from pathlib import Path

# Add the project root to Python path
sys.path.insert(0, str(Path(__file__).parent))

# Enable info logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

from mindlink.tools.file_tools import GenerateLargeFileTool

def test_large_target():
    """Test the GenerateLargeFileTool with large target optimized for success"""
    tool = GenerateLargeFileTool()
    
    print("=== Testing GenerateLargeFileTool with Large Target ===")
    print("Testing with 1000-line target and optimized settings...")
    
    # Test with large target and optimized settings
    result = tool.execute(
        path="large_target_file.py",
        content_description="Create a comprehensive Python web application framework with the following components: 1) A Flask-based web server with multiple routes and blueprints, 2) Database models using SQLAlchemy with User, Post, Comment, and Category models, 3) Authentication system with login, logout, registration, and password reset, 4) API endpoints for CRUD operations, 5) File upload and download functionality, 6) Email notification system, 7) Logging and error handling throughout, 8) Configuration management, 9) Unit tests for all components, 10) Admin interface for managing users and content. Include extensive comments, docstrings, type hints, and error handling for all functions and classes.",
        target_line_count=1000,
        max_chunks=30,  # Increased max chunks
        chunk_size_description="Generate approximately 80-120 lines of functional Python code with detailed comments, docstrings, and comprehensive implementations. Focus on creating complete functions and classes.",
        context_carryover_lines=25
    )
    
    print(f"\nResult: {result}")
    
    if result.get('status') == 'success':
        lines_written = result.get('result', {}).get('lines_written')
        chunks_written = result.get('result', {}).get('chunks_written')
        target_met = result.get('result', {}).get('target_lines_met')
        
        print(f"\n📝 Lines written: {lines_written}")
        print(f"🔢 Chunks written: {chunks_written}")
        print(f"🎯 Target met: {target_met}")
        print(f"📈 Target vs Actual: 1000 vs {lines_written} ({lines_written/1000*100:.1f}% of target)")
        print(f"📊 Average lines per chunk: {lines_written/chunks_written:.1f}")
        
        if lines_written >= 800:  # 80% of target
            print("🎉 SUCCESS: Generated substantial file!")
        elif lines_written >= 500:  # 50% of target
            print("✅ GOOD: Generated reasonable file size")
        elif lines_written >= 300:  # 30% of target
            print("⚠️  PARTIAL: Generated some content but below expectations")
        else:
            print("❌ FAILED: Generated insufficient content")
            
        # Additional analysis
        if chunks_written >= 10:
            print("✅ Tool generated many chunks")
        elif chunks_written >= 5:
            print("⚠️  Tool generated some chunks")
        else:
            print("❌ Tool stopped too early")
            
    else:
        print(f"❌ Error: {result.get('error', 'Unknown error')}")
        print(f"Observation: {result.get('observation', 'No observation')}")

if __name__ == "__main__":
    test_large_target()