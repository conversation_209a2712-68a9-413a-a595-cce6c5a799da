import unittest
from unittest.mock import MagicMock, patch, call
import sys
from io import String<PERSON>

from mindlink.tools.base import Tool, ToolParameters, register_tool, tool_registry, unregister_tool
from mindlink.tools.batch_execute_tool import BatchExecuteTool
from mindlink.schemas.mindlink import Action

# Mock Tool that supports streaming
class MockStreamingTool(Tool):
    name = "mock_streaming_tool"
    description = "A mock tool that supports streaming."

    class Parameters(ToolParameters):
        data_chunks: list = ["chunk1", "chunk2", "chunk3"]

    parameters_model = Parameters

    def execute(self, **parameters) -> dict:
        data_chunks = parameters.get("data_chunks", ["default_chunk"])
        stream = parameters.get("stream", False)
        callback = parameters.get("callback")

        if stream and callback:
            for chunk in data_chunks:
                callback(chunk)
            return {"observation": "".join(data_chunks), "status": "success"}
        else:
            return {"observation": "".join(data_chunks), "status": "success"}

# Mock Tool that does NOT support streaming (no stream/callback in signature)
class MockNonStreamingTool(Tool):
    name = "mock_non_streaming_tool"
    description = "A mock tool that does not support streaming."

    class Parameters(ToolParameters):
        message: str = "default message"

    parameters_model = Parameters

    def execute(self, **parameters) -> dict:
        message = parameters.get("message", "default message")
        return {"observation": f"non-streaming: {message}", "status": "success"}


class TestBatchExecuteToolStreaming(unittest.TestCase):

    def setUp(self):
        self.batch_tool = BatchExecuteTool()
        # Register mock tools
        register_tool(MockStreamingTool)
        register_tool(MockNonStreamingTool)
        # Redirect stdout to capture print statements
        self.held_stdout = sys.stdout
        sys.stdout = StringIO()

    def tearDown(self):
        # Unregister mock tools to avoid interference
        unregister_tool("mock_streaming_tool")
        unregister_tool("mock_non_streaming_tool")
        # Restore stdout
        sys.stdout.close()
        sys.stdout = self.held_stdout
        # Clear batch execute cache if it affects tests (not strictly necessary here as keys are unique)
        BatchExecuteTool._batch_execute_cache = {}


    def test_batch_execute_with_streaming_sub_tool(self):
        plan = [
            Action(tool_name="mock_streaming_tool", parameters={"data_chunks": ["s1_c1 ", "s1_c2"]})
        ]
        
        # Mock the execute method of the MockStreamingTool instance that will be created
        # This is a bit tricky as the instance is created inside BatchExecuteTool
        # So, we patch the class's execute method instead.
        
        mock_streaming_tool_instance_execute = MagicMock(
            wraps=MockStreamingTool().execute # Use a real instance to wrap for correct behavior
        )

        with patch.object(MockStreamingTool, 'execute', new=mock_streaming_tool_instance_execute):
            result = self.batch_tool.execute(plan=plan, stream=True)

        self.assertEqual(result['status'], 'success')
        self.assertEqual(result['observation'], "s1_c1 s1_c2")

        # Check that the MockStreamingTool's execute was called with stream and callback
        # The first call to the mock will be the one from BatchExecuteTool
        # It's hard to assert the callback instance directly, so we check for stream=True
        # and that a callable was passed as callback.
        args, kwargs = mock_streaming_tool_instance_execute.call_args
        self.assertTrue(kwargs.get('stream'))
        self.assertTrue(callable(kwargs.get('callback')))
        
        # Verify that BatchExecuteTool printed the streamed chunks
        output = sys.stdout.getvalue()
        self.assertIn("[BatchExecute][mock_streaming_tool][STREAM] s1_c1 ", output)
        self.assertIn("[BatchExecute][mock_streaming_tool][STREAM] s1_c2", output)
        # Also check the final observation print from BatchExecuteTool if the sub-tool didn't "fully" stream
        # In this case, the callback builds the result, so BatchExecuteTool shouldn't print the full obs again
        # if it was already printed by the sub-tool's callback mechanism via BatchExecute's stream flag.
        # The current implementation of BatchExecuteTool might print the final observation of a successfully streamed tool
        # if `executed_with_streaming and sub_tool_stream_data` is true, it relies on the callback to print.
        # If the callback prints, then BatchExecuteTool does not print the final full observation line.
        # Let's ensure it's not printing the full line again like "[BatchExecute][mock_streaming_tool] s1_c1 s1_c2"
        # This depends on the exact logic in BatchExecuteTool's print conditions.
        # The current BatchExecuteTool logic:
        # if stream and not (executed_with_streaming and sub_tool_stream_data): print(final_obs)
        # Since our mock tool uses the callback, sub_tool_stream_data will be populated, so this should be false.
        self.assertNotIn(f"[BatchExecute][mock_streaming_tool] s1_c1 s1_c2\n", output)


    def test_batch_execute_with_non_streaming_sub_tool(self):
        plan = [
            Action(tool_name="mock_non_streaming_tool", parameters={"message": "hello"})
        ]
        
        # Patch the non-streaming tool's execute to monitor its call
        mock_non_streaming_execute = MagicMock(wraps=MockNonStreamingTool().execute)
        
        with patch.object(MockNonStreamingTool, 'execute', new=mock_non_streaming_execute):
            result = self.batch_tool.execute(plan=plan, stream=True)

        self.assertEqual(result['status'], 'success')
        self.assertEqual(result['observation'], "non-streaming: hello")

        # Verify its execute was called, but without stream/callback if fallback worked
        args, kwargs = mock_non_streaming_execute.call_args
        self.assertNotIn('stream', kwargs) # Due to TypeError fallback
        self.assertNotIn('callback', kwargs) # Due to TypeError fallback
        
        # Verify BatchExecuteTool printed the final observation (since sub-tool didn't stream)
        output = sys.stdout.getvalue()
        self.assertIn("[BatchExecute][mock_non_streaming_tool] non-streaming: hello", output)
        # Ensure no "STREAM" messages for this tool
        self.assertNotIn("[BatchExecute][mock_non_streaming_tool][STREAM]", output)

if __name__ == '__main__':
    unittest.main()
