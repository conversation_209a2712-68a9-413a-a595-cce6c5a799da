#!/usr/bin/env python3
"""
Test script to verify the fix for generate_large_file tool parameter issue.
"""

import sys
import os
sys.path.insert(0, os.path.dirname(__file__))

from mindlink.agent import AgentOS
from mindlink.models.openrouter import OpenRouterModel

def test_generate_large_file_fix():
    """Test that the agent now properly provides path parameter to generate_large_file."""
    
    # Initialize the agent
    llm = OpenRouterModel()
    system_prompt = "You are a helpful AI assistant."
    agent = AgentOS(llm=llm, system_prompt_template=system_prompt)
    
    # Test goal that should trigger generate_large_file
    goal = "Create 3 Python files with 100 lines of code each for a simple web scraper project"
    
    print(f"Testing goal: {goal}")
    print("=" * 60)
    
    try:
        # Generate a plan
        plan = agent.plan_once(goal)
        
        print(f"Generated plan with {len(plan)} actions:")
        for i, action in enumerate(plan, 1):
            print(f"  {i}. Tool: {action.tool_name}")
            print(f"     Parameters: {action.parameters}")
            
            # Check if generate_large_file has required parameters
            if action.tool_name == "generate_large_file":
                params = action.parameters or {}
                if 'path' not in params:
                    print(f"     ❌ ERROR: Missing 'path' parameter!")
                    return False
                elif 'content_description' not in params:
                    print(f"     ❌ ERROR: Missing 'content_description' parameter!")
                    return False
                else:
                    print(f"     ✅ SUCCESS: Both required parameters present")
                    print(f"        - path: {params['path']}")
                    print(f"        - content_description: {params['content_description'][:50]}...")
            print()
        
        # Check if any generate_large_file actions were created
        generate_actions = [a for a in plan if a.tool_name == "generate_large_file"]
        if generate_actions:
            print(f"✅ Found {len(generate_actions)} generate_large_file actions with proper parameters")
            return True
        else:
            print("ℹ️  No generate_large_file actions in plan (this might be expected)")
            return True
            
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_direct_translation():
    """Test the direct NL to tool call translation."""
    
    llm = OpenRouterModel()
    system_prompt = "You are a helpful AI assistant."
    agent = AgentOS(llm=llm, system_prompt_template=system_prompt)
    
    # Test direct translation
    goal = "Generate a large Python file with 200 lines for a calculator application"
    
    print(f"\nTesting direct translation: {goal}")
    print("=" * 60)
    
    try:
        request = agent._translate_nl_to_tool_call(goal)
        
        if request and request.action.tool_name == "generate_large_file":
            params = request.action.parameters or {}
            print(f"Tool: {request.action.tool_name}")
            print(f"Parameters: {params}")
            
            if 'path' in params and 'content_description' in params:
                print("✅ SUCCESS: Direct translation includes both required parameters")
                return True
            else:
                print("❌ ERROR: Direct translation missing required parameters")
                return False
        else:
            print(f"ℹ️  Direct translation resulted in: {request.action.tool_name if request else 'None'}")
            return True
            
    except Exception as e:
        print(f"❌ Error during direct translation test: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("Testing generate_large_file parameter fix...")
    print("=" * 60)
    
    success1 = test_generate_large_file_fix()
    success2 = test_direct_translation()
    
    if success1 and success2:
        print("\n🎉 All tests passed! The fix appears to be working.")
    else:
        print("\n❌ Some tests failed. The issue may still exist.")