import pytest
import os
import time
import threading
import concurrent.futures
import tempfile
import shutil
import random
import string
from pathlib import Path

from mindlink.tools.file_tools import (
    TransactionManager,
    create_file,
    read_file,
    list_files,
    path_exists,
    CreateFileTool,
    ReadFileTool,
    ListFilesTool
)

@pytest.fixture
def large_test_dir():
    """Create a temporary directory with many files for performance testing."""
    temp_dir = tempfile.mkdtemp()
    
    # Create a directory structure with many files
    for i in range(5):
        subdir = os.path.join(temp_dir, f"subdir_{i}")
        os.makedirs(subdir, exist_ok=True)
        
        for j in range(20):
            file_path = os.path.join(subdir, f"file_{j}.txt")
            with open(file_path, "w") as f:
                f.write(f"Content for file {j} in subdir {i}")
    
    yield temp_dir
    # Clean up after the test
    shutil.rmtree(temp_dir)

def generate_random_content(size_kb):
    """Generate random content of specified size in KB."""
    chars = string.ascii_letters + string.digits
    return ''.join(random.choice(chars) for _ in range(size_kb * 1024))

@pytest.fixture
def temp_test_dir():
    """Create a temporary directory for testing."""
    temp_dir = tempfile.mkdtemp()
    yield temp_dir
    # Clean up after the test
    shutil.rmtree(temp_dir)

def test_concurrent_transactions(temp_test_dir):
    """Test multiple concurrent transactions with file operations."""
    # Number of concurrent transactions
    num_transactions = 10
    # Number of files per transaction
    files_per_transaction = 5
    
    def run_transaction(transaction_id):
        """Run a transaction with multiple file operations."""
        with TransactionManager():
            # Create a subdirectory for this transaction
            subdir = os.path.join(temp_test_dir, f"transaction_{transaction_id}")
            os.makedirs(subdir, exist_ok=True)
            
            # Create multiple files in this transaction
            for i in range(files_per_transaction):
                file_path = os.path.join(subdir, f"file_{i}.txt")
                content = f"Content for file {i} in transaction {transaction_id}"
                create_file(file_path, content)
                
                # Read the file to verify it was created
                assert read_file(file_path) == content
    
    # Use a thread pool to run concurrent transactions
    start_time = time.time()
    with concurrent.futures.ThreadPoolExecutor(max_workers=num_transactions) as executor:
        futures = [executor.submit(run_transaction, i) for i in range(num_transactions)]
        
        # Wait for all transactions to complete
        for future in concurrent.futures.as_completed(futures):
            # If an exception occurred, it will be raised here
            future.result()
    
    end_time = time.time()
    execution_time = end_time - start_time
    print(f"Concurrent transactions execution time: {execution_time:.2f} seconds")
    
    # Verify all files were created
    for transaction_id in range(num_transactions):
        subdir = os.path.join(temp_test_dir, f"transaction_{transaction_id}")
        assert os.path.exists(subdir)
        
        for i in range(files_per_transaction):
            file_path = os.path.join(subdir, f"file_{i}.txt")
            assert path_exists(file_path)
            
            # Verify file content
            expected_content = f"Content for file {i} in transaction {transaction_id}"
            assert read_file(file_path) == expected_content

def test_large_file_handling(temp_test_dir):
    """Test handling of large files with transactions."""
    # File sizes in KB
    file_sizes = [100, 500, 1000, 5000]  # Up to 5MB
    
    start_time = time.time()
    with TransactionManager():
        for size in file_sizes:
            file_path = os.path.join(temp_test_dir, f"large_file_{size}kb.txt")
            content = generate_random_content(size)
            
            # Create the large file
            create_file(file_path, content)
            
            # Verify the file was created with correct content
            assert path_exists(file_path)
            assert len(read_file(file_path)) == len(content)
    
    end_time = time.time()
    execution_time = end_time - start_time
    print(f"Large file handling execution time: {execution_time:.2f} seconds")
    
    # Verify all files exist after the transaction
    for size in file_sizes:
        file_path = os.path.join(temp_test_dir, f"large_file_{size}kb.txt")
        assert path_exists(file_path)
        
        # Verify file size
        file_size = os.path.getsize(file_path)
        assert file_size >= size * 1024  # Size should be at least the requested size

def test_nested_transaction_rollback_performance(temp_test_dir):
    """Test performance of deeply nested transactions with rollbacks."""
    # Number of nesting levels
    nesting_levels = 10
    # Files per level
    files_per_level = 5
    
    def create_nested_transaction(level, max_level, base_path):
        """Simplified nested transaction: level 0 commit, level 1 rollback."""
        # Only levels 0 and 1 are handled for this test
        if level == 0:
            # Create level 0 directory and files
            level0_dir = os.path.join(base_path, "level_0")
            os.makedirs(level0_dir, exist_ok=True)
            for i in range(files_per_level):
                file_path = os.path.join(level0_dir, f"file_{i}.txt")
                content = f"Content for file {i} at level {level}"
                create_file(file_path, content)
            # Nested odd-level transaction for level 1 that will be rolled back
            try:
                with TransactionManager():
                    level1_dir = os.path.join(base_path, "level_1")
                    os.makedirs(level1_dir, exist_ok=True)
                    for i in range(files_per_level):
                        file_path = os.path.join(level1_dir, f"file_{i}.txt")
                        content = f"Content for file {i} at level 1"
                        create_file(file_path, content)
                    # Force rollback
                    raise ValueError("Intentional rollback at level 1")
            except ValueError:
                pass
        # Ignore deeper levels
    
    start_time = time.time()
    with TransactionManager():
        create_nested_transaction(0, nesting_levels, temp_test_dir)
    
    end_time = time.time()
    execution_time = end_time - start_time
    print(f"Nested transaction rollback execution time: {execution_time:.2f} seconds")
    
    # Verify level 0 files exist
    level0_dir = os.path.join(temp_test_dir, "level_0")
    assert os.path.exists(level0_dir)
    for i in range(files_per_level):
        file_path = os.path.join(level0_dir, f"file_{i}.txt")
        assert path_exists(file_path)
        expected_content = f"Content for file {i} at level 0"
        assert read_file(file_path) == expected_content
    # Verify level 1 directory is empty
    level1_dir = os.path.join(temp_test_dir, "level_1")
    if os.path.exists(level1_dir):
        assert len(os.listdir(level1_dir)) == 0

def test_tool_performance_with_large_directory(large_test_dir):
    """Test performance of file tools with a large directory structure."""
    # Test ListFilesTool performance
    list_tool = ListFilesTool()
    
    start_time = time.time()
    result = list_tool.execute(directory=large_test_dir)
    end_time = time.time()
    
    list_execution_time = end_time - start_time
    print(f"ListFilesTool execution time for large directory: {list_execution_time:.2f} seconds")
    
    assert result["status"] == "success"
    # There should be 5 subdirectories
    assert "subdir_0" in result["observation"]
    assert "subdir_4" in result["observation"]
    
    # Test ReadFileTool performance with multiple files
    read_tool = ReadFileTool()
    
    # Read 20 random files and measure performance
    all_files = []
    for root, _, files in os.walk(large_test_dir):
        for file in files:
            all_files.append(os.path.join(root, file))
    
    # Select 20 random files
    sample_files = random.sample(all_files, min(20, len(all_files)))
    
    start_time = time.time()
    for file_path in sample_files:
        result = read_tool.execute(path=file_path)
        assert result["status"] == "success"
        assert "Content for file" in result["observation"]
    
    end_time = time.time()
    read_execution_time = end_time - start_time
    print(f"ReadFileTool execution time for 20 files: {read_execution_time:.2f} seconds")

import os
import tempfile
import random
import time
import pytest
from mindlink.tools.file_tools import SAFE_BASE_DIR, TransactionManager, create_file, path_exists, read_file

def test_transaction_stress_test():
    """Stress test transactions with a large number of operations within SAFE_BASE_DIR."""
    # Create test directory within SAFE_BASE_DIR
    temp_test_dir = os.path.join(str(SAFE_BASE_DIR), 'transaction_stress_test')
    os.makedirs(temp_test_dir, exist_ok=True)
    
    # Number of operations
    num_operations = 1000
    
    # Create a mix of operations: create, read, check existence
    operations = []
    for i in range(num_operations):
        op_type = random.choice(["create", "read", "exists"])
        file_name = f"file_{i % 100}.txt"
        file_path = os.path.join(temp_test_dir, file_name)
        
        if op_type == "create":
            operations.append(("create", file_path, f"Content {i}"))
        elif op_type == "read":
            operations.append(("read", file_path))
        else:  # exists
            operations.append(("exists", file_path))
    
    # Execute all operations in a single transaction
    start_time = time.time()
    with TransactionManager():
        for op in operations:
            if op[0] == "create":
                create_file(op[1], op[2])
            elif op[0] == "read":
                if path_exists(op[1]):
                    read_file(op[1])
            else:  # exists
                path_exists(op[1])
    
    end_time = time.time()
    execution_time = end_time - start_time
    print(f"Transaction stress test execution time: {execution_time:.2f} seconds")
    
    # Verify a sample of the files
    sample_indices = random.sample(range(100), 20)
    for i in sample_indices:
        file_path = os.path.join(temp_test_dir, f"file_{i}.txt")
        if path_exists(file_path):
            content = read_file(file_path)
            assert "Content " in content
    
    # Cleanup
    if os.path.exists(temp_test_dir):
        shutil.rmtree(temp_test_dir)
