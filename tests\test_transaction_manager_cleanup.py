"""
Test for verifying the TransactionManager's handling of permission errors during cleanup.
"""
import os
import sys
import tempfile
import unittest
import shutil
import stat
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.DEBUG, 
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

# Add the parent directory to sys.path to import mindlink modules
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from mindlink.tools.file_tools import TransactionManager, create_file, _transaction_stack, _direct_safe_remove


class TestTransactionManagerCleanup(unittest.TestCase):
    """Test that TransactionManager correctly handles permissions during cleanup."""
    
    def setUp(self):
        """Create a temporary directory for testing."""
        self.temp_dir = tempfile.mkdtemp()
        print(f"Created temp directory: {self.temp_dir}")
    
    def tearDown(self):
        """Clean up the temporary directory."""
        try:
            # Print transaction stack status
            print(f"Transaction stack at tearDown: {_transaction_stack}")
            
            if os.path.exists(self.temp_dir):
                print(f"Cleaning up temp directory: {self.temp_dir}")
                shutil.rmtree(self.temp_dir)
        except Exception as e:
            print(f"Error during tearDown: {e}")
    
    def test_cleanup_with_permission_issues(self):
        """Test cleanup with permission issues."""
        # Create a nested directory structure
        nested_dir = os.path.join(self.temp_dir, "level1", "level2")
        os.makedirs(nested_dir)
        
        # Create some test files
        test_file1 = os.path.join(nested_dir, "test1.txt")
        test_file2 = os.path.join(nested_dir, "test2.txt")
        
        with open(test_file1, "w") as f:
            f.write("Test content 1")
        
        with open(test_file2, "w") as f:
            f.write("Test content 2")
        
        # Make a file read-only to simulate permission issues
        os.chmod(test_file1, stat.S_IREAD)
        
        # Create a test file that will be managed by TransactionManager
        test_target = os.path.join(self.temp_dir, "should_not_exist.txt")
        
        # First verify it doesn't exist yet
        self.assertFalse(os.path.exists(test_target), f"File {test_target} should not exist yet")
        
        # Print transaction stack before test
        print(f"Transaction stack before test: {_transaction_stack}")
        
        # Use TransactionManager to temporarily create the file
        print(f"Starting TransactionManager test...")
        with TransactionManager() as tm:
            # Print transaction stack inside context
            print(f"Transaction stack inside context: {_transaction_stack}")
            
            try:
                # Create the file that should be removed during rollback
                create_file(test_target, "This should not exist after rollback")
                
                # Print transaction stack after file creation
                print(f"Transaction stack after file creation: {_transaction_stack}")
                print(f"Backup dictionary: {tm._backup}")
                
                # Verify the file was created
                self.assertTrue(os.path.exists(test_target), f"File {test_target} should exist during transaction")
                print(f"Created test file: {test_target}")
                
                # Now force an error to trigger rollback
                raise ValueError("Simulated error to trigger rollback")
            except ValueError as e:
                # This exception should trigger the cleanup in __exit__
                print(f"Caught expected exception: {e}")
                # Print transaction stack in exception
                print(f"Transaction stack in exception: {_transaction_stack}")
        
        # Print transaction stack after context exit
        print(f"Transaction stack after context: {_transaction_stack}")
        
        # Verify the transaction reverted properly, file should not exist
        print(f"After TransactionManager, checking if file still exists: {test_target}")
        exists = os.path.exists(test_target)
        print(f"File exists: {exists}")
        
        # If TransactionManager failed to clean up, try direct removal
        if exists:
            print("TransactionManager failed to clean up. Attempting direct removal.")
            removal_success = _direct_safe_remove(test_target)
            print(f"Direct removal success: {removal_success}")
            
            # Update exists status after direct removal attempt
            exists = os.path.exists(test_target)
            print(f"File exists after direct removal: {exists}")
        
        # This should be False if TransactionManager or direct removal properly cleaned up
        self.assertFalse(exists, f"File {test_target} should have been deleted during rollback or direct removal")
    

if __name__ == "__main__":
    unittest.main() 