#!/usr/bin/env python3
"""
Test script to verify the fixed GenerateLargeFileTool
"""

import os
import sys
from pathlib import Path

# Add the project root to Python path
sys.path.insert(0, str(Path(__file__).parent))

from mindlink.tools.file_tools import GenerateLargeFileTool

def test_fixed_tool():
    """Test the fixed GenerateLargeFileTool with proper parameters"""
    tool = GenerateLargeFileTool()
    
    print("=== Testing Fixed GenerateLargeFileTool ===")
    print("Testing with explicit target_line_count parameter...")
    
    # Test with explicit target_line_count
    result = tool.execute(
        path="test_fixed_large_file.py",
        content_description="Create a comprehensive Python web application with Flask, database models, API endpoints, authentication, file handling, and comprehensive functionality. Include detailed comments, docstrings, error handling, and multiple classes and functions.",
        target_line_count=1000,
        max_chunks=15,
        chunk_size_description="Generate a substantial content chunk of about 200-300 lines of functional code with detailed comments and docstrings.",
        context_carryover_lines=20
    )
    
    print(f"\nResult: {result}")
    
    if result.get('status') == 'success':
        file_path = result.get('result', {}).get('file_path')
        lines_written = result.get('result', {}).get('lines_written')
        chunks_written = result.get('result', {}).get('chunks_written')
        target_met = result.get('result', {}).get('target_lines_met')
        
        print(f"\n✅ Success!")
        print(f"📁 File: {file_path}")
        print(f"📝 Lines written: {lines_written}")
        print(f"🔢 Chunks written: {chunks_written}")
        print(f"🎯 Target met: {target_met}")
        
        # Verify file exists and check actual line count
        if os.path.exists(file_path):
            with open(file_path, 'r', encoding='utf-8') as f:
                actual_lines = len(f.readlines())
            print(f"📊 Actual file lines: {actual_lines}")
            print(f"📈 Target vs Actual: {1000} vs {actual_lines} ({actual_lines/1000*100:.1f}% of target)")
            
            if actual_lines >= 800:  # Allow some tolerance
                print("🎉 SUCCESS: Generated file meets expectations!")
            else:
                print("⚠️  WARNING: Generated file is shorter than expected")
        else:
            print("❌ ERROR: File was not created")
    else:
        print(f"❌ Error: {result.get('error', 'Unknown error')}")
        print(f"Observation: {result.get('observation', 'No observation')}")

if __name__ == "__main__":
    test_fixed_tool()