#!/usr/bin/env python3
"""
Test script for the optimized GenerateLargeFileTool performance improvements.
This script tests the tool with various target sizes to verify speed improvements.
"""

import logging
import time
import os
from pathlib import Path

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Import the tool
from mindlink.tools.file_tools import GenerateLargeFileTool

def test_speed_optimization(target_lines, description):
    """
    Test the GenerateLargeFileTool with speed optimizations.
    """
    print(f"\n=== Testing {target_lines} lines: {description} ===")
    
    # Initialize the tool
    tool = GenerateLargeFileTool()
    
    # Test parameters
    test_file = f"test_speed_{target_lines}_lines.py"
    
    # Remove existing test file if it exists
    if os.path.exists(test_file):
        os.remove(test_file)
    
    # Record start time
    start_time = time.time()
    
    # Execute the tool
    result = tool.execute(
        path=test_file,
        content_description=f"A comprehensive Python module for {description} with classes, functions, error handling, and documentation",
        target_line_count=target_lines,
        max_chunks=10,  # Allow up to 10 chunks
        chunk_size_description="Generate substantial, functional Python code with detailed comments and docstrings"
    )
    
    # Record end time
    end_time = time.time()
    execution_time = end_time - start_time
    
    # Print results
    print(f"Execution time: {execution_time:.2f} seconds")
    print(f"Status: {result.get('status', 'unknown')}")
    
    if result.get('status') == 'success':
        result_data = result.get('result', {})
        lines_written = result_data.get('lines_written', 0)
        chunks_written = result_data.get('chunks_written', 0)
        target_met = result_data.get('target_lines_met', False)
        
        print(f"Lines written: {lines_written}/{target_lines} ({lines_written/target_lines*100:.1f}% of target)")
        print(f"Chunks written: {chunks_written}")
        print(f"Target met: {target_met}")
        print(f"Speed: {lines_written/execution_time:.1f} lines/second")
        
        # Check if file exists and get actual line count
        if os.path.exists(test_file):
            with open(test_file, 'r', encoding='utf-8') as f:
                actual_lines = len(f.readlines())
            print(f"Actual file lines: {actual_lines}")
            
            # Performance rating
            if execution_time < 30:  # Less than 30 seconds
                print("✅ EXCELLENT performance (< 30s)")
            elif execution_time < 60:  # Less than 1 minute
                print("✅ GOOD performance (< 1min)")
            elif execution_time < 120:  # Less than 2 minutes
                print("⚠️  ACCEPTABLE performance (< 2min)")
            else:
                print("❌ SLOW performance (> 2min)")
        else:
            print("❌ File was not created")
    else:
        print(f"❌ Error: {result.get('observation', 'Unknown error')}")
    
    return execution_time, result

def main():
    """
    Run speed optimization tests.
    """
    print("🚀 Testing GenerateLargeFileTool Speed Optimizations")
    print("=" * 60)
    
    # Test cases: (target_lines, description)
    test_cases = [
        (200, "data processing utilities"),
        (500, "web API framework"),
        (1000, "machine learning pipeline"),
    ]
    
    total_start = time.time()
    results = []
    
    for target_lines, description in test_cases:
        try:
            execution_time, result = test_speed_optimization(target_lines, description)
            results.append((target_lines, execution_time, result.get('status') == 'success'))
        except Exception as e:
            print(f"❌ Test failed for {target_lines} lines: {e}")
            results.append((target_lines, float('inf'), False))
    
    total_time = time.time() - total_start
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 PERFORMANCE SUMMARY")
    print("=" * 60)
    
    for target_lines, execution_time, success in results:
        status = "✅ SUCCESS" if success else "❌ FAILED"
        if execution_time != float('inf'):
            print(f"{target_lines:4d} lines: {execution_time:6.1f}s - {status}")
        else:
            print(f"{target_lines:4d} lines: ERROR - {status}")
    
    print(f"\nTotal test time: {total_time:.1f} seconds")
    
    # Performance evaluation
    successful_tests = [r for r in results if r[2] and r[1] != float('inf')]
    if successful_tests:
        avg_time = sum(r[1] for r in successful_tests) / len(successful_tests)
        max_time = max(r[1] for r in successful_tests)
        
        print(f"Average execution time: {avg_time:.1f}s")
        print(f"Maximum execution time: {max_time:.1f}s")
        
        if max_time < 60:
            print("\n🎉 OPTIMIZATION SUCCESS: All tests completed in under 1 minute!")
        elif max_time < 120:
            print("\n✅ GOOD PERFORMANCE: All tests completed in under 2 minutes")
        else:
            print("\n⚠️  NEEDS IMPROVEMENT: Some tests took over 2 minutes")
    else:
        print("\n❌ NO SUCCESSFUL TESTS")

if __name__ == "__main__":
    main()