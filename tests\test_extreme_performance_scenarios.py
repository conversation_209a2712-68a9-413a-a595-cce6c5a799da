"""
Extreme performance tests for the MindLink Agent Core library.

These tests evaluate the library's performance under extreme scenarios
that haven't been tested before to ensure it is fully operational.
"""

import pytest
import os
import time
import tempfile
import shutil
import random
import string
import concurrent.futures
import json
from pathlib import Path

# Import the components we need to test
from mindlink.tools.file_tools import (
    TransactionManager,
    create_file,
    read_file,
    list_files,
    path_exists,
    CreateFileTool,
    ReadFileTool,
    ListFilesTool
)

# Import other tools for cross-module testing
from mindlink.tools.graph_tools import GenerateCallGraphTool
from mindlink.tools.base import tool_registry


def generate_random_content(size_kb):
    """Generate random content of specified size in KB."""
    chars = string.ascii_letters + string.digits
    return ''.join(random.choice(chars) for _ in range(size_kb * 1024))


def test_recursive_directory_structure_performance():
    """
    Test 1: Recursive Directory Structure Performance
    
    This test evaluates how the library performs when handling deeply nested
    directory structures with many files, simulating a complex project.
    """
    # Create a temporary directory for this test
    temp_dir = tempfile.mkdtemp()
    try:
        # Parameters for the test
        max_depth = 8  # Maximum directory nesting depth
        dirs_per_level = 3  # Number of directories to create at each level
        files_per_dir = 5  # Number of files to create in each directory
        
        # Function to recursively create directories and files
        def create_recursive_structure(base_path, current_depth=0):
            # Create files in the current directory
            for i in range(files_per_dir):
                file_path = os.path.join(base_path, f"file_{i}.txt")
                content = f"Content for file {i} at depth {current_depth}"
                create_file(file_path, content)
            
            # If we haven't reached the maximum depth, create subdirectories
            if current_depth < max_depth:
                for i in range(dirs_per_level):
                    dir_path = os.path.join(base_path, f"dir_{i}_depth_{current_depth}")
                    os.makedirs(dir_path, exist_ok=True)
                    create_recursive_structure(dir_path, current_depth + 1)
        
        # Measure creation time
        start_time = time.time()
        with TransactionManager():
            create_recursive_structure(temp_dir)
        creation_time = time.time() - start_time
        print(f"Recursive structure creation time: {creation_time:.2f} seconds")
        
        # Calculate expected number of directories and files
        total_dirs = sum(dirs_per_level ** i for i in range(1, max_depth + 1))
        total_files = files_per_dir * (1 + total_dirs)
        print(f"Created approximately {total_dirs} directories and {total_files} files")
        
        # Test listing performance
        list_tool = ListFilesTool()
        
        # Measure time to list all directories recursively
        start_time = time.time()
        all_entries = []
        
        def list_recursive(dir_path):
            result = list_tool.execute(directory=dir_path)
            assert result["status"] == "success"
            entries = result["observation"].split("\n")
            
            for entry in entries:
                if entry.endswith("/"):  # It's a directory
                    subdir_path = os.path.join(dir_path, entry.rstrip("/"))
                    list_recursive(subdir_path)
                else:
                    all_entries.append(os.path.join(dir_path, entry))
        
        list_recursive(temp_dir)
        listing_time = time.time() - start_time
        print(f"Recursive listing time: {listing_time:.2f} seconds")
        print(f"Found {len(all_entries)} files")
        
        # Test reading performance
        read_tool = ReadFileTool()
        
        # Sample a subset of files to read (to keep test duration reasonable)
        sample_size = min(100, len(all_entries))
        sample_files = random.sample(all_entries, sample_size)
        
        # Measure time to read sampled files
        start_time = time.time()
        for file_path in sample_files:
            result = read_tool.execute(path=file_path)
            assert result["status"] == "success"
            assert "Content for file" in result["observation"]
        reading_time = time.time() - start_time
        print(f"Reading {sample_size} files time: {reading_time:.2f} seconds")
        
        # Performance assertions
        assert creation_time < 60, f"Structure creation took too long: {creation_time:.2f} seconds"
        assert listing_time < 60, f"Recursive listing took too long: {listing_time:.2f} seconds"
        assert reading_time < 30, f"Reading files took too long: {reading_time:.2f} seconds"
    
    finally:
        # Clean up
        shutil.rmtree(temp_dir)


def test_concurrent_mixed_operations_performance():
    """
    Test 2: Concurrent Mixed Operations Performance
    
    This test evaluates how the library performs when multiple operations
    (create, read, modify, delete) are performed concurrently across different modules.
    """
    # Create a temporary directory for this test
    temp_dir = tempfile.mkdtemp()
    try:
        # Create initial structure
        os.makedirs(os.path.join(temp_dir, "src"), exist_ok=True)
        os.makedirs(os.path.join(temp_dir, "data"), exist_ok=True)
        os.makedirs(os.path.join(temp_dir, "config"), exist_ok=True)
        
        # Create some initial files
        create_file(os.path.join(temp_dir, "src", "main.py"), """
def main():
    print("Hello, world!")
    return True
""")
        
        create_file(os.path.join(temp_dir, "config", "settings.json"), json.dumps({
            "version": "1.0.0",
            "debug": False,
            "max_connections": 100
        }, indent=2))
        
        # Generate some data files
        for i in range(10):
            create_file(
                os.path.join(temp_dir, "data", f"data_{i}.txt"),
                generate_random_content(10)  # 10KB files
            )
        
        # Define different types of operations
        def create_operation(thread_id):
            """Create new files."""
            for i in range(5):
                file_path = os.path.join(temp_dir, "src", f"module_{thread_id}_{i}.py")
                content = f"""
# Module created by thread {thread_id}
def function_{i}():
    return {i * thread_id}
"""
                create_file(file_path, content)
        
        def read_operation(thread_id):
            """Read existing files."""
            # Read data files
            for i in range(10):
                file_path = os.path.join(temp_dir, "data", f"data_{i}.txt")
                if path_exists(file_path):
                    content = read_file(file_path)
                    assert len(content) > 0
            
            # Read config
            config_path = os.path.join(temp_dir, "config", "settings.json")
            if path_exists(config_path):
                content = read_file(config_path)
                config = json.loads(content)
                assert "version" in config
        
        def graph_operation(thread_id):
            """Generate call graphs for Python files."""
            graph_tool = GenerateCallGraphTool()
            
            # Find Python files
            src_dir = os.path.join(temp_dir, "src")
            py_files = [
                os.path.join(src_dir, f) for f in os.listdir(src_dir)
                if f.endswith(".py") and path_exists(os.path.join(src_dir, f))
            ]
            
            # Generate call graphs for a sample of files
            sample_size = min(3, len(py_files))
            if sample_size > 0:
                sample_files = random.sample(py_files, sample_size)
                for file_path in sample_files:
                    result = graph_tool.execute(path=file_path)
                    assert result["status"] == "success"
        
        def modify_operation(thread_id):
            """Modify existing files."""
            # Modify config
            config_path = os.path.join(temp_dir, "config", "settings.json")
            if path_exists(config_path):
                content = read_file(config_path)
                config = json.loads(content)
                config["modified_by"] = f"thread_{thread_id}"
                config["timestamp"] = time.time()
                create_file(config_path, json.dumps(config, indent=2))
            
            # Modify a random data file
            data_files = [
                os.path.join(temp_dir, "data", f) for f in os.listdir(os.path.join(temp_dir, "data"))
                if path_exists(os.path.join(temp_dir, "data", f))
            ]
            if data_files:
                file_path = random.choice(data_files)
                content = read_file(file_path)
                create_file(file_path, content + f"\nModified by thread {thread_id}")
        
        # List of operations to perform
        operations = [
            create_operation,
            read_operation,
            graph_operation,
            modify_operation
        ]
        
        # Number of threads to use
        num_threads = 8
        
        # Function to be executed by each thread
        def worker(thread_id):
            # Each thread performs a random mix of operations
            for _ in range(5):  # Each thread performs 5 operations
                operation = random.choice(operations)
                operation(thread_id)
        
        # Execute operations concurrently
        start_time = time.time()
        with concurrent.futures.ThreadPoolExecutor(max_workers=num_threads) as executor:
            futures = [executor.submit(worker, i) for i in range(num_threads)]
            # Wait for all threads to complete
            for future in concurrent.futures.as_completed(futures):
                try:
                    future.result()
                except Exception as e:
                    print(f"Thread error: {e}")
                    raise
        
        execution_time = time.time() - start_time
        print(f"Concurrent mixed operations execution time: {execution_time:.2f} seconds")
        
        # Verify the final state
        # Count files in each directory
        src_files = len(os.listdir(os.path.join(temp_dir, "src")))
        data_files = len(os.listdir(os.path.join(temp_dir, "data")))
        config_files = len(os.listdir(os.path.join(temp_dir, "config")))
        
        print(f"Final state: {src_files} source files, {data_files} data files, {config_files} config files")
        
        # Check if config was modified
        config_path = os.path.join(temp_dir, "config", "settings.json")
        config_content = read_file(config_path)
        config = json.loads(config_content)
        assert "modified_by" in config, "Config should have been modified"
        
        # Performance assertion
        assert execution_time < 60, f"Concurrent operations took too long: {execution_time:.2f} seconds"
    
    finally:
        # Clean up
        shutil.rmtree(temp_dir)


if __name__ == "__main__":
    pytest.main(["-xvs", __file__])
