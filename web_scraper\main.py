import requests
from bs4 import BeautifulSoup
import csv
import sys

def fetch_content(url):
    try:
        response = requests.get(url)
        response.raise_for_status()  # Raise an error for bad responses
        return response.text
    except requests.exceptions.RequestException as e:
        print(f"Error fetching the URL: {e}")
        sys.exit(1)

def extract_links(html_content):
    soup = BeautifulSoup(html_content, 'html.parser')
    links = [a['href'] for a in soup.find_all('a', href=True)]
    return links

def save_links_to_csv(links, filename):
    with open(filename, mode='w', newline='') as file:
        writer = csv.writer(file)
        writer.writerow(['Links'])
        for link in links:
            writer.writerow([link])

def main(url):
    html_content = fetch_content(url)
    links = extract_links(html_content)
    save_links_to_csv(links, 'links.csv')

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python main.py <URL>\")
        sys.exit(1)
    url = sys.argv[1]
    main(url)