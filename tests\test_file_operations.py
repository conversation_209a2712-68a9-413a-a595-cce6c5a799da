import os
import pytest
import os
import time
import shutil  # Added missing import
from mindlink.tools.file_tools import create_file, path_exists, SAFE_BASE_DIR, search_in_file

# Import TransactionManager directly to avoid circular import
from mindlink.tools.file_tools import TransactionManager

@pytest.fixture(autouse=True)
def temp_cwd(tmp_path, monkeypatch):
    """Run each file-ops test in its own temporary CWD within SAFE_BASE_DIR"""
    test_dir = os.path.join(str(SAFE_BASE_DIR), 'test_cwd')
    os.makedirs(test_dir, exist_ok=True)
    monkeypatch.chdir(test_dir)
    yield test_dir
    # Clean up after test
    shutil.rmtree(test_dir, ignore_errors=True)

def test_transaction_rollback():
    with pytest.raises(Exception):
        with TransactionManager() as tm:
            create_file('test.txt', 'data')
            raise Exception('Simulated failure')
    assert not path_exists('test.txt')

def test_transaction_commit():
    with TransactionManager() as tm:
        create_file('test.txt', 'data')
    assert path_exists('test.txt')

def test_search_in_file_performance():
    """Test performance of search_in_file with large files and different patterns"""
    # Create a large test file with 1 million lines
    test_file = os.path.join(str(SAFE_BASE_DIR), 'test_cwd', 'large_test.txt')
    
    # Generate 10MB of test data with repeating patterns
    pattern = 'performance_test_pattern_12345\n'
    with open(test_file, 'w', encoding='utf-8') as f:
        for i in range(100000):  # ~10MB file
            f.write(f"Line {i}: {pattern if i % 100 == 0 else 'random_data'}\n")
    
    # Test different search scenarios (converted regex patterns to plain strings)
    test_cases = [
        (pattern.strip(), 1000),  # Common pattern
        ('Line 12345', 1),        # Beginning pattern (removed ^)
        ('random_data', 99000),  # End pattern (removed $) and corrected expected count
        ('non_existent_pattern', 0) # Non-existent pattern
    ]
    
    # Run performance tests
    for search_str, expected_count in test_cases:
        start_time = time.time()
        results = search_in_file(test_file, search_str)
        duration = time.time() - start_time
        
        assert len(results) == expected_count
        print(f"Search '{search_str}' completed in {duration:.3f}s, found {len(results)} matches")
        
    # Clean up
    os.remove(test_file)