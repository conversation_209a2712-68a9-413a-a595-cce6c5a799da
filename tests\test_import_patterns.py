import pytest
import sys
import os

# Test different import patterns to ensure no circular imports

def test_direct_imports():
    """Test importing components directly."""
    # Import the TransactionManager directly
    from mindlink.tools.file_tools import TransactionManager
    assert TransactionManager is not None
    
    # Import file operation functions
    from mindlink.tools.file_tools import create_file, read_file, list_files, path_exists
    assert create_file is not None
    assert read_file is not None
    assert list_files is not None
    assert path_exists is not None
    
    # Import tool classes
    from mindlink.tools.file_tools import Create<PERSON><PERSON>Tool, ReadFileTool, ListFilesTool, PathExistsTool
    assert CreateFileTool is not None
    assert ReadFileTool is not None
    assert ListFilesTool is not None
    assert PathExistsTool is not None

def test_module_import():
    """Test importing the entire module."""
    import mindlink.tools.file_tools
    
    # Access components through the module
    assert mindlink.tools.file_tools.TransactionManager is not None
    assert mindlink.tools.file_tools.create_file is not None
    assert mindlink.tools.file_tools.read_file is not None
    assert mindlink.tools.file_tools.CreateFileTool is not None

def test_import_through_registry():
    """Test accessing tools through the tool registry."""
    from mindlink.tools.base import tool_registry
    
    # Check that file tools are registered
    assert 'create_file' in tool_registry
    assert 'read_file' in tool_registry
    assert 'list_files' in tool_registry
    assert 'path_exists' in tool_registry
    
    # Instantiate tools from registry
    create_tool = tool_registry['create_file']()
    read_tool = tool_registry['read_file']()
    
    assert create_tool is not None
    assert read_tool is not None

def test_import_from_init():
    """Test importing through the package __init__."""
    # Import the tools module
    import mindlink.tools
    
    # Access file tools through the module
    assert hasattr(mindlink.tools, 'CreateFileTool')
    assert hasattr(mindlink.tools, 'ReadFileTool')
    
    # Check that the tools are properly initialized
    assert mindlink.tools.CreateFileTool is not None
    assert mindlink.tools.ReadFileTool is not None
