import time
import uuid
import os
import shutil
from mindlink.logging_utils import (
    log_event,
    close_logs,
    create_user_input_log,
    create_agent_plan_generated_log,
    create_tool_call_start_log,
    create_tool_call_end_log,
    create_llm_query_log,
    create_llm_response_log,
    create_agent_goal_completed_log,
    create_error_occurred_log,
    PlanStep
)

def test_logging_performance():
    # Test parameters
    num_iterations = 1000  # Number of log events to generate
    session_id = str(uuid.uuid4())
    
    print(f"Starting performance test with {num_iterations} log events...")
    print(f"Session ID: {session_id}")
    print("-" * 50)
    
    # Test different log event types
    test_cases = [
        ("User Input", lambda: create_user_input_log(session_id, "Test user input", "test_intent")),
        ("Agent Plan", lambda: create_agent_plan_generated_log(
            session_id,
            [PlanStep(step_id="1", tool_name="test_tool", parameters={"param": "value"}, reasoning="test")],
            confidence=0.9
        )),
        ("Tool Start", lambda: create_tool_call_start_log(session_id, "test_tool", {"param": "value"})),
        ("Tool End", lambda: create_tool_call_end_log(
            session_id,
            "test_tool",
            {"param": "value"},
            {"result": "success"},
            "SUCCESS",
            100.5
        )),
        ("LLM Query", lambda: create_llm_query_log(session_id, "Test prompt", "gpt-4")),
        ("LLM Response", lambda: create_llm_response_log(
            session_id, "Test prompt", "Test response", "gpt-4", 150, 250.5
        )),
        ("Goal Completed", lambda: create_agent_goal_completed_log(session_id, {"status": "success"})),
        ("Error", lambda: create_error_occurred_log(
            session_id, "test_component", "Test error occurred", "ERROR", "Test stack trace"
        ))
    ]
    
    # Run performance test
    results = {}
    
    for name, log_func in test_cases:
        start_time = time.time()
        
        # Generate multiple log events
        for _ in range(num_iterations):
            event = log_func()
            log_event(event)
            
        end_time = time.time()
        total_time = end_time - start_time
        avg_time = (total_time / num_iterations) * 1000  # Convert to milliseconds
        
        results[name] = {
            "total_time": total_time,
            "avg_time_ms": avg_time,
            "events_per_second": num_iterations / total_time
        }
        
        print(f"{name}:")
        print(f"  Total time: {total_time:.4f} seconds")
        print(f"  Average time per log: {avg_time:.4f} ms")
        print(f"  Events per second: {results[name]['events_per_second']:.2f}")
        print("-" * 50)
    
    return results

def cleanup_old_logs():
    """Remove old log files from previous test runs."""
    logs_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'mindlink', 'logs')
    if os.path.exists(logs_dir):
        shutil.rmtree(logs_dir)

if __name__ == "__main__":
    # Clean up old logs before running the test
    cleanup_old_logs()
    
    try:
        test_logging_performance()
    finally:
        # Ensure logs are properly closed
        close_logs()
