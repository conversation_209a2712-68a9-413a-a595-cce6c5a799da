
#!/usr/bin/env python3
"""
Comprehensive Web Scraper Application

A full-featured web scraping framework with advanced capabilities including:
- Multi-threaded scraping
- Rate limiting and politeness
- Data processing and validation
- Multiple output formats
- Robust error handling
- Session management
- Proxy support
- Progress tracking

Author: MindLink Agent
Version: 1.0.0
"""

import asyncio
import csv
import json
import logging
import os
import random
import re
import sqlite3
import sys
import threading
import time
import urllib.parse
from concurrent.futures import ThreadPoolExecutor, as_completed
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Set, Tuple, Union, Any, Callable
from urllib.robotparser import RobotFileParser

try:
    import requests
    from bs4 import BeautifulSoup
    import pandas as pd
    from selenium import webdriver
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.common.exceptions import TimeoutException, NoSuchElementException
except ImportError as e:
    print(f"Missing required dependency: {e}")
    print("Please install: pip install requests beautifulsoup4 pandas selenium")
    sys.exit(1)


# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('web_scraper.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)


@dataclass
class ScrapingConfig:
    """Configuration class for web scraping parameters."""
    max_workers: int = 5
    delay_range: Tuple[float, float] = (1.0, 3.0)
    timeout: int = 30
    max_retries: int = 3
    retry_delay: float = 2.0
    respect_robots_txt: bool = True
    user_agents: List[str] = field(default_factory=lambda: [
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    ])
    headers: Dict[str, str] = field(default_factory=lambda: {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
    })


@dataclass
class ScrapedData:
    """Data class for storing scraped information."""
    url: str
    title: str = ""
    content: str = ""
    metadata: Dict[str, Any] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=datetime.now)
    status_code: int = 0
    error: Optional[str] = None

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            'url': self.url,
            'title': self.title,
            'content': self.content,
            'metadata': self.metadata,
            'timestamp': self.timestamp.isoformat(),
            'status_code': self.status_code,
            'error': self.error
        }


class RateLimiter:
    """Rate limiter to control request frequency."""
    
    def __init__(self, min_delay: float = 1.0, max_delay: float = 3.0):
        self.min_delay = min_delay
        self.max_delay = max_delay
        self.last_request_time = 0
        self.lock = threading.Lock()
    
    def wait(self):
        """Wait for appropriate delay before next request."""
        with self.lock:
            current_time = time.time()
            elapsed = current_time - self.last_request_time
            delay = random.uniform(self.min_delay, self.max_delay)
            
            if elapsed < delay:
                sleep_time = delay - elapsed
                time.sleep(sleep_time)
            
            self.last_request_time = time.time()


class SessionManager:
    """Manages HTTP sessions with cookie handling and user agent rotation."""
    
    def __init__(self, config: ScrapingConfig):
        self.config = config
        self.session = requests.Session()
        self.session.headers.update(config.headers)
        self._setup_session()
    
    def _setup_session(self):
        """Setup session with initial configuration."""
        self.rotate_user_agent()
        
        # Configure session timeouts and retries
        adapter = requests.adapters.HTTPAdapter(
            max_retries=requests.adapters.Retry(
                total=self.config.max_retries,
                backoff_factor=1,
                status_forcelist=[429, 500, 502, 503, 504]
            )
        )
        self.session.mount('http://', adapter)
        self.session.mount('https://', adapter)
    
    def rotate_user_agent(self):
        """Rotate user agent string."""
        user_agent = random.choice(self.config.user_agents)
        self.session.headers['User-Agent'] = user_agent
        logger.debug(f"Rotated user agent: {user_agent}")
    
    def get(self, url: str, **kwargs) -> requests.Response:
        """Make GET request with session."""
        kwargs.setdefault('timeout', self.config.timeout)
        return self.session.get(url, **kwargs)
    
    def close(self):
        """Close the session."""
        self.session.close()


class RobotsChecker:
    """Check robots.txt compliance."""
    
    def __init__(self):
        self.robots_cache: Dict[str, RobotFileParser] = {}
        self.cache_lock = threading.Lock()
    
    def can_fetch(self, url: str, user_agent: str = '*') -> bool:
        """Check if URL can be fetched according to robots.txt."""
        try:
            parsed_url = urllib.parse.urlparse(url)
            base_url = f"{parsed_url.scheme}://{parsed_url.netloc}"
            robots_url = urllib.parse.urljoin(base_url, '/robots.txt')
            
            with self.cache_lock:
                if robots_url not in self.robots_cache:
                    rp = RobotFileParser()
                    rp.set_url(robots_url)
                    try:
                        rp.read()
                        self.robots_cache[robots_url] = rp
                    except Exception as e:
                        logger.warning(f"Could not read robots.txt for {base_url}: {e}")
                        return True  # Allow if robots.txt is not accessible
                
                rp = self.robots_cache[robots_url]
                return rp.can_fetch(user_agent, url)
        
        except Exception as e:
            logger.warning(f"Error checking robots.txt for {url}: {e}")
            return True  # Allow if there's an error


class DataProcessor:
    """Process and clean scraped data."""
    
    @staticmethod
    def clean_text(text: str) -> str:
        """Clean and normalize text content."""
        if not text:
            return ""
        
        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text)
        
        # Remove special characters but keep basic punctuation
        text = re.sub(r'[^\w\s.,!?;:()-]', '', text)
        
        return text.strip()
    
    @staticmethod
    def extract_emails(text: str) -> List[str]:
        """Extract email addresses from text."""
        email_pattern = r'[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}'
        return re.findall(email_pattern, text)
    
    @staticmethod
    def extract_phone_numbers(text: str) -> List[str]:
        """Extract phone numbers from text."""
        phone_patterns = [
            r'\d{3}-\d{3}-\d{4}',  # ************
            r'\(\d{3}\)\s*\d{3}-\d{4}',  # (*************
            r'\d{3}\.\d{3}\.\d{4}',  # ************
            r'\d{10}'  # 1234567890
        ]
        
        phone_numbers = []
        for pattern in phone_patterns:
            phone_numbers.extend(re.findall(pattern, text))
        
        return phone_numbers
    
    @staticmethod
    def extract_urls(text: str) -> List[str]:
        """Extract URLs from text."""
        url_pattern = r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\(\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+'
        return re.findall(url_pattern, text)
    
    def process_data(self, data: ScrapedData) -> ScrapedData:
        """Process and enhance scraped data."""
        # Clean text content
        data.content = self.clean_text(data.content)
        data.title = self.clean_text(data.title)
        
        # Extract structured information
        data.metadata['emails'] = self.extract_emails(data.content)
        data.metadata['phone_numbers'] = self.extract_phone_numbers(data.content)
        data.metadata['urls'] = self.extract_urls(data.content)
        data.metadata['word_count'] = len(data.content.split())
        data.metadata['char_count'] = len(data.content)
        
        return data


class DataStorage:
    """Handle data storage in multiple formats."""
    
    def __init__(self, output_dir: str = "scraped_data"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        self.db_path = self.output_dir / "scraped_data.db"
        self._init_database()
    
    def _init_database(self):
        """Initialize SQLite database."""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS scraped_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    url TEXT UNIQUE NOT NULL,
                    title TEXT,
                    content TEXT,
                    metadata TEXT,
                    timestamp TEXT,
                    status_code INTEGER,
                    error TEXT
                )
            """)
            conn.commit()
    
    def save_to_json(self, data: List[ScrapedData], filename: str = None):
        """Save data to JSON file."""
        if filename is None:
            filename = f"scraped_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        filepath = self.output_dir / filename
        
        json_data = [item.to_dict() for item in data]
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(json_data, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Data saved to JSON: {filepath}")
    
    def save_to_csv(self, data: List[ScrapedData], filename: str = None):
        """Save data to CSV file."""
        if filename is None:
            filename = f"scraped_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        
        filepath = self.output_dir / filename
        
        # Flatten data for CSV
        csv_data = []
        for item in data:
            row = {
                'url': item.url,
                'title': item.title,
                'content': item.content[:1000],  # Truncate for CSV
                'timestamp': item.timestamp.isoformat(),
                'status_code': item.status_code,
                'error': item.error,
                'word_count': item.metadata.get('word_count', 0),
                'char_count': item.metadata.get('char_count', 0)
            }
            csv_data.append(row)
        
        df = pd.DataFrame(csv_data)
        df.to_csv(filepath, index=False, encoding='utf-8')
        
        logger.info(f"Data saved to CSV: {filepath}")
    
    def save_to_database(self, data: List[ScrapedData]):
        """Save data to SQLite database."""
        with sqlite3.connect(self.db_path) as conn:
            for item in data:
                try:
                    conn.execute("""
                        INSERT OR REPLACE INTO scraped_data 
                        (url, title, content, metadata, timestamp, status_code, error)
                        VALUES (?, ?, ?, ?, ?, ?, ?)
                    """, (
                        item.url,
                        item.title,
                        item.content,
                        json.dumps(item.metadata),
                        item.timestamp.isoformat(),
                        item.status_code,
                        item.error
                    ))
                except sqlite3.Error as e:
                    logger.error(f"Database error for {item.url}: {e}")
            
            conn.commit()
        
        logger.info(f"Data saved to database: {self.db_path}")


class ProgressTracker:
    """Track scraping progress and performance metrics."""
    
    def __init__(self):
        self.start_time = time.time()
        self.completed = 0
        self.failed = 0
        self.total = 0
        self.lock = threading.Lock()
    
    def set_total(self, total: int):
        """Set total number of URLs to scrape."""
        self.total = total
    
    def update(self, success: bool = True):
        """Update progress counters."""
        with self.lock:
            if success:
                self.completed += 1
            else:
                self.failed += 1
    
    def get_stats(self) -> Dict[str, Any]:
        """Get current progress statistics."""
        with self.lock:
            elapsed = time.time() - self.start_time
            processed = self.completed + self.failed
            
            return {
                'total': self.total,
                'completed': self.completed,
                'failed': self.failed,
                'processed': processed,
                'remaining': self.total - processed,
                'success_rate': (self.completed / processed * 100) if processed > 0 else 0,
                'elapsed_time': elapsed,
                'avg_time_per_url': elapsed / processed if processed > 0 else 0,
                'estimated_remaining': (elapsed / processed * (self.total - processed)) if processed > 0 else 0
            }
    
    def print_progress(self):
        """Print current progress to console."""
        stats = self.get_stats()
        
        print(f"
Progress: {stats['processed']}/{stats['total']} "
              f"({stats['success_rate']:.1f}% success) "
              f"[{stats['elapsed_time']:.1f}s elapsed, "
              f"{stats['estimated_remaining']:.1f}s remaining]", end="")


class WebScraper:
    """Main web scraper class with comprehensive functionality."""
    
    def __init__(self, config: ScrapingConfig = None):
        self.config = config or ScrapingConfig()
        self.session_manager = SessionManager(self.config)
        self.rate_limiter = RateLimiter(self.config.delay_range[0], self.config.delay_range[1])
        self.robots_checker = RobotsChecker() if self.config.respect_robots_txt else None
        self.data_processor = DataProcessor()
        self.storage = DataStorage()
        self.progress_tracker = ProgressTracker()
        self.scraped_urls: Set[str] = set()
        self.results: List[ScrapedData] = []
        self.lock = threading.Lock()
    
    def _is_valid_url(self, url: str) -> bool:
        """Validate URL format."""
        try:
            result = urllib.parse.urlparse(url)
            return all([result.scheme, result.netloc])
        except Exception:
            return False
    
    def _can_scrape_url(self, url: str) -> bool:
        """Check if URL can be scraped based on robots.txt."""
        if not self.robots_checker:
            return True
        
        user_agent = self.session_manager.session.headers.get('User-Agent', '*')
        return self.robots_checker.can_fetch(url, user_agent)
    
    def _extract_content(self, soup: BeautifulSoup, url: str) -> ScrapedData:
        """Extract content from BeautifulSoup object."""
        data = ScrapedData(url=url)
        
        try:
            # Extract title
            title_tag = soup.find('title')
            data.title = title_tag.get_text().strip() if title_tag else ""
            
            # Extract main content
            content_selectors = [
                'main', 'article', '.content', '#content',
                '.post-content', '.entry-content', '.article-content'
            ]
            
            content_text = ""
            for selector in content_selectors:
                content_elem = soup.select_one(selector)
                if content_elem:
                    content_text = content_elem.get_text(separator=' ', strip=True)
                    break
            
            # Fallback to body if no specific content found
            if not content_text:
                body = soup.find('body')
                if body:
                    content_text = body.get_text(separator=' ', strip=True)
            
            data.content = content_text
            
            # Extract metadata
            data.metadata = {
                'description': self._get_meta_content(soup, 'description'),
                'keywords': self._get_meta_content(soup, 'keywords'),
                'author': self._get_meta_content(soup, 'author'),
                'language': soup.get('lang', ''),
                'links_count': len(soup.find_all('a')),
                'images_count': len(soup.find_all('img')),
                'headings': [h.get_text().strip() for h in soup.find_all(['h1', 'h2', 'h3'])[:10]]
            }
            
        except Exception as e:
            logger.error(f"Error extracting content from {url}: {e}")
            data.error = str(e)
        
        return data
    
    def _get_meta_content(self, soup: BeautifulSoup, name: str) -> str:
        """Extract meta tag content."""
        meta_tag = soup.find('meta', attrs={'name': name}) or soup.find('meta', attrs={'property': f'og:{name}'})
        return meta_tag.get('content', '') if meta_tag else ''
    
    def scrape_url(self, url: str) -> Optional[ScrapedData]:
        """Scrape a single URL."""
        if not self._is_valid_url(url):
            logger.warning(f"Invalid URL: {url}")
            return None
        
        if url in self.scraped_urls:
            logger.info(f"URL already scraped: {url}")
            return None
        
        if not self._can_scrape_url(url):
            logger.warning(f"Robots.txt disallows scraping: {url}")
            return None
        
        # Rate limiting
        self.rate_limiter.wait()
        
        try:
            # Rotate user agent occasionally
            if random.random() < 0.1:  # 10% chance
                self.session_manager.rotate_user_agent()
            
            logger.info(f"Scraping: {url}")
            response = self.session_manager.get(url)
            response.raise_for_status()
            
            # Parse HTML
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Extract data
            data = self._extract_content(soup, url)
            data.status_code = response.status_code
            
            # Process data
            data = self.data_processor.process_data(data)
            
            # Store URL as scraped
            with self.lock:
                self.scraped_urls.add(url)
                self.results.append(data)
            
            self.progress_tracker.update(success=True)
            logger.info(f"Successfully scraped: {url}")
            
            return data
            
        except requests.RequestException as e:
            logger.error(f"Request error for {url}: {e}")
            error_data = ScrapedData(url=url, error=str(e))
            self.progress_tracker.update(success=False)
            return error_data
        
        except Exception as e:
            logger.error(f"Unexpected error scraping {url}: {e}")
            error_data = ScrapedData(url=url, error=str(e))
            self.progress_tracker.update(success=False)
            return error_data
    
    def scrape_urls(self, urls: List[str], max_workers: int = None) -> List[ScrapedData]:
        """Scrape multiple URLs concurrently."""
        max_workers = max_workers or self.config.max_workers
        self.progress_tracker.set_total(len(urls))
        
        logger.info(f"Starting to scrape {len(urls)} URLs with {max_workers} workers")
        
        results = []
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit all tasks
            future_to_url = {executor.submit(self.scrape_url, url): url for url in urls}
            
            # Process completed tasks
            for future in as_completed(future_to_url):
                url = future_to_url[future]
                try:
                    result = future.result()
                    if result:
                        results.append(result)
                    
                    # Print progress
                    self.progress_tracker.print_progress()
                    
                except Exception as e:
                    logger.error(f"Error processing {url}: {e}")
                    self.progress_tracker.update(success=False)
        
        print()  # New line after progress
        logger.info(f"Scraping completed. {len(results)} URLs processed.")
        
        return results
    
    def save_results(self, formats: List[str] = None):
        """Save scraping results in specified formats."""
        if not self.results:
            logger.warning("No results to save")
            return
        
        formats = formats or ['json', 'csv', 'database']
        
        for format_type in formats:
            try:
                if format_type == 'json':
                    self.storage.save_to_json(self.results)
                elif format_type == 'csv':
                    self.storage.save_to_csv(self.results)
                elif format_type == 'database':
                    self.storage.save_to_database(self.results)
                else:
                    logger.warning(f"Unknown format: {format_type}")
            except Exception as e:
                logger.error(f"Error saving to {format_type}: {e}")
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get scraping statistics."""
        stats = self.progress_tracker.get_stats()
        
        if self.results:
            content_lengths = [len(r.content) for r in self.results if r.content]
            stats.update({
                'avg_content_length': sum(content_lengths) / len(content_lengths) if content_lengths else 0,
                'total_content_chars': sum(content_lengths),
                'unique_domains': len(set(urllib.parse.urlparse(r.url).netloc for r in self.results))
            })
        
        return stats
    
    def close(self):
        """Clean up resources."""
        self.session_manager.close()


class ScrapingScheduler:
    """Advanced scheduler for managing scraping tasks."""
    
    def __init__(self, scraper: WebScraper):
        self.scraper = scraper
        self.task_queue: List[Dict[str, Any]] = []
        self.completed_tasks: List[Dict[str, Any]] = []
        self.is_running = False
    
    def add_task(self, urls: List[str], priority: int = 1, metadata: Dict[str, Any] = None):
        """Add a scraping task to the queue."""
        task = {
            'id': len(self.task_queue) + 1,
            'urls': urls,
            'priority': priority,
            'metadata': metadata or {},
            'created_at': datetime.now(),
            'status': 'pending'
        }
        self.task_queue.append(task)
        
        # Sort by priority (higher priority first)
        self.task_queue.sort(key=lambda x: x['priority'], reverse=True)
        
        logger.info(f"Added task {task['id']} with {len(urls)} URLs (priority: {priority})")
    
    def run_scheduler(self):
        """Run the task scheduler."""
        self.is_running = True
        logger.info("Starting task scheduler")
        
        while self.is_running and self.task_queue:
            task = self.task_queue.pop(0)
            task['status'] = 'running'
            task['started_at'] = datetime.now()
            
            logger.info(f"Starting task {task['id']} with {len(task['urls'])} URLs")
            
            try:
                results = self.scraper.scrape_urls(task['urls'])
                task['results'] = results
                task['status'] = 'completed'
                task['completed_at'] = datetime.now()
                
                logger.info(f"Completed task {task['id']} - {len(results)} URLs scraped")
                
            except Exception as e:
                task['status'] = 'failed'
                task['error'] = str(e)
                task['failed_at'] = datetime.now()
                logger.error(f"Task {task['id']} failed: {e}")
            
            self.completed_tasks.append(task)
        
        self.is_running = False
        logger.info("Task scheduler stopped")
    
    def stop_scheduler(self):
        """Stop the task scheduler."""
        self.is_running = False
        logger.info("Stopping task scheduler")
    
    def get_status(self) -> Dict[str, Any]:
        """Get scheduler status."""
        return {
            'is_running': self.is_running,
            'pending_tasks': len(self.task_queue),
            'completed_tasks': len(self.completed_tasks),
            'total_tasks': len(self.task_queue) + len(self.completed_tasks)
        }


def create_sample_urls() -> List[str]:
    """Create sample URLs for testing."""
    return [
        'https://httpbin.org/html',
        'https://httpbin.org/json',
        'https://example.com',
        'https://httpbin.org/robots.txt',
        'https://httpbin.org/status/200'
    ]


def main():
    """Main function demonstrating the web scraper usage."""
    print("Comprehensive Web Scraper - Demo")
    print("=" * 50)
    
    # Configure scraping
    config = ScrapingConfig(
        max_workers=3,
        delay_range=(1.0, 2.0),
        timeout=15,
        max_retries=2
    )
    
    # Create scraper
    scraper = WebScraper(config)
    
    try:
        # Get sample URLs
        urls = create_sample_urls()
        print(f"Scraping {len(urls)} sample URLs...")
        
        # Scrape URLs
        results = scraper.scrape_urls(urls)
        
        # Print statistics
        stats = scraper.get_statistics()
        print(f"
Scraping Statistics:")
        print(f"- Total URLs processed: {stats['processed']}")
        print(f"- Successful: {stats['completed']}")
        print(f"- Failed: {stats['failed']}")
        print(f"- Success rate: {stats['success_rate']:.1f}%")
        print(f"- Total time: {stats['elapsed_time']:.1f}s")
        print(f"- Average time per URL: {stats['avg_time_per_url']:.1f}s")
        
        if 'avg_content_length' in stats:
            print(f"- Average content length: {stats['avg_content_length']:.0f} chars")
            print(f"- Total content: {stats['total_content_chars']:,} chars")
        
        # Save results
        print("
Saving results...")
        scraper.save_results(['json', 'csv', 'database'])
        
        # Display sample results
        print("
Sample Results:")
        for i, result in enumerate(results[:3]):
            print(f"
{i+1}. {result.url}")
            print(f"   Title: {result.title[:100]}..." if len(result.title) > 100 else f"   Title: {result.title}")
            print(f"   Content length: {len(result.content)} chars")
            print(f"   Status: {result.status_code}")
            if result.error:
                print(f"   Error: {result.error}")
    
    except KeyboardInterrupt:
        print("
Scraping interrupted by user")
    
    except Exception as e:
        logger.error(f"Error in main: {e}")
    
    finally:
        # Clean up
        scraper.close()
        print("
Scraper closed. Goodbye!")


if __name__ == "__main__":
    main()
