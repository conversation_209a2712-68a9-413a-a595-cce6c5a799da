import os
# Quick test for code capabilities performance and correctness
os.environ["OPENROUTER_API_KEY"] = "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"

from agent_capability_benchmark import initialize_agent, test_code_capabilities, BenchmarkScore

agent = initialize_agent()
scores = BenchmarkScore()
test_code_capabilities(agent, scores)

print("Code Capabilities Test:")
print(" Analysis Score:", scores.code_analysis)
print(" Generation Score:", scores.code_generation)
print(" Analysis Time:", scores.execution_times.get("code_analysis"), "seconds")
print(" Generation Time:", scores.execution_times.get("code_generation"), "seconds") 