"""
Tool implementations for MindLink Agent Core.
"""

from .base import Tool, tool_registry, FinishTool

# Register all tools
__all__ = [
    'Tool',
    'tool_registry',
    'FinishTool',
]

# Import tools at the end to avoid circular imports
from .file_tools import C<PERSON>FileTool, ReadFileTool, ListFilesTool
from .shell_tools import RunS<PERSON><PERSON><PERSON>mandTool
from .ast_tools import InsertASTNodeTool
from .semantic_tools import SemanticSuggestTool
from .sandbox_tools import RunC<PERSON>Tool, SnapshotTool
from .graph_tools import GenerateCallGraphTool
from .utility_tools import EchoTool

# Import HoverDocTool from both modules with aliases to avoid name conflicts
from .knowledge_tools import <PERSON>rateGraphTool
from .knowledge_tools import <PERSON><PERSON><PERSON><PERSON>Tool as KnowledgeHoverDocTool
from .doc_tools import <PERSON>verDocTool as DocHoverDocTool

# Use the DocHoverDocTool as the default HoverDocTool
HoverDocTool = DocHoverDocTool

# Import and register our new batch execution tool for high-performance workflows
from .batch_execute_tool import Batch<PERSON>xecuteTool

# Add imported tools to __all__
__all__.extend([
    'CreateFileTool',
    'ReadFileTool',
    'ListFilesTool',
    'RunShellCommandTool',
    'InsertASTNodeTool',
    'SemanticSuggestTool',
    'RunCodeTool',
    'SnapshotTool',
    'GenerateGraphTool',
    'HoverDocTool',
    'GenerateCallGraphTool',
    'KnowledgeHoverDocTool',
    'DocHoverDocTool',
    'EchoTool',
    'BatchExecuteTool',
])
