import pytest
from mindlink.tools.sandbox_tools import Run<PERSON>odeTool


def test_run_code_success():
    tool = RunCodeTool()
    # Simple print should succeed
    code = "print('hello world')"
    result = tool.execute(code=code, timeout=5)
    assert result['status'] == 'success'
    assert 'hello world' in result['observation']


def test_run_code_error_nonzero_exit():
    tool = RunCodeTool()
    # Exit with non-zero code should be treated as error
    code = "import sys; sys.exit(2)"
    result = tool.execute(code=code, timeout=5)
    assert result['status'] == 'error'
    assert 'Non-zero exit' in result.get('error', '') or result.get('error')


def test_run_code_exception():
    tool = RunCodeTool()
    # Code that raises an exception
    code = "raise ValueError('boom')"
    result = tool.execute(code=code, timeout=5)
    assert result['status'] == 'error'
    # The stderr should mention ValueError
    assert 'ValueError' in result['observation'] or 'ValueError' in result.get('error', '') 