"""
Example usage of MindLink Agent Core.
"""

import os

# Optional dotenv import
try:
    from dotenv import load_dotenv
    has_dotenv = True
except ImportError:
    has_dotenv = False

from mindlink.agent import AgentOS
from mindlink.models.openrouter import OpenRouterModel
from mindlink.config import DEFAULT_SYSTEM_PROMPT
import asyncio # Added import


async def main(): # Changed to async def
    """
    Example usage of MindLink Agent Core.
    """
    # Load environment variables from .env file if dotenv is available
    if has_dotenv:
        load_dotenv()

    # Create LLM using OpenRouter (no API key needed as it's hardcoded in the model)
    llm = OpenRouterModel(
        model_name="deepseek-r1",  # Options: "deepseek-r1", "glm-z1-32b"
        temperature=0.7,
        max_tokens=1024,
        top_p=0.9
    )

    # Create Agent OS
    agent = AgentOS(
        llm=llm,
        system_prompt_template=DEFAULT_SYSTEM_PROMPT,
        max_steps=15
    )

    # Define a goal
    goal = "Create a file called hello.txt with the content 'Hello, MindLink!'"

    print(f"Goal: {goal}")
    print("\nRunning agent...")

    # Run the agent
    success, result, history = await agent.run(goal) # Added await

    # Print result
    print("\n" + "="*50)
    print("Result:", "Success" if success else "Incomplete")
    print(result)
    print("="*50)

    # Print history summary
    print("\nAgent steps:")
    for i, entry in enumerate(history):
        print(f"{i+1}. {entry['request'].action.tool_name}")


if __name__ == "__main__":
    asyncio.run(main()) # Changed to asyncio.run
