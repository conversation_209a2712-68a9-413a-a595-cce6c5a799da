"""
Simple Performance Test for MindLink Agent Core.

This test evaluates the core performance of the library's file operations and tools.
"""

import os
import time
import tempfile
import shutil
import random
import string
from contextlib import contextmanager

# Import core components
from mindlink.tools.file_tools import (
    TransactionManager,
    create_file,
    read_file,
    list_files,
    path_exists,
    CreateFileTool,
    ReadFileTool,
    ListFilesTool
)
from mindlink.schemas.mindlink import MindLinkRequest, Action
from mindlink.executor import ToolExecutor
from mindlink.tools.file_tools import SAFE_BASE_DIR


@contextmanager
def measure_time(operation_name):
    """Simple context manager to measure execution time."""
    start_time = time.time()
    yield
    end_time = time.time()
    duration = end_time - start_time
    print(f"{operation_name}: {duration:.4f} seconds")
    return duration


def generate_random_content(size_kb):
    """Generate random content of specified size in KB."""
    chars = string.ascii_letters + string.digits
    return ''.join(random.choice(chars) for _ in range(size_kb * 1024))


def test_file_operations_performance():
    """Test the performance of basic file operations."""
    # Create a temporary directory within SAFE_BASE_DIR
    temp_dir = os.path.join(str(SAFE_BASE_DIR), 'test_performance')
    os.makedirs(temp_dir, exist_ok=True)
    try:
        print("\n=== Testing File Operations Performance ===")
        
        # Test parameters
        file_sizes = [1, 10, 100]  # KB
        iterations = 10
        
        # Test create_file performance
        for size in file_sizes:
            content = generate_random_content(size)
            with measure_time(f"Creating {iterations} files of {size}KB"):
                for i in range(iterations):
                    file_path = os.path.join(temp_dir, f"create_test_{size}kb_{i}.txt")
                    create_file(file_path, content)
        
        # Test read_file performance
        for size in file_sizes:
            with measure_time(f"Reading {iterations} files of {size}KB"):
                for i in range(iterations):
                    file_path = os.path.join(temp_dir, f"create_test_{size}kb_{i}.txt")
                    content = read_file(file_path)
                    assert len(content) > 0
        
        # Test list_files performance
        with measure_time(f"Listing directory with {iterations * len(file_sizes)} files"):
            files = list_files(temp_dir)
            assert len(files) == iterations * len(file_sizes)
        
        # Test transaction performance
        with measure_time("Transaction with 10 file operations"):
            with TransactionManager():
                for i in range(10):
                    file_path = os.path.join(temp_dir, f"transaction_test_{i}.txt")
                    create_file(file_path, f"Transaction test {i}")
                    content = read_file(file_path)
                    assert content == f"Transaction test {i}"

    finally:
        # Clean up
        shutil.rmtree(temp_dir)


def test_tool_performance():
    """Test the performance of tools."""
    # Create a temporary directory
    temp_dir = tempfile.mkdtemp()
    try:
        print("\n=== Testing Tool Performance ===")
        
        # Create tool instances
        create_tool = CreateFileTool()
        read_tool = ReadFileTool()
        list_tool = ListFilesTool()
        
        # Test CreateFileTool performance
        iterations = 10
        with measure_time(f"CreateFileTool.execute ({iterations} iterations)"):
            for i in range(iterations):
                result = create_tool.execute(
                    path=os.path.join(temp_dir, f"tool_test_{i}.txt"),
                    content=f"Tool test {i}"
                )
                assert result["status"] == "success"
        
        # Test ReadFileTool performance
        with measure_time(f"ReadFileTool.execute ({iterations} iterations)"):
            for i in range(iterations):
                result = read_tool.execute(
                    path=os.path.join(temp_dir, f"tool_test_{i}.txt")
                )
                assert result["status"] == "success"
                assert result["observation"] == f"Tool test {i}"
        
        # Test ListFilesTool performance
        with measure_time(f"ListFilesTool.execute ({iterations} iterations)"):
            for i in range(iterations):
                result = list_tool.execute(
                    directory=temp_dir
                )
                assert result["status"] == "success"
                assert len(result["observation"]) > 0
        
        # Test ToolExecutor performance
        executor = ToolExecutor()
        with measure_time(f"ToolExecutor.execute ({iterations} iterations)"):
            for i in range(iterations):
                request = MindLinkRequest(
                    action=Action(
                        tool_name="create_file",
                        parameters={
                            "path": os.path.join(temp_dir, f"executor_test_{i}.txt"),
                            "content": f"Executor test {i}"
                        }
                    ),
                    reasoning="Testing executor performance"
                )
                response = executor.execute(request)
                assert response.status == "success"
    
    finally:
        # Clean up
        shutil.rmtree(temp_dir)


def run_all_tests():
    """Run all performance tests."""
    print("\n=== Running Simple Performance Tests ===")
    
    start_time = time.time()
    
    test_file_operations_performance()
    test_tool_performance()
    
    end_time = time.time()
    total_time = end_time - start_time
    
    print(f"\n=== All tests completed in {total_time:.2f} seconds ===")


if __name__ == "__main__":
    run_all_tests()
