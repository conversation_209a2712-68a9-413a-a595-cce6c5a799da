[
{
  "event_id": "150e5429-fb06-4e09-887d-f438736b3d8a",
  "timestamp": "2025-06-02T20:46:24.659890",
  "session_id": "2bf65e5e-68df-4782-864d-360f5882e64f",
  "event_type": "user_input",
  "user_input": {
    "text": "Create 5 Python files, each containing 300 lines of functional code.\n\n",
    "intent": "agent_goal"
  }
},

{
  "event_id": "4fe9680a-bb6f-4836-98a1-61c80b238c23",
  "timestamp": "2025-06-02T20:46:29.421671",
  "session_id": "2bf65e5e-68df-4782-864d-360f5882e64f",
  "event_type": "llm_query",
  "llm_query": {
    "model": "Mistral Small 3.1 (OpenRouter)",
    "prompt_length": 210,
    "temperature": null,
    "max_tokens": null,
    "has_system_prompt": false,
    "tools_available": 0
  }
},

{
  "event_id": "0a83101c-e161-4236-8a82-ff4ea1aba993",
  "timestamp": "2025-06-02T20:46:36.822070",
  "session_id": "2bf65e5e-68df-4782-864d-360f5882e64f",
  "event_type": "llm_response",
  "llm_response": {
    "model": "Mistral Small 3.1 (OpenRouter)",
    "response_length": 0,
    "prompt_tokens": null,
    "completion_tokens": null,
    "total_tokens": null,
    "finish_reason": null,
    "latency_ms": 7390.0
  },
  "metadata": {
    "error": "All LLM providers failed. Last error: 429 Client Error: Too Many Requests for url: https://openrouter.ai/api/v1/chat/completions"
  }
},

{
  "event_id": "fb7fc9b3-4687-473f-8f7c-e9f92f9e556c",
  "timestamp": "2025-06-02T20:46:36.824458",
  "session_id": "2bf65e5e-68df-4782-864d-360f5882e64f",
  "event_type": "error_occurred",
  "error_details": {
    "component": "LLMInterface",
    "severity": "ERROR",
    "message": "All LLM providers failed. Last error: 429 Client Error: Too Many Requests for url: https://openrouter.ai/api/v1/chat/completions",
    "has_stack_trace": true
  }
},

{
  "event_id": "e713b2ef-3359-46ff-a573-b72c8e38545a",
  "timestamp": "2025-06-02T20:46:36.826990",
  "session_id": "2bf65e5e-68df-4782-864d-360f5882e64f",
  "event_type": "llm_query",
  "llm_query": {
    "model": "Mistral Small 3.1 (OpenRouter)",
    "prompt_length": 242,
    "temperature": null,
    "max_tokens": null,
    "has_system_prompt": false,
    "tools_available": 0
  }
},

{
  "event_id": "ac8b8d80-7265-47e2-8f87-52fe5a7197d2",
  "timestamp": "2025-06-02T20:46:38.079751",
  "session_id": "2bf65e5e-68df-4782-864d-360f5882e64f",
  "event_type": "llm_response",
  "llm_response": {
    "model": "Mistral Small 3.1 (OpenRouter)",
    "response_length": 0,
    "prompt_tokens": null,
    "completion_tokens": null,
    "total_tokens": null,
    "finish_reason": null,
    "latency_ms": 1250.0
  },
  "metadata": {
    "error": "All LLM providers failed. Last error: 429 Client Error: Too Many Requests for url: https://openrouter.ai/api/v1/chat/completions"
  }
},

{
  "event_id": "c00a693b-a054-44f3-8d9d-6661647ea7bc",
  "timestamp": "2025-06-02T20:46:38.079751",
  "session_id": "2bf65e5e-68df-4782-864d-360f5882e64f",
  "event_type": "error_occurred",
  "error_details": {
    "component": "LLMInterface",
    "severity": "ERROR",
    "message": "All LLM providers failed. Last error: 429 Client Error: Too Many Requests for url: https://openrouter.ai/api/v1/chat/completions",
    "has_stack_trace": true
  }
},

{
  "event_id": "41f11316-15d7-4753-8d9e-e3d6b2117044",
  "timestamp": "2025-06-02T20:46:38.080870",
  "session_id": "2bf65e5e-68df-4782-864d-360f5882e64f",
  "event_type": "llm_query",
  "llm_query": {
    "model": "Mistral Small 3.1 (OpenRouter)",
    "prompt_length": 275,
    "temperature": null,
    "max_tokens": null,
    "has_system_prompt": false,
    "tools_available": 0
  }
},

{
  "event_id": "5143badd-6bdb-4529-97b9-7ec9c51c07bb",
  "timestamp": "2025-06-02T20:46:40.164674",
  "session_id": "2bf65e5e-68df-4782-864d-360f5882e64f",
  "event_type": "llm_response",
  "llm_response": {
    "model": "Mistral Small 3.1 (OpenRouter)",
    "response_length": 0,
    "prompt_tokens": null,
    "completion_tokens": null,
    "total_tokens": null,
    "finish_reason": null,
    "latency_ms": 2078.0
  },
  "metadata": {
    "error": "All LLM providers failed. Last error: 429 Client Error: Too Many Requests for url: https://openrouter.ai/api/v1/chat/completions"
  }
},

{
  "event_id": "fe4db44d-d23e-4504-85ec-261c1e61df2b",
  "timestamp": "2025-06-02T20:46:40.165795",
  "session_id": "2bf65e5e-68df-4782-864d-360f5882e64f",
  "event_type": "error_occurred",
  "error_details": {
    "component": "LLMInterface",
    "severity": "ERROR",
    "message": "All LLM providers failed. Last error: 429 Client Error: Too Many Requests for url: https://openrouter.ai/api/v1/chat/completions",
    "has_stack_trace": true
  }
},

{
  "event_id": "e5f40040-2a14-4337-9867-d1c1e0de277a",
  "timestamp": "2025-06-02T20:46:40.166953",
  "session_id": "2bf65e5e-68df-4782-864d-360f5882e64f",
  "event_type": "llm_query",
  "llm_query": {
    "model": "Mistral Small 3.1 (OpenRouter)",
    "prompt_length": 308,
    "temperature": null,
    "max_tokens": null,
    "has_system_prompt": false,
    "tools_available": 0
  }
},

{
  "event_id": "926d97f3-20fa-4237-bb35-adba952c7422",
  "timestamp": "2025-06-02T20:46:41.497919",
  "session_id": "2bf65e5e-68df-4782-864d-360f5882e64f",
  "event_type": "llm_response",
  "llm_response": {
    "model": "Mistral Small 3.1 (OpenRouter)",
    "response_length": 0,
    "prompt_tokens": null,
    "completion_tokens": null,
    "total_tokens": null,
    "finish_reason": null,
    "latency_ms": 1312.0
  },
  "metadata": {
    "error": "All LLM providers failed. Last error: 429 Client Error: Too Many Requests for url: https://openrouter.ai/api/v1/chat/completions"
  }
},

{
  "event_id": "e72b4108-c886-47a8-ae6d-ad44342bcfbb",
  "timestamp": "2025-06-02T20:46:41.498916",
  "session_id": "2bf65e5e-68df-4782-864d-360f5882e64f",
  "event_type": "error_occurred",
  "error_details": {
    "component": "LLMInterface",
    "severity": "ERROR",
    "message": "All LLM providers failed. Last error: 429 Client Error: Too Many Requests for url: https://openrouter.ai/api/v1/chat/completions",
    "has_stack_trace": true
  }
},

{
  "event_id": "396a92cf-79d5-4886-a4b1-dd77c78eecdd",
  "timestamp": "2025-06-02T20:46:41.498916",
  "session_id": "2bf65e5e-68df-4782-864d-360f5882e64f",
  "event_type": "llm_query",
  "llm_query": {
    "model": "Mistral Small 3.1 (OpenRouter)",
    "prompt_length": 341,
    "temperature": null,
    "max_tokens": null,
    "has_system_prompt": false,
    "tools_available": 0
  }
},

{
  "event_id": "fa04003a-c556-483a-88ff-514e67c877a0",
  "timestamp": "2025-06-02T20:46:43.170950",
  "session_id": "2bf65e5e-68df-4782-864d-360f5882e64f",
  "event_type": "llm_response",
  "llm_response": {
    "model": "Mistral Small 3.1 (OpenRouter)",
    "response_length": 0,
    "prompt_tokens": null,
    "completion_tokens": null,
    "total_tokens": null,
    "finish_reason": null,
    "latency_ms": 1656.0
  },
  "metadata": {
    "error": "All LLM providers failed. Last error: 429 Client Error: Too Many Requests for url: https://openrouter.ai/api/v1/chat/completions"
  }
},

{
  "event_id": "f8b94de5-55c4-4817-9ea4-5a7a4d3b9c74",
  "timestamp": "2025-06-02T20:46:43.171481",
  "session_id": "2bf65e5e-68df-4782-864d-360f5882e64f",
  "event_type": "error_occurred",
  "error_details": {
    "component": "LLMInterface",
    "severity": "ERROR",
    "message": "All LLM providers failed. Last error: 429 Client Error: Too Many Requests for url: https://openrouter.ai/api/v1/chat/completions",
    "has_stack_trace": true
  }
},

{
  "event_id": "52471090-f69a-4dd3-b137-b778a8fba531",
  "timestamp": "2025-06-02T20:46:43.171985",
  "session_id": "2bf65e5e-68df-4782-864d-360f5882e64f",
  "event_type": "llm_query",
  "llm_query": {
    "model": "Mistral Small 3.1 (OpenRouter)",
    "prompt_length": 374,
    "temperature": null,
    "max_tokens": null,
    "has_system_prompt": false,
    "tools_available": 0
  }
},

{
  "event_id": "28709ad7-3bf9-44d7-ac2d-4d3e92667cd6",
  "timestamp": "2025-06-02T20:46:49.416284",
  "session_id": "2bf65e5e-68df-4782-864d-360f5882e64f",
  "event_type": "llm_response",
  "llm_response": {
    "model": "Mistral Small 3.1 (OpenRouter)",
    "response_length": 0,
    "prompt_tokens": null,
    "completion_tokens": null,
    "total_tokens": null,
    "finish_reason": null,
    "latency_ms": 6234.0
  },
  "metadata": {
    "error": "All LLM providers failed. Last error: 429 Client Error: Too Many Requests for url: https://openrouter.ai/api/v1/chat/completions"
  }
},

{
  "event_id": "ca3619fb-0579-4eda-bdfa-0ed344591707",
  "timestamp": "2025-06-02T20:46:49.416838",
  "session_id": "2bf65e5e-68df-4782-864d-360f5882e64f",
  "event_type": "error_occurred",
  "error_details": {
    "component": "LLMInterface",
    "severity": "ERROR",
    "message": "All LLM providers failed. Last error: 429 Client Error: Too Many Requests for url: https://openrouter.ai/api/v1/chat/completions",
    "has_stack_trace": true
  }
},

{
  "event_id": "87a9e782-44ab-4f81-8b01-9533bf52436f",
  "timestamp": "2025-06-02T20:46:49.417918",
  "session_id": "2bf65e5e-68df-4782-864d-360f5882e64f",
  "event_type": "llm_query",
  "llm_query": {
    "model": "Mistral Small 3.1 (OpenRouter)",
    "prompt_length": 407,
    "temperature": null,
    "max_tokens": null,
    "has_system_prompt": false,
    "tools_available": 0
  }
},

{
  "event_id": "91e9f391-4f28-40c6-a675-aba37e88c645",
  "timestamp": "2025-06-02T20:46:55.746854",
  "session_id": "2bf65e5e-68df-4782-864d-360f5882e64f",
  "event_type": "llm_response",
  "llm_response": {
    "model": "Mistral Small 3.1 (OpenRouter)",
    "response_length": 0,
    "prompt_tokens": null,
    "completion_tokens": null,
    "total_tokens": null,
    "finish_reason": null,
    "latency_ms": 6312.0
  },
  "metadata": {
    "error": "All LLM providers failed. Last error: 429 Client Error: Too Many Requests for url: https://openrouter.ai/api/v1/chat/completions"
  }
},

{
  "event_id": "ab994f48-0997-46e2-a3bf-5a71d4732598",
  "timestamp": "2025-06-02T20:46:55.748733",
  "session_id": "2bf65e5e-68df-4782-864d-360f5882e64f",
  "event_type": "error_occurred",
  "error_details": {
    "component": "LLMInterface",
    "severity": "ERROR",
    "message": "All LLM providers failed. Last error: 429 Client Error: Too Many Requests for url: https://openrouter.ai/api/v1/chat/completions",
    "has_stack_trace": true
  }
},

{
  "event_id": "95c7838c-f093-444b-a35d-710e2030d4a3",
  "timestamp": "2025-06-02T20:46:55.750709",
  "session_id": "2bf65e5e-68df-4782-864d-360f5882e64f",
  "event_type": "llm_query",
  "llm_query": {
    "model": "Mistral Small 3.1 (OpenRouter)",
    "prompt_length": 440,
    "temperature": null,
    "max_tokens": null,
    "has_system_prompt": false,
    "tools_available": 0
  }
},

{
  "event_id": "f889db31-f7ff-478b-84b2-f268d0fce2ab",
  "timestamp": "2025-06-02T20:46:57.088617",
  "session_id": "2bf65e5e-68df-4782-864d-360f5882e64f",
  "event_type": "llm_response",
  "llm_response": {
    "model": "Mistral Small 3.1 (OpenRouter)",
    "response_length": 0,
    "prompt_tokens": null,
    "completion_tokens": null,
    "total_tokens": null,
    "finish_reason": null,
    "latency_ms": 1328.0
  },
  "metadata": {
    "error": "All LLM providers failed. Last error: 429 Client Error: Too Many Requests for url: https://openrouter.ai/api/v1/chat/completions"
  }
},

{
  "event_id": "dc43b537-9ef1-42cb-a552-dd29496b9742",
  "timestamp": "2025-06-02T20:46:57.089542",
  "session_id": "2bf65e5e-68df-4782-864d-360f5882e64f",
  "event_type": "error_occurred",
  "error_details": {
    "component": "LLMInterface",
    "severity": "ERROR",
    "message": "All LLM providers failed. Last error: 429 Client Error: Too Many Requests for url: https://openrouter.ai/api/v1/chat/completions",
    "has_stack_trace": true
  }
},

{
  "event_id": "a738843b-7f14-4fea-a8c1-e9a4e544244a",
  "timestamp": "2025-06-02T21:18:16.358172",
  "session_id": "2bf65e5e-68df-4782-864d-360f5882e64f",
  "event_type": "user_input",
  "user_input": {
    "text": "Create 5 Python files, each containing 300 lines of functional code.\n\n",
    "intent": "agent_goal"
  }
},

{
  "event_id": "98969e6c-e7d1-40df-a096-8e4fead2aa82",
  "timestamp": "2025-06-02T21:18:17.624319",
  "session_id": "2bf65e5e-68df-4782-864d-360f5882e64f",
  "event_type": "llm_query",
  "llm_query": {
    "model": "Mistral Small 3.1 (OpenRouter)",
    "prompt_length": 210,
    "temperature": null,
    "max_tokens": null,
    "has_system_prompt": false,
    "tools_available": 0
  }
},

{
  "event_id": "dd5271b6-7c11-4758-84c1-90d9365a700d",
  "timestamp": "2025-06-02T21:18:18.791328",
  "session_id": "2bf65e5e-68df-4782-864d-360f5882e64f",
  "event_type": "llm_response",
  "llm_response": {
    "model": "Mistral Small 3.1 (OpenRouter)",
    "response_length": 0,
    "prompt_tokens": null,
    "completion_tokens": null,
    "total_tokens": null,
    "finish_reason": null,
    "latency_ms": 1156.0
  },
  "metadata": {
    "error": "All LLM providers failed. Last error: 429 Client Error: Too Many Requests for url: https://openrouter.ai/api/v1/chat/completions"
  }
},

{
  "event_id": "64e47888-09af-4bd6-98da-a65f1c78408d",
  "timestamp": "2025-06-02T21:18:18.791975",
  "session_id": "2bf65e5e-68df-4782-864d-360f5882e64f",
  "event_type": "error_occurred",
  "error_details": {
    "component": "LLMInterface",
    "severity": "ERROR",
    "message": "All LLM providers failed. Last error: 429 Client Error: Too Many Requests for url: https://openrouter.ai/api/v1/chat/completions",
    "has_stack_trace": true
  }
},

{
  "event_id": "99d6abd3-0eb0-43d4-bd74-f2bf94e92cf9",
  "timestamp": "2025-06-02T21:18:18.793097",
  "session_id": "2bf65e5e-68df-4782-864d-360f5882e64f",
  "event_type": "llm_query",
  "llm_query": {
    "model": "Mistral Small 3.1 (OpenRouter)",
    "prompt_length": 242,
    "temperature": null,
    "max_tokens": null,
    "has_system_prompt": false,
    "tools_available": 0
  }
},

{
  "event_id": "0cd9fa31-fb5e-4b1f-a0e0-167929a77e77",
  "timestamp": "2025-06-02T21:18:19.941889",
  "session_id": "2bf65e5e-68df-4782-864d-360f5882e64f",
  "event_type": "llm_response",
  "llm_response": {
    "model": "Mistral Small 3.1 (OpenRouter)",
    "response_length": 0,
    "prompt_tokens": null,
    "completion_tokens": null,
    "total_tokens": null,
    "finish_reason": null,
    "latency_ms": 1140.0
  },
  "metadata": {
    "error": "All LLM providers failed. Last error: 429 Client Error: Too Many Requests for url: https://openrouter.ai/api/v1/chat/completions"
  }
},

{
  "event_id": "62babf06-cacd-40c5-afc3-e75a5db0a6c7",
  "timestamp": "2025-06-02T21:18:19.942405",
  "session_id": "2bf65e5e-68df-4782-864d-360f5882e64f",
  "event_type": "error_occurred",
  "error_details": {
    "component": "LLMInterface",
    "severity": "ERROR",
    "message": "All LLM providers failed. Last error: 429 Client Error: Too Many Requests for url: https://openrouter.ai/api/v1/chat/completions",
    "has_stack_trace": true
  }
},

{
  "event_id": "55331b56-96f6-4df2-8919-5b22ea59cc80",
  "timestamp": "2025-06-02T21:18:19.942908",
  "session_id": "2bf65e5e-68df-4782-864d-360f5882e64f",
  "event_type": "llm_query",
  "llm_query": {
    "model": "Mistral Small 3.1 (OpenRouter)",
    "prompt_length": 275,
    "temperature": null,
    "max_tokens": null,
    "has_system_prompt": false,
    "tools_available": 0
  }
},

{
  "event_id": "7ff3cbaa-5ee4-48e3-8753-c46049269994",
  "timestamp": "2025-06-02T21:18:21.173508",
  "session_id": "2bf65e5e-68df-4782-864d-360f5882e64f",
  "event_type": "llm_response",
  "llm_response": {
    "model": "Mistral Small 3.1 (OpenRouter)",
    "response_length": 0,
    "prompt_tokens": null,
    "completion_tokens": null,
    "total_tokens": null,
    "finish_reason": null,
    "latency_ms": 1219.0
  },
  "metadata": {
    "error": "All LLM providers failed. Last error: 429 Client Error: Too Many Requests for url: https://openrouter.ai/api/v1/chat/completions"
  }
},

{
  "event_id": "61a35b39-db16-4d1a-a853-84b7df35799b",
  "timestamp": "2025-06-02T21:18:21.175543",
  "session_id": "2bf65e5e-68df-4782-864d-360f5882e64f",
  "event_type": "error_occurred",
  "error_details": {
    "component": "LLMInterface",
    "severity": "ERROR",
    "message": "All LLM providers failed. Last error: 429 Client Error: Too Many Requests for url: https://openrouter.ai/api/v1/chat/completions",
    "has_stack_trace": true
  }
},

{
  "event_id": "4f81fadc-981b-4732-9469-b3c245b6e80e",
  "timestamp": "2025-06-02T21:18:21.177243",
  "session_id": "2bf65e5e-68df-4782-864d-360f5882e64f",
  "event_type": "llm_query",
  "llm_query": {
    "model": "Mistral Small 3.1 (OpenRouter)",
    "prompt_length": 308,
    "temperature": null,
    "max_tokens": null,
    "has_system_prompt": false,
    "tools_available": 0
  }
},

{
  "event_id": "e25f9cc0-3282-4f04-a7e8-22697b3b2dad",
  "timestamp": "2025-06-02T21:18:22.405396",
  "session_id": "2bf65e5e-68df-4782-864d-360f5882e64f",
  "event_type": "llm_response",
  "llm_response": {
    "model": "Mistral Small 3.1 (OpenRouter)",
    "response_length": 0,
    "prompt_tokens": null,
    "completion_tokens": null,
    "total_tokens": null,
    "finish_reason": null,
    "latency_ms": 1218.0
  },
  "metadata": {
    "error": "All LLM providers failed. Last error: 429 Client Error: Too Many Requests for url: https://openrouter.ai/api/v1/chat/completions"
  }
},

{
  "event_id": "70ef1b74-ff39-4e3b-a8bd-fa1a5978b741",
  "timestamp": "2025-06-02T21:18:22.405396",
  "session_id": "2bf65e5e-68df-4782-864d-360f5882e64f",
  "event_type": "error_occurred",
  "error_details": {
    "component": "LLMInterface",
    "severity": "ERROR",
    "message": "All LLM providers failed. Last error: 429 Client Error: Too Many Requests for url: https://openrouter.ai/api/v1/chat/completions",
    "has_stack_trace": true
  }
},

{
  "event_id": "83a5f085-bfc0-4f0d-8fdc-8a6794eb7cb5",
  "timestamp": "2025-06-02T21:18:22.406477",
  "session_id": "2bf65e5e-68df-4782-864d-360f5882e64f",
  "event_type": "llm_query",
  "llm_query": {
    "model": "Mistral Small 3.1 (OpenRouter)",
    "prompt_length": 341,
    "temperature": null,
    "max_tokens": null,
    "has_system_prompt": false,
    "tools_available": 0
  }
},

{
  "event_id": "dc35a10b-3a84-4841-b393-1199994f7ecf",
  "timestamp": "2025-06-02T21:18:23.491024",
  "session_id": "2bf65e5e-68df-4782-864d-360f5882e64f",
  "event_type": "llm_response",
  "llm_response": {
    "model": "Mistral Small 3.1 (OpenRouter)",
    "response_length": 0,
    "prompt_tokens": null,
    "completion_tokens": null,
    "total_tokens": null,
    "finish_reason": null,
    "latency_ms": 1078.0
  },
  "metadata": {
    "error": "All LLM providers failed. Last error: 429 Client Error: Too Many Requests for url: https://openrouter.ai/api/v1/chat/completions"
  }
},

{
  "event_id": "77dd7851-27cd-4472-92d7-1ceff1499324",
  "timestamp": "2025-06-02T21:18:23.491543",
  "session_id": "2bf65e5e-68df-4782-864d-360f5882e64f",
  "event_type": "error_occurred",
  "error_details": {
    "component": "LLMInterface",
    "severity": "ERROR",
    "message": "All LLM providers failed. Last error: 429 Client Error: Too Many Requests for url: https://openrouter.ai/api/v1/chat/completions",
    "has_stack_trace": true
  }
},

{
  "event_id": "3cb41b73-75ed-4966-b069-ce8da0eab51a",
  "timestamp": "2025-06-02T21:18:23.492062",
  "session_id": "2bf65e5e-68df-4782-864d-360f5882e64f",
  "event_type": "llm_query",
  "llm_query": {
    "model": "Mistral Small 3.1 (OpenRouter)",
    "prompt_length": 374,
    "temperature": null,
    "max_tokens": null,
    "has_system_prompt": false,
    "tools_available": 0
  }
},

{
  "event_id": "ecf2112d-2dc6-4490-a735-85944e4f0286",
  "timestamp": "2025-06-02T21:18:24.573163",
  "session_id": "2bf65e5e-68df-4782-864d-360f5882e64f",
  "event_type": "llm_response",
  "llm_response": {
    "model": "Mistral Small 3.1 (OpenRouter)",
    "response_length": 0,
    "prompt_tokens": null,
    "completion_tokens": null,
    "total_tokens": null,
    "finish_reason": null,
    "latency_ms": 1078.0
  },
  "metadata": {
    "error": "All LLM providers failed. Last error: 429 Client Error: Too Many Requests for url: https://openrouter.ai/api/v1/chat/completions"
  }
},

{
  "event_id": "c971d90f-0564-4a62-a588-91d9ec7898f3",
  "timestamp": "2025-06-02T21:18:24.573692",
  "session_id": "2bf65e5e-68df-4782-864d-360f5882e64f",
  "event_type": "error_occurred",
  "error_details": {
    "component": "LLMInterface",
    "severity": "ERROR",
    "message": "All LLM providers failed. Last error: 429 Client Error: Too Many Requests for url: https://openrouter.ai/api/v1/chat/completions",
    "has_stack_trace": true
  }
},

{
  "event_id": "c351e2fb-12f0-4e5a-bfb3-7ec33186bc90",
  "timestamp": "2025-06-02T21:18:24.574214",
  "session_id": "2bf65e5e-68df-4782-864d-360f5882e64f",
  "event_type": "llm_query",
  "llm_query": {
    "model": "Mistral Small 3.1 (OpenRouter)",
    "prompt_length": 407,
    "temperature": null,
    "max_tokens": null,
    "has_system_prompt": false,
    "tools_available": 0
  }
},

{
  "event_id": "570f7da8-3490-43be-a646-9828ef297a0e",
  "timestamp": "2025-06-02T21:18:25.752015",
  "session_id": "2bf65e5e-68df-4782-864d-360f5882e64f",
  "event_type": "llm_response",
  "llm_response": {
    "model": "Mistral Small 3.1 (OpenRouter)",
    "response_length": 0,
    "prompt_tokens": null,
    "completion_tokens": null,
    "total_tokens": null,
    "finish_reason": null,
    "latency_ms": 1172.0
  },
  "metadata": {
    "error": "All LLM providers failed. Last error: 429 Client Error: Too Many Requests for url: https://openrouter.ai/api/v1/chat/completions"
  }
},

{
  "event_id": "c0cc944d-79a6-40e1-bacf-f0297efdd1ce",
  "timestamp": "2025-06-02T21:18:25.752544",
  "session_id": "2bf65e5e-68df-4782-864d-360f5882e64f",
  "event_type": "error_occurred",
  "error_details": {
    "component": "LLMInterface",
    "severity": "ERROR",
    "message": "All LLM providers failed. Last error: 429 Client Error: Too Many Requests for url: https://openrouter.ai/api/v1/chat/completions",
    "has_stack_trace": true
  }
},

{
  "event_id": "8ca8663d-06f0-439f-b081-d0aede78851f",
  "timestamp": "2025-06-02T21:18:25.753062",
  "session_id": "2bf65e5e-68df-4782-864d-360f5882e64f",
  "event_type": "llm_query",
  "llm_query": {
    "model": "Mistral Small 3.1 (OpenRouter)",
    "prompt_length": 440,
    "temperature": null,
    "max_tokens": null,
    "has_system_prompt": false,
    "tools_available": 0
  }
},

{
  "event_id": "9fe552dd-30a4-41fb-b5c3-6156a096c3ae",
  "timestamp": "2025-06-02T21:18:26.840906",
  "session_id": "2bf65e5e-68df-4782-864d-360f5882e64f",
  "event_type": "llm_response",
  "llm_response": {
    "model": "Mistral Small 3.1 (OpenRouter)",
    "response_length": 0,
    "prompt_tokens": null,
    "completion_tokens": null,
    "total_tokens": null,
    "finish_reason": null,
    "latency_ms": 1078.0
  },
  "metadata": {
    "error": "All LLM providers failed. Last error: 429 Client Error: Too Many Requests for url: https://openrouter.ai/api/v1/chat/completions"
  }
},

{
  "event_id": "cdc982ee-c59e-4d28-a3b2-ed9730f7cae4",
  "timestamp": "2025-06-02T21:18:26.841902",
  "session_id": "2bf65e5e-68df-4782-864d-360f5882e64f",
  "event_type": "error_occurred",
  "error_details": {
    "component": "LLMInterface",
    "severity": "ERROR",
    "message": "All LLM providers failed. Last error: 429 Client Error: Too Many Requests for url: https://openrouter.ai/api/v1/chat/completions",
    "has_stack_trace": true
  }
}