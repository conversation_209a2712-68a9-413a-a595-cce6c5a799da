"""
Test script for MindLink Agent Core.
"""

import sys
import os

# Add the current directory to the path so we can import mindlink
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from mindlink.agent import AgentOS
from mindlink.models.openrouter import OpenRouterModel
from mindlink.config import DEFAULT_SYSTEM_PROMPT


def main():
    """
    Test the MindLink Agent Core with a simple task.
    """
    print("Testing MindLink Agent Core with OpenRouter model...")
    
    # Create LLM using OpenRouter
    llm = OpenRouterModel(
        model_name="deepseek-r1",  # Options: "deepseek-r1", "glm-z1-32b"
        temperature=0.7,
        max_tokens=1024,
        top_p=0.9
    )
    
    # Create Agent OS
    agent = AgentOS(
        llm=llm,
        system_prompt_template=DEFAULT_SYSTEM_PROMPT,
        max_steps=15
    )
    
    # Define a simple goal
    goal = "Create a file called test.txt with the content 'This is a test file created by MindLink Agent Core.'"
    
    print(f"Goal: {goal}")
    print("\nRunning agent...")
    
    # Run the agent
    success, result, history = agent.run(goal)
    
    # Print result
    print("\n" + "="*50)
    print("Result:", "Success" if success else "Incomplete")
    print(result)
    print("="*50)
    
    # Print history summary
    print("\nAgent steps:")
    for i, entry in enumerate(history):
        print(f"{i+1}. {entry['request'].action.tool_name}")
    
    # Check if the file was created
    if os.path.exists("test.txt"):
        print("\nFile test.txt was successfully created.")
        with open("test.txt", "r") as f:
            content = f.read()
        print(f"Content: {content}")
    else:
        print("\nFile test.txt was not created.")


if __name__ == "__main__":
    main()
