import os
# Quick test for project structure performance and quality
os.environ["OPENROUTER_API_KEY"] = "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"

from agent_capability_benchmark import initialize_agent, test_project_structure, BenchmarkScore

agent = initialize_agent()
scores = BenchmarkScore()
test_project_structure(agent, scores)

print("Project Structure Test:")
print(" Success:", scores.project_structure > 0)
print("Score:", scores.project_structure)
print("Time:", scores.execution_times.get("project_structure"), "seconds") 