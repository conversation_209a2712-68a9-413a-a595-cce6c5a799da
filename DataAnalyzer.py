class DataAnalyzer:

    def __init__(self, data):
        """Initialize the DataAnalyzer with a list of numerical data.

        Args:
            data (list): A list of numerical data.
        """
        if not all(isinstance(i, (int, float)) for i in data):
            raise ValueError("All elements in the data list must be numerical.")
        self.data = data

    def calculate_mean(self):
        """Calculate the mean of the data.

        Returns:
            float: The mean of the data.
        """
        if not self.data:
            raise ValueError("Data list is empty.")
        return sum(self.data) / len(self.data)

    def calculate_median(self):
        """Calculate the median of the data.

        Returns:
            float: The median of the data.
        """
        if not self.data:
            raise ValueError("Data list is empty.")
        sorted_data = sorted(self.data)
        n = len(sorted_data)
        mid = n // 2
        if n % 2 == 0:
            median = (sorted_data[mid - 1] + sorted_data[mid]) / 2
        else:
            median = sorted_data[mid]
        return median

    def calculate_mode(self):
        """Calculate the mode of the data.

        Returns:
            list: The mode(s) of the data.
        """
        if not self.data:
            raise ValueError("Data list is empty.")
        frequency = {}
        for value in self.data:
            if value in frequency:
                frequency[value] += 1
            else:
                frequency[value] = 1
        max_frequency = max(frequency.values())
        mode = [key for key, value in frequency.items() if value == max_frequency]
        return mode

    def plot_histogram(self):
        """Plot a histogram of the data using matplotlib.

        This method requires matplotlib to be installed.
        """
        import matplotlib.pyplot as plt
        if not self.data:
            raise ValueError("Data list is empty.")
        plt.hist(self.data, bins='auto', alpha=0.7, rwidth=0.85)
        plt.title('Histogram of Data')
        plt.xlabel('Value')
        plt.ylabel('Frequency')
        plt.grid(axis='y', alpha=0.75)
        plt.show()