import os
import json
from pathlib import Path

def convert_latest_log(logs_dir=None):
    if logs_dir is None:
        logs_dir = os.path.join(os.path.dirname(__file__), 'logs')
    
    # Get all JSONL files
    log_files = list(Path(logs_dir).glob('*.jsonl'))
    
    if not log_files:
        print("No JSONL files found in the logs directory.")
        return
    
    # Sort by modification time to get the most recent
    latest_log = max(log_files, key=os.path.getmtime)
    output_path = latest_log.with_suffix('.json')
    
    print(f"Converting {latest_log.name} to JSON...")
    
    # Read the JSONL file and convert to JSON
    with open(latest_log, 'r', encoding='utf-8') as f:
        log_entries = [json.loads(line) for line in f if line.strip()]
    
    # Write as formatted JSON
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(log_entries, f, indent=2, ensure_ascii=False)
    
    print(f"Successfully converted to {output_path}")
    return str(output_path)

if __name__ == "__main__":
    convert_latest_log()
