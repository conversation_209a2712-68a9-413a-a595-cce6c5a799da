"""
Performance demonstration of MindLink Agent Core.
"""

import sys
import os
import time

# Add the current directory to the path so we can import mindlink
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from mindlink.agent import AgentOS
from mindlink.models.openai import OpenAIModel
from mindlink.models.openrouter import OpenRouterModel
from mindlink.config import DEFAULT_SYSTEM_PROMPT


def run_performance_test():
    """
    Run a comprehensive performance test of the MindLink Agent Core.
    """
    print("=== MindLink Agent Core Performance Test ===\n")
    
    # Create LLM: prefer OpenAI if API key, else fallback to OpenRouter
    api_key = os.getenv("OPENAI_API_KEY")
    if api_key:
        print("Initializing OpenAI LLM model...")
        llm = OpenAIModel(api_key=api_key)
    else:
        print("OPENAI_API_KEY not set; falling back to OpenRouterModel...")
        llm = OpenRouterModel(model_name="deepseek-r1", temperature=0.7, max_tokens=1024, top_p=0.9)
    
    # Create Agent OS
    print("Initializing Agent OS...")
    agent = AgentOS(
        llm=llm,
        system_prompt_template=DEFAULT_SYSTEM_PROMPT,
        max_steps=15
    )
    
    # Define a multi-step goal that requires planning and tool usage
    goal = """
    Perform the following tasks:
    1. Create a directory called 'performance_test'
    2. Create a file in that directory called 'data.txt' with the content 'This is test data'
    3. List the contents of the directory
    4. Read the content of the file to verify it was created correctly
    """
    
    print(f"\nGoal: {goal}")
    print("\nRunning agent...\n")
    
    # Measure execution time
    start_time = time.time()
    
    # Run the agent
    success, result, history = agent.run(goal)
    
    # Calculate execution time
    execution_time = time.time() - start_time
    
    # Print result
    print("\n" + "="*50)
    print(f"Execution time: {execution_time:.2f} seconds")
    print("Result:", "Success" if success else "Incomplete")
    print(result)
    print("="*50)
    
    # Print history summary
    print("\nAgent steps:")
    for i, entry in enumerate(history):
        print(f"{i+1}. {entry['request'].action.tool_name}")
    
    # Verify results
    print("\nVerifying results:")
    
    if os.path.exists("performance_test"):
        print("✓ Directory 'performance_test' was created")
        
        if os.path.exists("performance_test/data.txt"):
            print("✓ File 'data.txt' was created in the directory")
            
            with open("performance_test/data.txt", "r") as f:
                content = f.read()
                if content == "This is test data":
                    print("✓ File content is correct")
                else:
                    print("✗ File content is incorrect")
        else:
            print("✗ File 'data.txt' was not created")
    else:
        print("✗ Directory 'performance_test' was not created")
    
    # Calculate success metrics
    total_steps = len(history)
    successful_steps = sum(1 for entry in history if entry['response'].status == "success")
    success_rate = (successful_steps / total_steps) * 100 if total_steps > 0 else 0
    
    print(f"\nPerformance metrics:")
    print(f"Total steps: {total_steps}")
    print(f"Successful steps: {successful_steps}")
    print(f"Success rate: {success_rate:.1f}%")
    print(f"Average time per step: {execution_time/total_steps:.2f} seconds")


# New complex performance test added by Cascade
def test_complex_performance():
    import time
    from mindlink.agent import AgentOS  # Replace with actual import if needed; assuming based on library structure
    start_time = time.time()
    
    # Simulate nested and complex operations with loops and recursion
    def nested_function(depth, max_depth=5):
        if depth > max_depth:
            return 0
        result = 0
        for i in range(10):  # Arbitrary nested loop for complexity
            agent = AgentOS(llm=None, system_prompt_template=DEFAULT_SYSTEM_PROMPT, max_steps=15)  # Hypothetical agent creation
            result += 1  # Assumed method; adapt if necessary
            result += nested_function(depth + 1, max_depth)  # Recursive call
        return result
    
    try:
        nested_function(1)  # Start recursion from depth 1
        end_time = time.time()
        execution_time = end_time - start_time
        assert execution_time < 5.0, f"Performance too slow: took {execution_time:.2f} seconds"
    except Exception as e:
        assert False, f"Error during performance test: {str(e)}"


if __name__ == "__main__":
    run_performance_test()
    test_complex_performance()
