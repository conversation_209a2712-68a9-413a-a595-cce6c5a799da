"""
Ultimate Challenge Performance Test for MindLink Agent Core.

This test represents the most extreme, complex scenario possible:
1. Massive parallel operations with high concurrency
2. Complex nested transactions with rollbacks
3. Large file operations with streaming
4. Tool chaining and composition
5. Memory pressure and resource constraints
6. Error injection and recovery
7. Real-time performance monitoring
8. Complex project structure manipulation

This test is designed to push the library beyond its limits and evaluate
its performance, stability, and error handling under extreme conditions.
"""

import os
import sys
import time
import tempfile
import shutil
import random
import string
import threading
import queue
import concurrent.futures
import traceback
import json
import gc
from contextlib import contextmanager
from typing import Dict, List, Any, Tuple, Optional, Set
from dataclasses import dataclass

# Import core components
from mindlink.tools.file_tools import (
    TransactionManager,
    create_file,
    read_file,
    list_files,
    path_exists,
    CreateFileTool,
    ReadFileTool,
    ListFilesTool,
    safe_cleanup_directory
)
from mindlink.tools.base import tool_registry
from mindlink.schemas.mindlink import MindLinkRequest, Action
from mindlink.executor import ToolExecutor


# Performance monitoring
@dataclass
class PerformanceMetric:
    """Container for performance metrics."""
    operation: str
    start_time: float
    end_time: float
    duration: float
    success: bool
    error: Optional[str] = None
    metadata: Dict[str, Any] = None

    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


class PerformanceMonitor:
    """Real-time performance monitoring."""
    
    def __init__(self):
        self.metrics = []
        self.lock = threading.Lock()
        self._start_time = time.time()
    
    def record(self, metric: PerformanceMetric):
        """Record a performance metric."""
        with self.lock:
            self.metrics.append(metric)
    
    def start_operation(self, operation: str, metadata: Dict[str, Any] = None) -> int:
        """Start timing an operation and return its ID."""
        metric = PerformanceMetric(
            operation=operation,
            start_time=time.time(),
            end_time=0,
            duration=0,
            success=False,
            metadata=metadata or {}
        )
        with self.lock:
            self.metrics.append(metric)
            return len(self.metrics) - 1
    
    def end_operation(self, metric_id: int, success: bool = True, error: Optional[str] = None):
        """End timing an operation."""
        end_time = time.time()
        with self.lock:
            metric = self.metrics[metric_id]
            metric.end_time = end_time
            metric.duration = end_time - metric.start_time
            metric.success = success
            metric.error = error
    
    def get_summary(self) -> Dict[str, Any]:
        """Get a summary of all metrics."""
        with self.lock:
            operations = set(m.operation for m in self.metrics)
            summary = {
                "total_operations": len(self.metrics),
                "successful_operations": sum(1 for m in self.metrics if m.success),
                "failed_operations": sum(1 for m in self.metrics if not m.success),
                "total_duration": time.time() - self._start_time,
                "operations": {}
            }
            
            for op in operations:
                op_metrics = [m for m in self.metrics if m.operation == op]
                successful = [m for m in op_metrics if m.success]
                failed = [m for m in op_metrics if not m.success]
                
                if not op_metrics:
                    continue
                
                durations = [m.duration for m in successful] if successful else [0]
                
                summary["operations"][op] = {
                    "count": len(op_metrics),
                    "successful": len(successful),
                    "failed": len(failed),
                    "avg_duration": sum(durations) / len(durations) if durations else 0,
                    "min_duration": min(durations) if durations else 0,
                    "max_duration": max(durations) if durations else 0,
                    "total_duration": sum(durations),
                    "errors": [m.error for m in failed if m.error]
                }
            
            return summary
    
    def print_summary(self):
        """Print a summary of all metrics."""
        summary = self.get_summary()
        
        print("\n=== Performance Summary ===")
        print(f"Total operations: {summary['total_operations']}")
        print(f"Successful operations: {summary['successful_operations']}")
        print(f"Failed operations: {summary['failed_operations']}")
        print(f"Total duration: {summary['total_duration']:.2f} seconds")
        
        print("\nOperation Details:")
        for op, details in summary["operations"].items():
            print(f"\n  {op}:")
            print(f"    Count: {details['count']}")
            print(f"    Success rate: {details['successful'] / details['count'] * 100:.1f}%")
            print(f"    Avg duration: {details['avg_duration'] * 1000:.2f} ms")
            print(f"    Min duration: {details['min_duration'] * 1000:.2f} ms")
            print(f"    Max duration: {details['max_duration'] * 1000:.2f} ms")
            print(f"    Total duration: {details['total_duration']:.2f} seconds")
            
            if details["errors"]:
                print(f"    Error samples ({min(3, len(details['errors']))} of {len(details['errors'])}):")
                for error in details["errors"][:3]:
                    print(f"      - {error}")


# Utility functions
def generate_random_content(size_kb: int) -> str:
    """Generate random content of specified size in KB."""
    chars = string.ascii_letters + string.digits
    return ''.join(random.choice(chars) for _ in range(size_kb * 1024))


def generate_complex_json(depth: int, breadth: int) -> Dict[str, Any]:
    """Generate a complex nested JSON structure."""
    if depth <= 0:
        return {f"key_{i}": random.choice([
            random.randint(1, 1000),
            random.random(),
            ''.join(random.choices(string.ascii_letters, k=10)),
            random.choice([True, False]),
            None
        ]) for i in range(breadth)}
    
    result = {}
    for i in range(breadth):
        if random.random() < 0.7:  # 70% chance of nesting
            result[f"nested_{i}"] = generate_complex_json(depth - 1, breadth)
        else:
            result[f"key_{i}"] = random.choice([
                random.randint(1, 1000),
                random.random(),
                ''.join(random.choices(string.ascii_letters, k=10)),
                random.choice([True, False]),
                None
            ])
    
    # Add arrays
    result["array"] = [generate_complex_json(depth - 1, max(1, breadth // 2)) for _ in range(breadth)]
    
    return result


class ComplexProject:
    """Manages a complex project structure for testing."""
    
    def __init__(self, base_dir: str, monitor: PerformanceMonitor):
        self.base_dir = base_dir
        self.monitor = monitor
        self.dirs = []
        self.files = []
        self.locks = {}  # path -> lock
    
    def create_structure(self, depth: int = 5, breadth: int = 5, files_per_dir: int = 10):
        """Create a complex directory structure."""
        metric_id = self.monitor.start_operation("create_project_structure", {
            "depth": depth,
            "breadth": breadth,
            "files_per_dir": files_per_dir
        })
        
        try:
            self._create_dir_recursive(self.base_dir, depth, breadth, files_per_dir)
            self.monitor.end_operation(metric_id, success=True)
        except Exception as e:
            self.monitor.end_operation(metric_id, success=False, error=str(e))
            raise
    
    def _create_dir_recursive(self, path: str, depth: int, breadth: int, files_per_dir: int):
        """Recursively create directories and files."""
        os.makedirs(path, exist_ok=True)
        self.dirs.append(path)
        
        # Create files in this directory
        for i in range(files_per_dir):
            file_type = random.choice(["txt", "json", "py", "md", "csv"])
            file_path = os.path.join(path, f"file_{i}.{file_type}")
            
            if file_type == "json":
                content = json.dumps(generate_complex_json(3, 5), indent=2)
            elif file_type == "py":
                content = f"""
def function_{i}():
    \"\"\"Function {i} documentation.\"\"\"
    print("Function {i}")
    return {i}

class Class_{i}:
    \"\"\"Class {i} documentation.\"\"\"
    
    def __init__(self):
        self.value = {i}
    
    def method(self):
        return self.value * 2

if __name__ == "__main__":
    obj = Class_{i}()
    print(obj.method())
    print(function_{i}())
"""
            elif file_type == "md":
                content = f"""
# Heading 1

## Heading 2

### Heading 3

This is a paragraph with **bold** and *italic* text.

- List item 1
- List item 2
- List item 3

1. Numbered item 1
2. Numbered item 2
3. Numbered item 3

```python
def example():
    return "This is a code block"
```

> This is a blockquote

| Column 1 | Column 2 | Column 3 |
|----------|----------|----------|
| Row 1    | Data     | Data     |
| Row 2    | Data     | Data     |
| Row 3    | Data     | Data     |
"""
            elif file_type == "csv":
                content = "id,name,value\n"
                for j in range(100):
                    content += f"{j},item_{j},{random.randint(1, 1000)}\n"
            else:  # txt
                content = generate_random_content(random.randint(1, 10))
            
            create_file(file_path, content)
            self.files.append(file_path)
            self.locks[file_path] = threading.Lock()
        
        # Recursively create subdirectories if depth > 0
        if depth > 0:
            for i in range(breadth):
                subdir = os.path.join(path, f"dir_{i}")
                self._create_dir_recursive(subdir, depth - 1, breadth, files_per_dir)
    
    def get_random_file(self) -> str:
        """Get a random file path from the project."""
        return random.choice(self.files) if self.files else None
    
    def get_random_dir(self) -> str:
        """Get a random directory path from the project."""
        return random.choice(self.dirs) if self.dirs else None
    
    def get_file_lock(self, path: str) -> threading.Lock:
        """Get the lock for a file."""
        return self.locks.get(path, threading.Lock())


class ChaosMonkey:
    """Introduces random failures and errors to test error handling."""
    
    def __init__(self, failure_rate: float = 0.1):
        self.failure_rate = failure_rate
    
    def maybe_fail(self, operation: str = "unknown"):
        """Randomly fail with the configured probability."""
        if random.random() < self.failure_rate:
            raise Exception(f"Chaos Monkey induced failure during {operation}")


class WorkerPool:
    """Manages a pool of workers for concurrent operations."""
    
    def __init__(self, project: ComplexProject, monitor: PerformanceMonitor, chaos: ChaosMonkey):
        self.project = project
        self.monitor = monitor
        self.chaos = chaos
        self.results_queue = queue.Queue()
        self.error_queue = queue.Queue()
    
    def run_concurrent_operations(self, num_workers: int, operations_per_worker: int):
        """Run concurrent operations with multiple workers."""
        metric_id = self.monitor.start_operation("concurrent_operations", {
            "num_workers": num_workers,
            "operations_per_worker": operations_per_worker
        })
        
        try:
            with concurrent.futures.ThreadPoolExecutor(max_workers=num_workers) as executor:
                futures = [
                    executor.submit(self._worker_task, i, operations_per_worker)
                    for i in range(num_workers)
                ]
                
                # Wait for all workers to complete
                for future in concurrent.futures.as_completed(futures):
                    try:
                        worker_id, success_count, error_count = future.result()
                        self.results_queue.put((worker_id, success_count, error_count))
                    except Exception as e:
                        self.error_queue.put(str(e))
            
            # Collect results
            success_count = 0
            error_count = 0
            
            while not self.results_queue.empty():
                _, s, e = self.results_queue.get()
                success_count += s
                error_count += e
            
            self.monitor.end_operation(metric_id, success=True, 
                                      error=f"{error_count} operations failed" if error_count > 0 else None)
            
            return success_count, error_count
        
        except Exception as e:
            self.monitor.end_operation(metric_id, success=False, error=str(e))
            raise
    
    def _worker_task(self, worker_id: int, num_operations: int) -> Tuple[int, int, int]:
        """Worker task that performs random operations."""
        success_count = 0
        error_count = 0
        
        for i in range(num_operations):
            try:
                # Choose a random operation
                operation = random.choice([
                    self._read_random_file,
                    self._write_random_file,
                    self._list_random_dir,
                    self._nested_transaction,
                    self._execute_tool
                ])
                
                # Perform the operation
                operation(worker_id, i)
                success_count += 1
            
            except Exception as e:
                error_count += 1
                self.error_queue.put(f"Worker {worker_id}, Operation {i}: {str(e)}")
        
        return worker_id, success_count, error_count
    
    def _read_random_file(self, worker_id: int, operation_id: int):
        """Read a random file from the project."""
        file_path = self.project.get_random_file()
        if not file_path:
            return
        
        metric_id = self.monitor.start_operation("read_file", {
            "worker_id": worker_id,
            "operation_id": operation_id,
            "path": file_path
        })
        
        try:
            # Try to introduce a failure
            self.chaos.maybe_fail("read_file")
            
            # Get the lock for this file
            with self.project.get_file_lock(file_path):
                content = read_file(file_path)
                assert len(content) > 0
            
            self.monitor.end_operation(metric_id, success=True)
        
        except Exception as e:
            self.monitor.end_operation(metric_id, success=False, error=str(e))
            raise
    
    def _write_random_file(self, worker_id: int, operation_id: int):
        """Write to a random file in the project."""
        file_path = self.project.get_random_file()
        if not file_path:
            return
        
        metric_id = self.monitor.start_operation("write_file", {
            "worker_id": worker_id,
            "operation_id": operation_id,
            "path": file_path
        })
        
        try:
            # Try to introduce a failure
            self.chaos.maybe_fail("write_file")
            
            # Get the lock for this file
            with self.project.get_file_lock(file_path):
                # Read the current content
                current_content = read_file(file_path)
                
                # Append some content
                new_content = current_content + f"\n# Modified by Worker {worker_id}, Operation {operation_id}\n"
                
                # Write the new content
                create_file(file_path, new_content)
            
            self.monitor.end_operation(metric_id, success=True)
        
        except Exception as e:
            self.monitor.end_operation(metric_id, success=False, error=str(e))
            raise
    
    def _list_random_dir(self, worker_id: int, operation_id: int):
        """List files in a random directory."""
        dir_path = self.project.get_random_dir()
        if not dir_path:
            return
        
        metric_id = self.monitor.start_operation("list_dir", {
            "worker_id": worker_id,
            "operation_id": operation_id,
            "path": dir_path
        })
        
        try:
            # Try to introduce a failure
            self.chaos.maybe_fail("list_dir")
            
            # List the directory
            files = list_files(dir_path)
            assert isinstance(files, list)
            
            self.monitor.end_operation(metric_id, success=True)
        
        except Exception as e:
            self.monitor.end_operation(metric_id, success=False, error=str(e))
            raise
    
    def _nested_transaction(self, worker_id: int, operation_id: int):
        """Perform a nested transaction with multiple operations."""
        file_path = self.project.get_random_file()
        if not file_path:
            return
        
        metric_id = self.monitor.start_operation("nested_transaction", {
            "worker_id": worker_id,
            "operation_id": operation_id,
            "path": file_path
        })
        
        try:
            # Try to introduce a failure
            self.chaos.maybe_fail("nested_transaction_outer")
            
            # Get the lock for this file
            with self.project.get_file_lock(file_path):
                # Start an outer transaction
                with TransactionManager():
                    # Read the current content
                    current_content = read_file(file_path)
                    
                    # Modify the content
                    new_content = current_content + f"\n# Outer transaction by Worker {worker_id}, Operation {operation_id}\n"
                    create_file(file_path, new_content)
                    
                    # Try to introduce a failure
                    self.chaos.maybe_fail("nested_transaction_inner")
                    
                    # Start an inner transaction
                    with TransactionManager():
                        # Read the content again
                        current_content = read_file(file_path)
                        
                        # Modify the content again
                        new_content = current_content + f"\n# Inner transaction by Worker {worker_id}, Operation {operation_id}\n"
                        create_file(file_path, new_content)
                        
                        # 50% chance of forcing a rollback
                        if random.random() < 0.5:
                            raise Exception("Forced rollback of inner transaction")
            
            self.monitor.end_operation(metric_id, success=True)
        
        except Exception as e:
            self.monitor.end_operation(metric_id, success=False, error=str(e))
            raise
    
    def _execute_tool(self, worker_id: int, operation_id: int):
        """Execute a random tool."""
        file_path = self.project.get_random_file()
        if not file_path:
            return
        
        metric_id = self.monitor.start_operation("execute_tool", {
            "worker_id": worker_id,
            "operation_id": operation_id,
            "path": file_path
        })
        
        try:
            # Try to introduce a failure
            self.chaos.maybe_fail("execute_tool")
            
            # Choose a random tool
            tool_name = random.choice(["create_file", "read_file", "list_files"])
            
            # Create the request
            if tool_name == "create_file":
                request = MindLinkRequest(
                    action=Action(
                        tool_name=tool_name,
                        parameters={
                            "path": file_path,
                            "content": f"Content from tool execution by Worker {worker_id}, Operation {operation_id}"
                        }
                    ),
                    reasoning=f"Testing tool execution by Worker {worker_id}, Operation {operation_id}"
                )
            elif tool_name == "read_file":
                request = MindLinkRequest(
                    action=Action(
                        tool_name=tool_name,
                        parameters={
                            "path": file_path
                        }
                    ),
                    reasoning=f"Testing tool execution by Worker {worker_id}, Operation {operation_id}"
                )
            else:  # list_files
                request = MindLinkRequest(
                    action=Action(
                        tool_name=tool_name,
                        parameters={
                            "directory": os.path.dirname(file_path)
                        }
                    ),
                    reasoning=f"Testing tool execution by Worker {worker_id}, Operation {operation_id}"
                )
            
            # Execute the tool
            executor = ToolExecutor()
            response = executor.execute(request)
            
            # Verify the response
            assert response.status in ["success", "error"]
            assert response.observation is not None
            
            self.monitor.end_operation(metric_id, success=response.status == "success", 
                                      error=response.error if response.status == "error" else None)
        
        except Exception as e:
            self.monitor.end_operation(metric_id, success=False, error=str(e))
            raise


def run_ultimate_challenge():
    """Run the ultimate challenge performance test."""
    print("\n=== Running Ultimate Challenge Performance Test ===\n")
    
    # Create a temporary directory
    temp_dir = tempfile.mkdtemp()
    
    try:
        # Initialize performance monitor
        monitor = PerformanceMonitor()
        
        # Create a complex project structure
        print("Creating complex project structure...")
        project = ComplexProject(temp_dir, monitor)
        project.create_structure(depth=3, breadth=3, files_per_dir=5)
        
        # Initialize chaos monkey
        chaos = ChaosMonkey(failure_rate=0.05)  # 5% failure rate
        
        # Initialize worker pool
        pool = WorkerPool(project, monitor, chaos)
        
        # Run concurrent operations
        print("\nRunning concurrent operations...")
        num_workers = 8
        operations_per_worker = 20
        success_count, error_count = pool.run_concurrent_operations(num_workers, operations_per_worker)
        
        print(f"\nCompleted {success_count + error_count} operations")
        print(f"Successful: {success_count}")
        print(f"Failed: {error_count}")
        
        # Print performance summary
        monitor.print_summary()
        
        # Save performance report
        report_path = os.path.join(temp_dir, "performance_report.json")
        with open(report_path, "w") as f:
            json.dump(monitor.get_summary(), f, indent=2)
        
        print(f"\nPerformance report saved to: {report_path}")
        print("\n=== Ultimate Challenge Performance Test Completed ===")
    
    finally:
        # Clean up using the improved safe_cleanup_directory
        safe_cleanup_directory(temp_dir, recreate=False)
        print("\n=== Ultimate Challenge Performance Test Cleanup Complete ===")


if __name__ == "__main__":
    run_ultimate_challenge()
