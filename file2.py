#!/usr/bin/env python3
"""
Python Data Analysis and Visualization Toolkit
A comprehensive toolkit for statistical analysis, data cleaning, plotting,
machine learning helpers, and report generation.
"""

import os
import sys
import json
import csv
import math
import statistics
import random
import datetime
from typing import List, Dict, Any, Optional, Union, Tuple
from collections import defaultdict, Counter
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DataCleaner:
    """
    Data cleaning and preprocessing utilities.
    """
    
    def __init__(self):
        self.cleaning_stats = defaultdict(int)
        self.transformations_applied = []
    
    def remove_duplicates(self, data: List[Dict[str, Any]], key_fields: List[str] = None) -> List[Dict[str, Any]]:
        """
        Remove duplicate records from data.
        
        Args:
            data (List[Dict[str, Any]]): Input data
            key_fields (List[str]): Fields to consider for uniqueness
            
        Returns:
            List[Dict[str, Any]]: Data with duplicates removed
        """
        if not key_fields:
            # Remove exact duplicates
            seen = set()
            unique_data = []
            for item in data:
                item_str = json.dumps(item, sort_keys=True)
                if item_str not in seen:
                    seen.add(item_str)
                    unique_data.append(item)
        else:
            # Remove duplicates based on key fields
            seen = set()
            unique_data = []
            for item in data:
                key = tuple(item.get(field, '') for field in key_fields)
                if key not in seen:
                    seen.add(key)
                    unique_data.append(item)
        
        removed_count = len(data) - len(unique_data)
        self.cleaning_stats['duplicates_removed'] += removed_count
        self.transformations_applied.append(f"Removed {removed_count} duplicates")
        logger.info(f"Removed {removed_count} duplicate records")
        return unique_data
    
    def handle_missing_values(self, data: List[Dict[str, Any]], strategy: str = 'remove', 
                            fill_value: Any = None, fields: List[str] = None) -> List[Dict[str, Any]]:
        """
        Handle missing values in data.
        
        Args:
            data (List[Dict[str, Any]]): Input data
            strategy (str): Strategy ('remove', 'fill', 'mean', 'median', 'mode')
            fill_value (Any): Value to fill missing data with
            fields (List[str]): Specific fields to process
            
        Returns:
            List[Dict[str, Any]]: Data with missing values handled
        """
        if strategy == 'remove':
            cleaned_data = []
            for item in data:
                if fields:
                    has_missing = any(item.get(field) in [None, '', 'N/A', 'NULL'] for field in fields)
                else:
                    has_missing = any(value in [None, '', 'N/A', 'NULL'] for value in item.values())
                
                if not has_missing:
                    cleaned_data.append(item)
            
            removed_count = len(data) - len(cleaned_data)
            self.cleaning_stats['missing_records_removed'] += removed_count
            self.transformations_applied.append(f"Removed {removed_count} records with missing values")
            return cleaned_data
        
        elif strategy == 'fill':
            for item in data:
                target_fields = fields if fields else item.keys()
                for field in target_fields:
                    if item.get(field) in [None, '', 'N/A', 'NULL']:
                        item[field] = fill_value
                        self.cleaning_stats['missing_values_filled'] += 1
            
            self.transformations_applied.append(f"Filled missing values with {fill_value}")
            return data
        
        elif strategy in ['mean', 'median', 'mode']:
            # Calculate statistics for numeric fields
            numeric_stats = {}
            target_fields = fields if fields else data[0].keys() if data else []
            
            for field in target_fields:
                values = []
                for item in data:
                    value = item.get(field)
                    if value not in [None, '', 'N/A', 'NULL']:
                        try:
                            values.append(float(value))
                        except (ValueError, TypeError):
                            continue
                
                if values:
                    if strategy == 'mean':
                        numeric_stats[field] = statistics.mean(values)
                    elif strategy == 'median':
                        numeric_stats[field] = statistics.median(values)
                    elif strategy == 'mode':
                        try:
                            numeric_stats[field] = statistics.mode(values)
                        except statistics.StatisticsError:
                            numeric_stats[field] = values[0]  # Fallback to first value
            
            # Fill missing values with calculated statistics
            for item in data:
                for field, fill_val in numeric_stats.items():
                    if item.get(field) in [None, '', 'N/A', 'NULL']:
                        item[field] = fill_val
                        self.cleaning_stats['missing_values_filled'] += 1
            
            self.transformations_applied.append(f"Filled missing values using {strategy}")
            return data
        
        return data
    
    def normalize_text(self, data: List[Dict[str, Any]], fields: List[str], 
                      operations: List[str] = ['lower', 'strip']) -> List[Dict[str, Any]]:
        """
        Normalize text fields in data.
        
        Args:
            data (List[Dict[str, Any]]): Input data
            fields (List[str]): Text fields to normalize
            operations (List[str]): Operations to apply ('lower', 'upper', 'strip', 'title')
            
        Returns:
            List[Dict[str, Any]]: Data with normalized text
        """
        for item in data:
            for field in fields:
                if field in item and isinstance(item[field], str):
                    text = item[field]
                    
                    if 'strip' in operations:
                        text = text.strip()
                    if 'lower' in operations:
                        text = text.lower()
                    elif 'upper' in operations:
                        text = text.upper()
                    elif 'title' in operations:
                        text = text.title()
                    
                    item[field] = text
                    self.cleaning_stats['text_normalizations'] += 1
        
        self.transformations_applied.append(f"Normalized text fields: {fields}")
        return data
    
    def validate_data_types(self, data: List[Dict[str, Any]], 
                          type_schema: Dict[str, str]) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]]]:
        """
        Validate and convert data types according to schema.
        
        Args:
            data (List[Dict[str, Any]]): Input data
            type_schema (Dict[str, str]): Field type schema
            
        Returns:
            Tuple[List[Dict[str, Any]], List[Dict[str, Any]]]: Valid data and invalid records
        """
        valid_data = []
        invalid_data = []
        
        for item in data:
            is_valid = True
            converted_item = item.copy()
            
            for field, expected_type in type_schema.items():
                if field in item:
                    value = item[field]
                    try:
                        if expected_type == 'int':
                            converted_item[field] = int(value)
                        elif expected_type == 'float':
                            converted_item[field] = float(value)
                        elif expected_type == 'str':
                            converted_item[field] = str(value)
                        elif expected_type == 'bool':
                            if isinstance(value, str):
                                converted_item[field] = value.lower() in ['true', '1', 'yes', 'on']
                            else:
                                converted_item[field] = bool(value)
                    except (ValueError, TypeError):
                        is_valid = False
                        break
            
            if is_valid:
                valid_data.append(converted_item)
                self.cleaning_stats['valid_records'] += 1
            else:
                invalid_data.append(item)
                self.cleaning_stats['invalid_records'] += 1
        
        self.transformations_applied.append(f"Validated {len(valid_data)} records, {len(invalid_data)} invalid")
        return valid_data, invalid_data
    
    def get_cleaning_report(self) -> Dict[str, Any]:
        """
        Get a report of all cleaning operations performed.
        
        Returns:
            Dict[str, Any]: Cleaning report
        """
        return {
            'statistics': dict(self.cleaning_stats),
            'transformations': self.transformations_applied,
            'timestamp': datetime.datetime.now().isoformat()
        }

class StatisticalAnalyzer:
    """
    Statistical analysis utilities.
    """
    
    @staticmethod
    def descriptive_statistics(data: List[Union[int, float]]) -> Dict[str, float]:
        """
        Calculate descriptive statistics for a dataset.
        
        Args:
            data (List[Union[int, float]]): Numeric data
            
        Returns:
            Dict[str, float]: Descriptive statistics
        """
        if not data:
            return {}
        
        data = [x for x in data if x is not None]
        if not data:
            return {}
        
        stats = {
            'count': len(data),
            'mean': statistics.mean(data),
            'median': statistics.median(data),
            'min': min(data),
            'max': max(data),
            'range': max(data) - min(data),
            'sum': sum(data)
        }
        
        if len(data) > 1:
            stats['std_dev'] = statistics.stdev(data)
            stats['variance'] = statistics.variance(data)
        
        try:
            stats['mode'] = statistics.mode(data)
        except statistics.StatisticsError:
            stats['mode'] = None
        
        # Calculate percentiles
        sorted_data = sorted(data)
        n = len(sorted_data)
        stats['q1'] = sorted_data[n // 4] if n > 3 else sorted_data[0]
        stats['q3'] = sorted_data[3 * n // 4] if n > 3 else sorted_data[-1]
        stats['iqr'] = stats['q3'] - stats['q1']
        
        return stats
    
    @staticmethod
    def correlation_coefficient(x: List[float], y: List[float]) -> float:
        """
        Calculate Pearson correlation coefficient between two variables.
        
        Args:
            x (List[float]): First variable
            y (List[float]): Second variable
            
        Returns:
            float: Correlation coefficient
        """
        if len(x) != len(y) or len(x) < 2:
            return 0.0
        
        n = len(x)
        sum_x = sum(x)
        sum_y = sum(y)
        sum_xy = sum(x[i] * y[i] for i in range(n))
        sum_x2 = sum(xi ** 2 for xi in x)
        sum_y2 = sum(yi ** 2 for yi in y)
        
        numerator = n * sum_xy - sum_x * sum_y
        denominator = math.sqrt((n * sum_x2 - sum_x ** 2) * (n * sum_y2 - sum_y ** 2))
        
        if denominator == 0:
            return 0.0
        
        return numerator / denominator
    
    @staticmethod
    def linear_regression(x: List[float], y: List[float]) -> Dict[str, float]:
        """
        Perform simple linear regression.
        
        Args:
            x (List[float]): Independent variable
            y (List[float]): Dependent variable
            
        Returns:
            Dict[str, float]: Regression results
        """
        if len(x) != len(y) or len(x) < 2:
            return {}
        
        n = len(x)
        sum_x = sum(x)
        sum_y = sum(y)
        sum_xy = sum(x[i] * y[i] for i in range(n))
        sum_x2 = sum(xi ** 2 for xi in x)
        
        # Calculate slope and intercept
        slope = (n * sum_xy - sum_x * sum_y) / (n * sum_x2 - sum_x ** 2)
        intercept = (sum_y - slope * sum_x) / n
        
        # Calculate R-squared
        y_mean = sum_y / n
        ss_tot = sum((yi - y_mean) ** 2 for yi in y)
        ss_res = sum((y[i] - (slope * x[i] + intercept)) ** 2 for i in range(n))
        r_squared = 1 - (ss_res / ss_tot) if ss_tot != 0 else 0
        
        return {
            'slope': slope,
            'intercept': intercept,
            'r_squared': r_squared,
            'correlation': StatisticalAnalyzer.correlation_coefficient(x, y)
        }
    
    @staticmethod
    def outlier_detection(data: List[float], method: str = 'iqr') -> List[int]:
        """
        Detect outliers in data.
        
        Args:
            data (List[float]): Input data
            method (str): Detection method ('iqr', 'zscore')
            
        Returns:
            List[int]: Indices of outliers
        """
        if not data or len(data) < 4:
            return []
        
        outlier_indices = []
        
        if method == 'iqr':
            sorted_data = sorted(data)
            n = len(sorted_data)
            q1 = sorted_data[n // 4]
            q3 = sorted_data[3 * n // 4]
            iqr = q3 - q1
            lower_bound = q1 - 1.5 * iqr
            upper_bound = q3 + 1.5 * iqr
            
            for i, value in enumerate(data):
                if value < lower_bound or value > upper_bound:
                    outlier_indices.append(i)
        
        elif method == 'zscore':
            mean_val = statistics.mean(data)
            std_val = statistics.stdev(data) if len(data) > 1 else 0
            
            if std_val > 0:
                for i, value in enumerate(data):
                    z_score = abs((value - mean_val) / std_val)
                    if z_score > 3:  # 3-sigma rule
                        outlier_indices.append(i)
        
        return outlier_indices
    
    @staticmethod
    def frequency_analysis(data: List[Any]) -> Dict[Any, int]:
        """
        Perform frequency analysis on data.
        
        Args:
            data (List[Any]): Input data
            
        Returns:
            Dict[Any, int]: Frequency distribution
        """
        return dict(Counter(data))
    
    @staticmethod
    def confidence_interval(data: List[float], confidence: float = 0.95) -> Tuple[float, float]:
        """
        Calculate confidence interval for the mean.
        
        Args:
            data (List[float]): Input data
            confidence (float): Confidence level (0-1)
            
        Returns:
            Tuple[float, float]: Lower and upper bounds
        """
        if len(data) < 2:
            return (0.0, 0.0)
        
        mean_val = statistics.mean(data)
        std_val = statistics.stdev(data)
        n = len(data)
        
        # Using t-distribution approximation
        # For simplicity, using 1.96 for 95% confidence (normal approximation)
        z_score = 1.96 if confidence == 0.95 else 2.576 if confidence == 0.99 else 1.645
        margin_error = z_score * (std_val / math.sqrt(n))
        
        return (mean_val - margin_error, mean_val + margin_error)

class DataVisualizer:
    """
    Data visualization utilities (text-based for console output).
    """
    
    @staticmethod
    def create_histogram(data: List[float], bins: int = 10, width: int = 50) -> str:
        """
        Create a text-based histogram.
        
        Args:
            data (List[float]): Input data
            bins (int): Number of bins
            width (int): Width of the histogram
            
        Returns:
            str: Text histogram
        """
        if not data:
            return "No data to display"
        
        min_val = min(data)
        max_val = max(data)
        bin_width = (max_val - min_val) / bins
        
        # Create bins
        bin_counts = [0] * bins
        for value in data:
            bin_index = min(int((value - min_val) / bin_width), bins - 1)
            bin_counts[bin_index] += 1
        
        # Create histogram
        max_count = max(bin_counts) if bin_counts else 1
        histogram = []
        
        for i, count in enumerate(bin_counts):
            bin_start = min_val + i * bin_width
            bin_end = bin_start + bin_width
            bar_length = int((count / max_count) * width) if max_count > 0 else 0
            bar = '█' * bar_length
            histogram.append(f"{bin_start:6.2f}-{bin_end:6.2f} |{bar} ({count})")
        
        return '\n'.join(histogram)
    
    @staticmethod
    def create_bar_chart(data: Dict[str, int], width: int = 50) -> str:
        """
        Create a text-based bar chart.
        
        Args:
            data (Dict[str, int]): Data to plot
            width (int): Width of the chart
            
        Returns:
            str: Text bar chart
        """
        if not data:
            return "No data to display"
        
        max_value = max(data.values()) if data else 1
        chart = []
        
        for label, value in sorted(data.items(), key=lambda x: x[1], reverse=True):
            bar_length = int((value / max_value) * width) if max_value > 0 else 0
            bar = '█' * bar_length
            chart.append(f"{label:15} |{bar} ({value})")
        
        return '\n'.join(chart)
    
    @staticmethod
    def create_scatter_plot(x: List[float], y: List[float], width: int = 50, height: int = 20) -> str:
        """
        Create a text-based scatter plot.
        
        Args:
            x (List[float]): X coordinates
            y (List[float]): Y coordinates
            width (int): Plot width
            height (int): Plot height
            
        Returns:
            str: Text scatter plot
        """
        if not x or not y or len(x) != len(y):
            return "Invalid data for scatter plot"
        
        min_x, max_x = min(x), max(x)
        min_y, max_y = min(y), max(y)
        
        # Create grid
        grid = [[' ' for _ in range(width)] for _ in range(height)]
        
        # Plot points
        for xi, yi in zip(x, y):
            plot_x = int(((xi - min_x) / (max_x - min_x)) * (width - 1)) if max_x != min_x else 0
            plot_y = int(((yi - min_y) / (max_y - min_y)) * (height - 1)) if max_y != min_y else 0
            plot_y = height - 1 - plot_y  # Flip Y axis
            
            if 0 <= plot_x < width and 0 <= plot_y < height:
                grid[plot_y][plot_x] = '*'
        
        # Convert grid to string
        plot_lines = []
        for row in grid:
            plot_lines.append(''.join(row))
        
        # Add axes labels
        plot_lines.append('-' * width)
        plot_lines.append(f"X: {min_x:.2f} to {max_x:.2f}")
        plot_lines.append(f"Y: {min_y:.2f} to {max_y:.2f}")
        
        return '\n'.join(plot_lines)

class MLHelpers:
    """
    Machine learning helper utilities.
    """
    
    @staticmethod
    def train_test_split(data: List[Any], test_size: float = 0.2, random_seed: int = None) -> Tuple[List[Any], List[Any]]:
        """
        Split data into training and testing sets.
        
        Args:
            data (List[Any]): Input data
            test_size (float): Proportion of data for testing
            random_seed (int): Random seed for reproducibility
            
        Returns:
            Tuple[List[Any], List[Any]]: Training and testing data
        """
        if random_seed is not None:
            random.seed(random_seed)
        
        data_copy = data.copy()
        random.shuffle(data_copy)
        
        split_index = int(len(data_copy) * (1 - test_size))
        train_data = data_copy[:split_index]
        test_data = data_copy[split_index:]
        
        return train_data, test_data
    
    @staticmethod
    def normalize_features(data: List[List[float]]) -> Tuple[List[List[float]], List[float], List[float]]:
        """
        Normalize features using min-max scaling.
        
        Args:
            data (List[List[float]]): Feature matrix
            
        Returns:
            Tuple[List[List[float]], List[float], List[float]]: Normalized data, min values, max values
        """
        if not data or not data[0]:
            return data, [], []
        
        num_features = len(data[0])
        min_vals = [float('inf')] * num_features
        max_vals = [float('-inf')] * num_features
        
        # Find min and max for each feature
        for row in data:
            for i, value in enumerate(row):
                min_vals[i] = min(min_vals[i], value)
                max_vals[i] = max(max_vals[i], value)
        
        # Normalize data
        normalized_data = []
        for row in data:
            normalized_row = []
            for i, value in enumerate(row):
                if max_vals[i] != min_vals[i]:
                    normalized_value = (value - min_vals[i]) / (max_vals[i] - min_vals[i])
                else:
                    normalized_value = 0.0
                normalized_row.append(normalized_value)
            normalized_data.append(normalized_row)
        
        return normalized_data, min_vals, max_vals
    
    @staticmethod
    def standardize_features(data: List[List[float]]) -> Tuple[List[List[float]], List[float], List[float]]:
        """
        Standardize features using z-score normalization.
        
        Args:
            data (List[List[float]]): Feature matrix
            
        Returns:
            Tuple[List[List[float]], List[float], List[float]]: Standardized data, means, standard deviations
        """
        if not data or not data[0]:
            return data, [], []
        
        num_features = len(data[0])
        means = [0.0] * num_features
        stds = [0.0] * num_features
        
        # Calculate means
        for i in range(num_features):
            feature_values = [row[i] for row in data]
            means[i] = statistics.mean(feature_values)
            stds[i] = statistics.stdev(feature_values) if len(feature_values) > 1 else 1.0
        
        # Standardize data
        standardized_data = []
        for row in data:
            standardized_row = []
            for i, value in enumerate(row):
                if stds[i] != 0:
                    standardized_value = (value - means[i]) / stds[i]
                else:
                    standardized_value = 0.0
                standardized_row.append(standardized_value)
            standardized_data.append(standardized_row)
        
        return standardized_data, means, stds
    
    @staticmethod
    def euclidean_distance(point1: List[float], point2: List[float]) -> float:
        """
        Calculate Euclidean distance between two points.
        
        Args:
            point1 (List[float]): First point
            point2 (List[float]): Second point
            
        Returns:
            float: Euclidean distance
        """
        if len(point1) != len(point2):
            return float('inf')
        
        return math.sqrt(sum((p1 - p2) ** 2 for p1, p2 in zip(point1, point2)))
    
    @staticmethod
    def manhattan_distance(point1: List[float], point2: List[float]) -> float:
        """
        Calculate Manhattan distance between two points.
        
        Args:
            point1 (List[float]): First point
            point2 (List[float]): Second point
            
        Returns:
            float: Manhattan distance
        """
        if len(point1) != len(point2):
            return float('inf')
        
        return sum(abs(p1 - p2) for p1, p2 in zip(point1, point2))
    
    @staticmethod
    def k_means_clustering(data: List[List[float]], k: int, max_iterations: int = 100) -> Tuple[List[int], List[List[float]]]:
        """
        Perform K-means clustering.
        
        Args:
            data (List[List[float]]): Data points
            k (int): Number of clusters
            max_iterations (int): Maximum iterations
            
        Returns:
            Tuple[List[int], List[List[float]]]: Cluster assignments and centroids
        """
        if not data or k <= 0 or k > len(data):
            return [], []
        
        num_features = len(data[0])
        
        # Initialize centroids randomly
        centroids = []
        for _ in range(k):
            centroid = [random.uniform(min(row[i] for row in data), 
                                     max(row[i] for row in data)) for i in range(num_features)]
            centroids.append(centroid)
        
        for iteration in range(max_iterations):
            # Assign points to clusters
            assignments = []
            for point in data:
                distances = [MLHelpers.euclidean_distance(point, centroid) for centroid in centroids]
                closest_cluster = distances.index(min(distances))
                assignments.append(closest_cluster)
            
            # Update centroids
            new_centroids = []
            for cluster_id in range(k):
                cluster_points = [data[i] for i, assignment in enumerate(assignments) if assignment == cluster_id]
                if cluster_points:
                    new_centroid = [statistics.mean(point[i] for point in cluster_points) for i in range(num_features)]
                    new_centroids.append(new_centroid)
                else:
                    new_centroids.append(centroids[cluster_id])  # Keep old centroid if no points assigned
            
            # Check for convergence
            if all(MLHelpers.euclidean_distance(old, new) < 1e-6 
                  for old, new in zip(centroids, new_centroids)):
                break
            
            centroids = new_centroids
        
        return assignments, centroids

class ReportGenerator:
    """
    Report generation utilities.
    """
    
    def __init__(self):
        self.report_sections = []
    
    def add_section(self, title: str, content: str, section_type: str = 'text') -> None:
        """
        Add a section to the report.
        
        Args:
            title (str): Section title
            content (str): Section content
            section_type (str): Type of section ('text', 'table', 'chart')
        """
        self.report_sections.append({
            'title': title,
            'content': content,
            'type': section_type,
            'timestamp': datetime.datetime.now().isoformat()
        })
    
    def generate_summary_statistics_report(self, data: List[float], title: str = "Summary Statistics") -> None:
        """
        Generate a summary statistics report section.
        
        Args:
            data (List[float]): Numeric data
            title (str): Report title
        """
        stats = StatisticalAnalyzer.descriptive_statistics(data)
        
        content = []
        content.append(f"Dataset Size: {stats.get('count', 0)}")
        content.append(f"Mean: {stats.get('mean', 0):.4f}")
        content.append(f"Median: {stats.get('median', 0):.4f}")
        content.append(f"Standard Deviation: {stats.get('std_dev', 0):.4f}")
        content.append(f"Minimum: {stats.get('min', 0):.4f}")
        content.append(f"Maximum: {stats.get('max', 0):.4f}")
        content.append(f"Range: {stats.get('range', 0):.4f}")
        content.append(f"Q1: {stats.get('q1', 0):.4f}")
        content.append(f"Q3: {stats.get('q3', 0):.4f}")
        content.append(f"IQR: {stats.get('iqr', 0):.4f}")
        
        self.add_section(title, '\n'.join(content), 'text')
    
    def generate_frequency_report(self, data: List[Any], title: str = "Frequency Analysis") -> None:
        """
        Generate a frequency analysis report section.
        
        Args:
            data (List[Any]): Data for frequency analysis
            title (str): Report title
        """
        frequencies = StatisticalAnalyzer.frequency_analysis(data)
        chart = DataVisualizer.create_bar_chart(frequencies)
        
        content = []
        content.append("Frequency Distribution:")
        content.append(chart)
        content.append("\nTop 5 Most Frequent:")
        
        sorted_freq = sorted(frequencies.items(), key=lambda x: x[1], reverse=True)[:5]
        for item, count in sorted_freq:
            content.append(f"{item}: {count}")
        
        self.add_section(title, '\n'.join(content), 'chart')
    
    def export_report(self, filename: str, format_type: str = 'txt') -> bool:
        """
        Export the report to a file.
        
        Args:
            filename (str): Output filename
            format_type (str): Export format ('txt', 'json')
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            if format_type == 'txt':
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write("DATA ANALYSIS REPORT\n")
                    f.write("=" * 50 + "\n\n")
                    
                    for section in self.report_sections:
                        f.write(f"{section['title']}\n")
                        f.write("-" * len(section['title']) + "\n")
                        f.write(f"{section['content']}\n\n")
                    
                    f.write(f"\nReport generated on: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            
            elif format_type == 'json':
                report_data = {
                    'title': 'Data Analysis Report',
                    'generated_at': datetime.datetime.now().isoformat(),
                    'sections': self.report_sections
                }
                
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(report_data, f, indent=2, ensure_ascii=False)
            
            return True
        
        except Exception as e:
            logger.error(f"Failed to export report: {e}")
            return False
    
    def get_report_text(self) -> str:
        """
        Get the complete report as text.
        
        Returns:
            str: Complete report text
        """
        report_lines = []
        report_lines.append("DATA ANALYSIS REPORT")
        report_lines.append("=" * 50)
        report_lines.append("")
        
        for section in self.report_sections:
            report_lines.append(section['title'])
            report_lines.append("-" * len(section['title']))
            report_lines.append(section['content'])
            report_lines.append("")
        
        report_lines.append(f"Report generated on: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        return '\n'.join(report_lines)

def generate_sample_dataset(size: int, num_features: int = 3, noise_level: float = 0.1) -> List[List[float]]:
    """
    Generate a sample dataset for testing.
    
    Args:
        size (int): Number of samples
        num_features (int): Number of features
        noise_level (float): Amount of noise to add
        
    Returns:
        List[List[float]]: Generated dataset
    """
    dataset = []
    for _ in range(size):
        sample = []
        for feature in range(num_features):
            # Generate correlated features with some noise
            base_value = random.uniform(0, 10)
            noise = random.uniform(-noise_level, noise_level)
            value = base_value + noise
            sample.append(value)
        dataset.append(sample)
    
    return dataset

def main():
    """
    Main function demonstrating the data analysis toolkit.
    """
    print("Python Data Analysis and Visualization Toolkit Demo")
    print("=" * 55)
    
    # Generate sample data
    print("\nGenerating sample dataset...")
    sample_data = generate_sample_dataset(100, 3, 0.5)
    feature_1 = [row[0] for row in sample_data]
    feature_2 = [row[1] for row in sample_data]
    feature_3 = [row[2] for row in sample_data]
    
    # Data cleaning demo
    print("\nData Cleaning Demo:")
    cleaner = DataCleaner()
    
    # Create some messy data
    messy_data = [
        {'name': 'Alice', 'age': '30', 'salary': '50000'},
        {'name': 'Bob', 'age': '', 'salary': '60000'},
        {'name': 'Charlie', 'age': '35', 'salary': 'N/A'},
        {'name': 'Alice', 'age': '30', 'salary': '50000'},  # Duplicate
        {'name': 'Diana', 'age': '28', 'salary': '55000'}
    ]
    
    print(f"Original data: {len(messy_data)} records")
    
    # Remove duplicates
    clean_data = cleaner.remove_duplicates(messy_data)
    print(f"After removing duplicates: {len(clean_data)} records")
    
    # Handle missing values
    clean_data = cleaner.handle_missing_values(clean_data, strategy='fill', fill_value='0')
    
    # Validate data types
    type_schema = {'age': 'int', 'salary': 'int'}
    valid_data, invalid_data = cleaner.validate_data_types(clean_data, type_schema)
    print(f"Valid records: {len(valid_data)}, Invalid records: {len(invalid_data)}")
    
    # Statistical analysis demo
    print("\nStatistical Analysis Demo:")
    analyzer = StatisticalAnalyzer()
    
    stats = analyzer.descriptive_statistics(feature_1)
    print(f"Feature 1 statistics:")
    for key, value in stats.items():
        if isinstance(value, float):
            print(f"  {key}: {value:.4f}")
        else:
            print(f"  {key}: {value}")
    
    # Correlation analysis
    correlation = analyzer.correlation_coefficient(feature_1, feature_2)
    print(f"\nCorrelation between Feature 1 and 2: {correlation:.4f}")
    
    # Linear regression
    regression = analyzer.linear_regression(feature_1, feature_2)
    print(f"Linear regression results:")
    for key, value in regression.items():
        print(f"  {key}: {value:.4f}")
    
    # Outlier detection
    outliers = analyzer.outlier_detection(feature_1, method='iqr')
    print(f"\nOutliers detected (IQR method): {len(outliers)} points")
    
    # Visualization demo
    print("\nVisualization Demo:")
    visualizer = DataVisualizer()
    
    print("\nHistogram of Feature 1:")
    histogram = visualizer.create_histogram(feature_1, bins=8, width=40)
    print(histogram)
    
    # Frequency analysis
    categorical_data = ['A', 'B', 'A', 'C', 'B', 'A', 'D', 'B', 'C', 'A']
    frequencies = analyzer.frequency_analysis(categorical_data)
    print("\nFrequency Bar Chart:")
    bar_chart = visualizer.create_bar_chart(frequencies, width=30)
    print(bar_chart)
    
    print("\nScatter Plot (Feature 1 vs Feature 2):")
    scatter_plot = visualizer.create_scatter_plot(feature_1[:20], feature_2[:20], width=40, height=15)
    print(scatter_plot)
    
    # Machine learning helpers demo
    print("\nMachine Learning Helpers Demo:")
    ml_helpers = MLHelpers()
    
    # Train-test split
    train_data, test_data = ml_helpers.train_test_split(sample_data, test_size=0.3, random_seed=42)
    print(f"Train set size: {len(train_data)}, Test set size: {len(test_data)}")
    
    # Feature normalization
    normalized_data, min_vals, max_vals = ml_helpers.normalize_features(sample_data[:10])
    print(f"Normalized first sample: {[f'{x:.3f}' for x in normalized_data[0]]}")
    
    # Feature standardization
    standardized_data, means, stds = ml_helpers.standardize_features(sample_data[:10])
    print(f"Standardized first sample: {[f'{x:.3f}' for x in standardized_data[0]]}")
    
    # K-means clustering
    assignments, centroids = ml_helpers.k_means_clustering(sample_data[:20], k=3)
    print(f"\nK-means clustering (k=3):")
    cluster_counts = Counter(assignments)
    for cluster_id, count in cluster_counts.items():
        print(f"  Cluster {cluster_id}: {count} points")
    
    # Distance calculations
    point1 = [1.0, 2.0, 3.0]
    point2 = [4.0, 5.0, 6.0]
    euclidean_dist = ml_helpers.euclidean_distance(point1, point2)
    manhattan_dist = ml_helpers.manhattan_distance(point1, point2)
    print(f"\nDistance between {point1} and {point2}:")
    print(f"  Euclidean: {euclidean_dist:.4f}")
    print(f"  Manhattan: {manhattan_dist:.4f}")
    
    # Report generation demo
    print("\nReport Generation Demo:")
    report_gen = ReportGenerator()
    
    # Add summary statistics section
    report_gen.generate_summary_statistics_report(feature_1, "Feature 1 Analysis")
    
    # Add frequency analysis section
    report_gen.generate_frequency_report(categorical_data, "Categorical Data Analysis")
    
    # Add custom section
    custom_content = f"""This analysis was performed on a dataset with {len(sample_data)} samples.
The correlation between Feature 1 and Feature 2 is {correlation:.4f}.
Outliers detected: {len(outliers)} points using IQR method."""
    
    report_gen.add_section("Analysis Summary", custom_content, "text")
    
    # Export report
    report_filename = "analysis_report.txt"
    success = report_gen.export_report(report_filename, 'txt')
    if success:
        print(f"Report exported to: {report_filename}")
    
    # Display report preview
    print("\nReport Preview:")
    print("-" * 50)
    report_text = report_gen.get_report_text()
    # Show first 500 characters of the report
    print(report_text[:500] + "..." if len(report_text) > 500 else report_text)
    
    print("\nDemo completed successfully!")
    print(f"Total samples processed: {len(sample_data)}")
    print(f"Features analyzed: {len(sample_data[0]) if sample_data else 0}")
    print(f"Cleaning operations: {len(cleaner.transformations_applied)}")
    print(f"Report sections generated: {len(report_gen.report_sections)}")

if __name__ == "__main__":
    main()