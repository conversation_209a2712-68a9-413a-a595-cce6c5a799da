#!/usr/bin/env python3
"""
Comprehensive Integration Demo for GenerateLargeFileTool

This script demonstrates how the GenerateLargeFileTool integrates seamlessly
with all other components of the MindLink library including:
- Agent OS and planning
- Terminal/CLI interface
- File operations
- Tool registry
- LLM integration
- Error handling
"""

import sys
import os
import json
import time
from pathlib import Path
from typing import Dict, Any, List

# Add the project root to the path
sys.path.append('.')

from mindlink.agent import AgentOS
from mindlink.models.openrouter import OpenRouterModel
from mindlink.config import DEFAULT_SYSTEM_PROMPT
from mindlink.tools.base import tool_registry
from mindlink.tools.file_tools import (
    GenerateLargeFileTool, 
    CreateFileTool, 
    ReadFileTool, 
    <PERSON>F<PERSON>Tool,
    SearchInFilesTool
)
from mindlink.schemas.mindlink import MindLinkRequest, Action

class IntegrationDemo:
    """Comprehensive integration demonstration class."""
    
    def __init__(self, api_key: str = None):
        """Initialize the integration demo."""
        self.api_key = api_key or os.getenv('OPENROUTER_API_KEY')
        if not self.api_key:
            raise ValueError("OpenRouter API key is required. Set OPENROUTER_API_KEY environment variable.")
        
        # Initialize LLM and Agent
        self.llm = OpenRouterModel(
            api_key=self.api_key,
            model_name="deepseek/deepseek-r1",
            temperature=0.7,
            max_tokens=4000
        )
        
        self.agent = AgentOS(
            llm=self.llm,
            system_prompt_template=DEFAULT_SYSTEM_PROMPT,
            max_steps=20
        )
        
        # Initialize tools
        self.generate_tool = GenerateLargeFileTool()
        self.create_tool = CreateFileTool()
        self.read_tool = ReadFileTool()
        self.list_tool = ListFilesTool()
        self.search_tool = SearchInFilesTool()
        
        print("🚀 Integration Demo Initialized")
        print(f"📡 LLM Model: {self.llm.model_id}")
        print(f"🔧 Available Tools: {len(tool_registry)}")
        print("="*60)
    
    def demo_tool_registry_integration(self):
        """Demonstrate tool registry integration."""
        print("\n🔧 TOOL REGISTRY INTEGRATION DEMO")
        print("="*50)
        
        # Show all registered tools
        print(f"📋 Total registered tools: {len(tool_registry)}")
        for tool_name, tool_class in tool_registry.items():
            icon = "📄" if tool_name == "generate_large_file" else "⚙️"
            print(f"  {icon} {tool_name}: {tool_class.description[:60]}...")
        
        # Verify GenerateLargeFileTool is properly registered
        assert "generate_large_file" in tool_registry, "GenerateLargeFileTool not registered!"
        print("\n✅ GenerateLargeFileTool is properly registered in tool registry")
        
        # Test tool instantiation from registry
        tool_from_registry = tool_registry["generate_large_file"]()
        assert isinstance(tool_from_registry, GenerateLargeFileTool), "Tool instantiation failed!"
        print("✅ Tool can be instantiated from registry")
        
        return True
    
    def demo_direct_tool_usage(self):
        """Demonstrate direct tool usage."""
        print("\n🎯 DIRECT TOOL USAGE DEMO")
        print("="*50)
        
        # Test direct GenerateLargeFileTool usage
        print("📄 Testing GenerateLargeFileTool directly...")
        
        result = self.generate_tool.execute(
            path="demo_direct_usage.py",
            content_description="Create a Python utility script with file operations, data processing functions, and command-line interface",
            target_line_count=150,
            max_chunks=3
        )
        
        print(f"📊 Result Status: {result['status']}")
        if result['status'] == 'success':
            lines = result['result']['lines_written']
            chunks = result['result']['chunks_written']
            target_met = result['result']['target_lines_met']
            print(f"📝 Generated {lines} lines in {chunks} chunks (Target met: {target_met})")
            print(f"📁 File created: {result['result']['file_path']}")
        else:
            print(f"❌ Error: {result.get('observation', 'Unknown error')}")
        
        return result['status'] == 'success'
    
    def demo_agent_integration(self):
        """Demonstrate agent-based tool usage."""
        print("\n🤖 AGENT INTEGRATION DEMO")
        print("="*50)
        
        # Test agent planning and execution
        goal = "Create a comprehensive Python web scraper with multiple extraction methods, data processing, and export functionality. Target 500 lines of code."
        
        print(f"🎯 Goal: {goal}")
        print("🧠 Agent planning...")
        
        try:
            # Plan the task
            plan = self.agent.plan_once(goal)
            
            if plan:
                print(f"📋 Generated plan with {len(plan)} steps:")
                for i, action in enumerate(plan, 1):
                    icon = "📄" if action.tool_name == "generate_large_file" else "⚙️"
                    print(f"  {i}. {icon} {action.tool_name}")
                    if action.tool_name == "generate_large_file":
                        params = action.parameters or {}
                        target_lines = params.get('target_line_count', 'Not specified')
                        print(f"     📊 Target lines: {target_lines}")
                
                # Execute the plan
                print("\n⚡ Executing plan...")
                response = self.agent.batch_execute_plan(plan, parallel=False)
                
                print(f"📊 Execution Status: {response.status}")
                print(f"📝 Result: {response.observation[:200]}...")
                
                return response.status.lower() == "success"
            else:
                print("❌ Agent failed to generate a plan")
                return False
                
        except Exception as e:
            print(f"❌ Agent integration error: {e}")
            return False
    
    def demo_file_operations_integration(self):
        """Demonstrate integration with other file operations."""
        print("\n📁 FILE OPERATIONS INTEGRATION DEMO")
        print("="*50)
        
        # Generate a large file
        print("📄 Step 1: Generate large file...")
        gen_result = self.generate_tool.execute(
            path="integration_test_file.py",
            content_description="Create a Python module with classes for data analysis, file I/O operations, and utility functions",
            target_line_count=200,
            max_chunks=4
        )
        
        if gen_result['status'] != 'success':
            print(f"❌ Generation failed: {gen_result.get('observation')}")
            return False
        
        file_path = gen_result['result']['file_path']
        print(f"✅ Generated file: {file_path}")
        
        # Read the generated file
        print("📖 Step 2: Read generated file...")
        read_result = self.read_tool.execute(path=file_path)
        
        if read_result['status'] == 'success':
            content = read_result['observation']
            newline = '\n'
            lines = len(content.split(newline))
            print(f"✅ Read file successfully: {lines} lines")
        else:
            print(f"❌ Read failed: {read_result.get('observation')}")
            return False
        
        # Search in the generated file
        print("🔍 Step 3: Search in generated file...")
        search_result = self.search_tool.execute(
            directory=str(Path(file_path).parent),
            pattern="def |class "
        )
        
        if search_result['status'] == 'success':
            matches = search_result['observation']
            newline = '\n'
            print(f"✅ Found function/class definitions: {len(matches.split(newline))} matches")
        else:
            print(f"❌ Search failed: {search_result.get('observation')}")
        
        # List files in directory
        print("📋 Step 4: List directory contents...")
        list_result = self.list_tool.execute(directory=str(Path(file_path).parent))
        
        if list_result['status'] == 'success':
            files = list_result['observation']
            print(f"✅ Directory listing successful")
            newline = '\n'
            print(f"📁 Files found: {len(files.split(newline))} items")
        else:
            print(f"❌ List failed: {list_result.get('observation')}")
        
        return True
    
    def demo_error_handling(self):
        """Demonstrate error handling and recovery."""
        print("\n🛡️ ERROR HANDLING DEMO")
        print("="*50)
        
        # Test with invalid parameters
        print("🧪 Testing error handling with invalid parameters...")
        
        # Test 1: Missing required parameters
        result1 = self.generate_tool.execute()
        print(f"Test 1 - Missing params: {result1['status']} ({'✅' if result1['status'] == 'error' else '❌'})")
        
        # Test 2: Invalid path
        result2 = self.generate_tool.execute(
            path="/invalid/path/file.py",
            content_description="Test",
            target_line_count=50
        )
        print(f"Test 2 - Invalid path: {result2['status']} ({'✅' if result2['status'] == 'error' else '❌'})")
        
        # Test 3: Extremely large target (should handle gracefully)
        result3 = self.generate_tool.execute(
            path="large_test.py",
            content_description="Simple test file",
            target_line_count=10000,  # Very large
            max_chunks=1  # Limited chunks
        )
        print(f"Test 3 - Large target: {result3['status']} (Should handle gracefully)")
        
        return True
    
    def demo_performance_metrics(self):
        """Demonstrate performance monitoring."""
        print("\n📊 PERFORMANCE METRICS DEMO")
        print("="*50)
        
        # Test different file sizes and measure performance
        test_cases = [
            {"lines": 50, "chunks": 1, "desc": "Small utility script"},
            {"lines": 200, "chunks": 3, "desc": "Medium application module"},
            {"lines": 500, "chunks": 5, "desc": "Large comprehensive system"}
        ]
        
        results = []
        
        for i, case in enumerate(test_cases, 1):
            print(f"\n🧪 Test Case {i}: {case['lines']} lines, {case['chunks']} chunks")
            
            start_time = time.time()
            result = self.generate_tool.execute(
                path=f"perf_test_{i}.py",
                content_description=f"Create a Python application: {case['desc']} with comprehensive functionality",
                target_line_count=case['lines'],
                max_chunks=case['chunks']
            )
            end_time = time.time()
            
            duration = end_time - start_time
            
            if result['status'] == 'success':
                actual_lines = result['result']['lines_written']
                actual_chunks = result['result']['chunks_written']
                efficiency = (actual_lines / duration) if duration > 0 else 0
                
                results.append({
                    'target_lines': case['lines'],
                    'actual_lines': actual_lines,
                    'chunks': actual_chunks,
                    'duration': duration,
                    'efficiency': efficiency
                })
                
                print(f"  ✅ Generated {actual_lines} lines in {duration:.2f}s ({efficiency:.1f} lines/sec)")
            else:
                print(f"  ❌ Failed: {result.get('observation')}")
        
        # Summary
        if results:
            avg_efficiency = sum(r['efficiency'] for r in results) / len(results)
            print(f"\n📈 Average Performance: {avg_efficiency:.1f} lines/second")
        
        return len(results) > 0
    
    def run_full_demo(self):
        """Run the complete integration demonstration."""
        print("🎬 STARTING COMPREHENSIVE INTEGRATION DEMO")
        print("="*60)
        
        demos = [
            ("Tool Registry Integration", self.demo_tool_registry_integration),
            ("Direct Tool Usage", self.demo_direct_tool_usage),
            ("Agent Integration", self.demo_agent_integration),
            ("File Operations Integration", self.demo_file_operations_integration),
            ("Error Handling", self.demo_error_handling),
            ("Performance Metrics", self.demo_performance_metrics)
        ]
        
        results = {}
        
        for demo_name, demo_func in demos:
            print(f"\n🎯 Running: {demo_name}")
            try:
                success = demo_func()
                results[demo_name] = success
                status = "✅ PASSED" if success else "❌ FAILED"
                print(f"📊 {demo_name}: {status}")
            except Exception as e:
                results[demo_name] = False
                print(f"💥 {demo_name}: ERROR - {e}")
        
        # Final summary
        print("\n" + "="*60)
        print("🏁 INTEGRATION DEMO SUMMARY")
        print("="*60)
        
        passed = sum(1 for success in results.values() if success)
        total = len(results)
        
        for demo_name, success in results.items():
            status = "✅" if success else "❌"
            print(f"  {status} {demo_name}")
        
        print(f"\n📊 Overall Result: {passed}/{total} demos passed")
        
        if passed == total:
            print("🎉 ALL INTEGRATION TESTS PASSED!")
            print("🚀 GenerateLargeFileTool is fully integrated with the library!")
        else:
            print("⚠️  Some integration issues detected.")
        
        return passed == total

def main():
    """Main entry point for the integration demo."""
    try:
        # Initialize demo
        demo = IntegrationDemo()
        
        # Run full demonstration
        success = demo.run_full_demo()
        
        # Exit with appropriate code
        sys.exit(0 if success else 1)
        
    except Exception as e:
        print(f"💥 Demo initialization failed: {e}")
        print("\n💡 Make sure to:")
        print("  1. Set OPENROUTER_API_KEY environment variable")
        print("  2. Install all required dependencies")
        print("  3. Run from the project root directory")
        sys.exit(1)

if __name__ == "__main__":
    main()