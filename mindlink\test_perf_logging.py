import os
import sys
import shutil
import uuid

# Ensure module path
sys.path.insert(0, os.path.dirname(__file__))

from logging_utils import log_event, close_logs, create_user_input_log, LOGS_DIR

# Clean logs dir
def reset_logs():
    if os.path.exists(LOGS_DIR):
        shutil.rmtree(LOGS_DIR)
    os.makedirs(LOGS_DIR)

if __name__ == '__main__':
    reset_logs()
    session_id = str(uuid.uuid4())
    # Generate 1000 events
    for i in range(1000):
        ev = create_user_input_log(session_id, f"message_{i}", intent="perf")
        log_event(ev)
    close_logs()
    files = sorted(os.listdir(LOGS_DIR))
    print("Generated files:", files)
    # Print summary of events count
    json_path = os.path.join(LOGS_DIR, files[0])
    with open(json_path, 'r', encoding='utf-8') as f:
        data = f.read()
    count = data.count('{"event_id"')
    print(f"Total events in JSON: {count}")
    # Show first and last event snippets
    lines = data.splitlines()
    print("First event:\n", lines[1])
    print("Last event:\n", lines[-2])
