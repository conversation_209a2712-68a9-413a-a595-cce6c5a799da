#!/usr/bin/env python3
"""
Test script to reproduce and fix the 402 Payment Required error.
This test will verify that the system uses the free Mistral model correctly.
"""

import os
import sys
import json
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_environment_configuration():
    """Test 1: Verify environment variables are set correctly"""
    print("\n=== Test 1: Environment Configuration ===")
    
    # Check .env file
    env_file = Path('.env')
    if env_file.exists():
        with open(env_file, 'r') as f:
            env_content = f.read()
        print(f"✓ .env file exists")
        print(f"Content:\n{env_content}")
    else:
        print("✗ .env file not found")
        return False
    
    # Check environment variables
    openrouter_key = os.environ.get('OPENROUTER_API_KEY')
    mindlink_provider = os.environ.get('MINDLINK_PROVIDER')
    mindlink_model = os.environ.get('MINDLINK_MODEL')
    
    print(f"OPENROUTER_API_KEY: {'Set' if openrouter_key else 'Not set'}")
    print(f"MINDLINK_PROVIDER: {mindlink_provider}")
    print(f"MINDLINK_MODEL: {mindlink_model}")
    
    if not openrouter_key:
        print("✗ OPENROUTER_API_KEY not set")
        return False
    if mindlink_provider != 'openrouter':
        print("✗ MINDLINK_PROVIDER should be 'openrouter'")
        return False
    if mindlink_model != 'mistral-small-3.1':
        print("✗ MINDLINK_MODEL should be 'mistral-small-3.1'")
        return False
    
    print("✓ Environment configuration looks correct")
    return True

def test_model_config_files():
    """Test 2: Verify model configuration files"""
    print("\n=== Test 2: Model Configuration Files ===")
    
    # Check .model_config.json
    model_config_file = Path('.model_config.json')
    if model_config_file.exists():
        with open(model_config_file, 'r') as f:
            model_config = json.load(f)
        print(f"✓ .model_config.json exists")
        print(f"Primary model: {model_config.get('primary_model')}")
        print(f"Active models: {model_config.get('active_models')}")
        
        if model_config.get('primary_model') != 'mistral-small-3.1':
            print("✗ Primary model should be 'mistral-small-3.1'")
            return False
        if 'mistral-small-3.1' not in model_config.get('active_models', []):
            print("✗ 'mistral-small-3.1' should be in active_models")
            return False
    else:
        print("✗ .model_config.json not found")
        return False
    
    print("✓ Model configuration files look correct")
    return True

def test_openrouter_model_mapping():
    """Test 3: Verify OpenRouter model mapping uses free tier"""
    print("\n=== Test 3: OpenRouter Model Mapping ===")
    
    try:
        from mindlink.models.openrouter import OPENROUTER_MODELS
        
        if 'mistral-small-3.1' in OPENROUTER_MODELS:
            model_config = OPENROUTER_MODELS['mistral-small-3.1']
            model_id = model_config.get('model_id')
            print(f"✓ Found mistral-small-3.1 in MODELS")
            print(f"Model ID: {model_id}")
            
            if model_id == 'mistralai/mistral-small-3.1-24b-instruct:free':
                print("✓ Model ID correctly points to free tier")
                return True
            else:
                print(f"✗ Model ID should be 'mistralai/mistral-small-3.1-24b-instruct:free', got '{model_id}'")
                return False
        else:
            print("✗ 'mistral-small-3.1' not found in OPENROUTER_MODELS")
            return False
    except ImportError as e:
        print(f"✗ Failed to import OpenRouter models: {e}")
        return False

def test_agent_initialization():
    """Test 4: Try to initialize agent and check for payment errors"""
    print("\n=== Test 4: Agent Initialization ===")
    
    try:
        from mindlink.agent import AgentOS
        from mindlink.models.openrouter import OpenRouterModel
        from mindlink.config import DEFAULT_SYSTEM_PROMPT
        
        print("Attempting to create OpenRouter LLM...")
        api_key = os.environ.get('OPENROUTER_API_KEY')
        llm = OpenRouterModel(api_key=api_key, model_name='mistral-small-3.1')
        
        print("Attempting to initialize agent...")
        agent = AgentOS(
            llm=llm,
            system_prompt_template=DEFAULT_SYSTEM_PROMPT,
            max_steps=5
        )
        
        print(f"✓ Agent initialized successfully")
        print(f"Agent LLM type: {type(agent.llm).__name__}")
        
        if hasattr(agent.llm, 'model_id'):
            print(f"Model ID: {agent.llm.model_id}")
            if ':free' in agent.llm.model_id:
                print("✓ Agent is using free tier model")
                return True
            else:
                print(f"✗ Agent model ID '{agent.llm.model_id}' does not indicate free tier")
                return False
        else:
            print("✗ Agent LLM does not have model_id attribute")
            return False
            
    except Exception as e:
        print(f"✗ Failed to initialize agent: {e}")
        return False

def test_simple_llm_call():
    """Test 5: Make a simple LLM call to check for 402 errors"""
    print("\n=== Test 5: Simple LLM Call ===")
    
    try:
        from mindlink.models.openrouter import OpenRouterModel
        
        print("Creating OpenRouter LLM for direct test...")
        api_key = os.environ.get('OPENROUTER_API_KEY')
        llm = OpenRouterModel(api_key=api_key, model_name='mistral-small-3.1')
        
        # Try a simple call that should not trigger payment errors
        print("Making a simple LLM call...")
        response = llm.generate(
            system_prompt="You are a helpful assistant.",
            user_prompt="Say 'Hello, this is a test'"
        )
        
        print(f"✓ LLM call successful")
        print(f"Response: {response[:100]}..." if len(response) > 100 else f"Response: {response}")
        return True
        
    except Exception as e:
        error_str = str(e)
        if '402' in error_str or 'Payment Required' in error_str:
            print(f"✗ 402 Payment Required error still occurring: {e}")
            return False
        else:
            print(f"✗ Other error occurred: {e}")
            return False

def main():
    """Run all tests and report results"""
    print("🔍 Testing Payment Error Fix")
    print("=" * 50)
    
    tests = [
        test_environment_configuration,
        test_model_config_files,
        test_openrouter_model_mapping,
        test_agent_initialization,
        test_simple_llm_call
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"✗ Test {test.__name__} failed with exception: {e}")
            results.append(False)
    
    print("\n" + "=" * 50)
    print("📊 Test Results Summary:")
    for i, (test, result) in enumerate(zip(tests, results), 1):
        status = "✓ PASS" if result else "✗ FAIL"
        print(f"Test {i} ({test.__name__}): {status}")
    
    passed = sum(results)
    total = len(results)
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Payment error should be resolved.")
    else:
        print("❌ Some tests failed. Payment error still exists.")
        print("\n🔧 Next steps:")
        if not results[0]:
            print("- Fix environment configuration")
        if not results[1]:
            print("- Fix model configuration files")
        if not results[2]:
            print("- Fix OpenRouter model mapping")
        if not results[3]:
            print("- Fix agent initialization")
        if not results[4]:
            print("- Investigate LLM call errors")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)