import os
import sys
import shutil
import uuid

# Ensure module path
sys.path.insert(0, os.path.dirname(__file__))

from logging_utils import create_user_input_log, log_event, close_logs, LOGS_DIR

# Reset logs
def reset_logs():
    if os.path.exists(LOGS_DIR):
        shutil.rmtree(LOGS_DIR)
    os.makedirs(LOGS_DIR)

if __name__ == '__main__':
    reset_logs()
    # Session 1
    sid1 = str(uuid.uuid4())
    ev1 = create_user_input_log(sid1, "first session input", intent="test1")
    log_event(ev1)
    close_logs()
    # Session 2
    sid2 = str(uuid.uuid4())
    ev2 = create_user_input_log(sid2, "second session input", intent="test2")
    log_event(ev2)
    close_logs()
    # List files
    files = sorted(os.listdir(LOGS_DIR))
    print("Log files after two sessions:", files)
