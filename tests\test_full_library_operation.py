"""
Comprehensive test to verify that the entire library is fully operational.

This test exercises all the main components of the library to ensure
that they work together correctly.
"""

import pytest
import os
import tempfile
import shutil
import json
from pathlib import Path

# Import all the main components of the library
from mindlink.tools.file_tools import (
    TransactionManager,
    create_file,
    read_file,
    list_files,
    path_exists,
    CreateFileTool,
    ReadFileTool,
    ListFilesTool
)
from mindlink.tools.graph_tools import GenerateCallGraphTool
from mindlink.tools.base import Tool, tool_registry


@pytest.fixture
def test_project_dir():
    """Create a temporary directory with a simple project structure for testing."""
    temp_dir = tempfile.mkdtemp()
    
    # Create a simple project structure
    os.makedirs(os.path.join(temp_dir, "src"), exist_ok=True)
    os.makedirs(os.path.join(temp_dir, "tests"), exist_ok=True)
    os.makedirs(os.path.join(temp_dir, "docs"), exist_ok=True)
    
    # Create some Python files
    create_file(os.path.join(temp_dir, "src", "main.py"), """
def main():
    print("Hello, world!")
    helper_function()
    return True

def helper_function():
    result = calculate_value(42)
    print(f"Result: {result}")
    return result

def calculate_value(x):
    return x * 2
""")
    
    create_file(os.path.join(temp_dir, "src", "utils.py"), """
def format_string(s):
    return s.strip().upper()

def parse_int(s):
    try:
        return int(s)
    except ValueError:
        return None
""")
    
    create_file(os.path.join(temp_dir, "tests", "test_main.py"), """
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.main import main, helper_function, calculate_value

def test_main():
    assert main() == True

def test_helper_function():
    assert helper_function() == 84

def test_calculate_value():
    assert calculate_value(10) == 20
    assert calculate_value(0) == 0
    assert calculate_value(-5) == -10
""")
    
    yield temp_dir
    
    # Clean up
    shutil.rmtree(temp_dir)


def test_full_library_operation(test_project_dir):
    """
    Test that all components of the library work together correctly.
    """
    # Test file operations with transactions
    with TransactionManager():
        # Create a new file
        readme_path = os.path.join(test_project_dir, "README.md")
        create_file(readme_path, "# Test Project\n\nThis is a test project.")
        
        # Verify the file was created
        assert path_exists(readme_path)
        assert read_file(readme_path) == "# Test Project\n\nThis is a test project."
        
        # List files in the project directory
        files = list_files(test_project_dir)
        assert "src/" in files
        assert "tests/" in files
        assert "docs/" in files
        assert "README.md" in files
    
    # Test file tools
    create_tool = CreateFileTool()
    read_tool = ReadFileTool()
    list_tool = ListFilesTool()
    
    # Create a file using the tool
    config_path = os.path.join(test_project_dir, "config.json")
    config_content = json.dumps({"name": "test-project", "version": "1.0.0"}, indent=2)
    result = create_tool.execute(path=config_path, content=config_content)
    assert result["status"] == "success"
    
    # Read the file using the tool
    result = read_tool.execute(path=config_path)
    assert result["status"] == "success"
    assert json.loads(result["observation"]) == {"name": "test-project", "version": "1.0.0"}
    
    # List files using the tool
    result = list_tool.execute(directory=test_project_dir)
    assert result["status"] == "success"
    assert "config.json" in result["observation"]
    
    # Test graph tools
    graph_tool = GenerateCallGraphTool()
    main_py_path = os.path.join(test_project_dir, "src", "main.py")
    result = graph_tool.execute(path=main_py_path)
    assert result["status"] == "success"
    
    # Parse the call graph
    call_graph = json.loads(result["observation"])
    assert "main" in call_graph
    assert "helper_function" in call_graph["main"]
    assert "calculate_value" in call_graph["helper_function"]
    
    # Test tool registry
    assert "create_file" in tool_registry
    assert "read_file" in tool_registry
    assert "list_files" in tool_registry
    assert "generate_call_graph" in tool_registry
    
    # Create a tool from the registry
    registry_read_tool = tool_registry["read_file"]()
    result = registry_read_tool.execute(path=readme_path)
    assert result["status"] == "success"
    assert result["observation"] == "# Test Project\n\nThis is a test project."
    
    print("All library components are fully operational!")


if __name__ == "__main__":
    pytest.main(["-xvs", __file__])
