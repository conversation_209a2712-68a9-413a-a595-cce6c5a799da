"""Test script for JSONL logging and JSON conversion."""
import os
import uuid
from mindlink.logging_utils import (
    log_event,
    create_user_input_log,
    create_agent_plan_generated_log,
    create_tool_call_start_log,
    create_tool_call_end_log,
    create_llm_query_log,
    create_llm_response_log,
    create_agent_goal_completed_log,
    create_error_occurred_log,
    PlanStep,
    convert_logs_to_json,
    close_logs
)

def test_logging():
    """Test the logging system with sample data."""
    # Create a test session
    session_id = str(uuid.uuid4())
    print(f"Test Session ID: {session_id}")
    
    # Generate test log entries
    logs = [
        create_user_input_log(session_id, "Hello, how are you?", "greeting"),
        create_agent_plan_generated_log(
            session_id,
            [PlanStep(step_id="1", tool_name="greet_user", parameters={"message": "Hello!"}, 
                     reasoning="Responding to user greeting")],
            confidence=0.95
        ),
        create_tool_call_start_log(session_id, "greet_user", {"message": "Hello!"}),
        create_tool_call_end_log(
            session_id, 
            "greet_user", 
            {"message": "Hello!"}, 
            {"response": "Hello! How can I help you today?"}, 
            "SUCCESS", 
            150.5
        ),
        create_llm_query_log(session_id, "User said: Hello, how are you?", "gpt-4"),
        create_llm_response_log(
            session_id, 
            "User said: Hello, how are you?",
            "I'm doing well, thank you for asking! How can I assist you today?",
            "gpt-4", 
            25, 
            1200.75
        ),
        create_agent_goal_completed_log(
            session_id, 
            {"status": "success", "message": "Greeted user successfully"}
        ),
        create_error_occurred_log(
            session_id, 
            "test_component", 
            "Test error occurred", 
            "WARNING", 
            "Traceback..."
        )
    ]
    
    # Log all events
    for event in logs:
        log_event(event)
    
    print(f"Logged {len(logs)} events to session {session_id}")
    
    # Close log files
    close_logs()
    
    return session_id

def test_conversion():
    """Test JSONL to JSON conversion."""
    print("\nTesting JSONL to JSON conversion...")
    
    # Run the logging test first to generate some logs
    session_id = test_logging()
    
    # Convert the logs to JSON
    logs_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'mindlink', 'logs')
    output_dir = os.path.join(logs_dir, 'json')
    
    print(f"\nConverting logs in {logs_dir} to JSON...")
    json_files = convert_logs_to_json(logs_dir, output_dir)
    
    print("\nConversion complete. Created the following JSON files:")
    for json_file in json_files:
        print(f"- {json_file}")
    
    return json_files

if __name__ == "__main__":
    # Run the tests
    print("=== Testing JSONL Logging ===")
    session_id = test_logging()
    
    print("\n=== Testing JSON Conversion ===")
    json_files = test_conversion()
    
    print("\n=== Tests Complete ===")
    print(f"Session ID: {session_id}")
    print(f"JSON files created: {len(json_files)}")
