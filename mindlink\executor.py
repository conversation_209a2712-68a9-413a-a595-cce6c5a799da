from .schemas.mindlink import MindLinkRequest, MindLinkResponse
from .utils.sandbox import run_shell_command
from .utils.cache import cache_result
import logging

logger = logging.getLogger(__name__)

class ToolExecutor:
    """
    Executes single tool requests forwarded from a central model.
    """
    def __init__(self):
        # Initialize any state store or event hooks here
        pass

    @cache_result(ttl=60)
    def execute(self, request: MindLinkRequest) -> MindLinkResponse:
        # Import tool_registry here to avoid circular imports
        from tools.base import tool_registry

        tool_name = request.action.tool_name
        params = request.action.parameters

        if tool_name not in tool_registry:
            return MindLinkResponse(
                observation=f"Error: tool '{tool_name}' not found.",
                status="error",
                error=f"Unknown tool '{tool_name}'"
            )

        tool_cls = tool_registry[tool_name]
        tool = tool_cls()
        try:
            # For shell commands, delegate to sandbox
            if tool_name == "run_shell_command":
                result = run_shell_command(params.get("command", ""))
            else:
                if tool_name == 'create_file':
                    # Log content length for create_file, but not the content itself to avoid huge logs
                    content_param = params.get('content')
                    content_length = len(content_param) if content_param is not None else 'None'
                    logger.info(f"Executing tool '{tool_name}' with params (content length: {content_length})")
                else:
                    logger.info(f"Executing tool '{tool_name}' with params: {params}")
                result = tool.execute(**params)
            return MindLinkResponse(
                observation=result.get("observation", ""),
                status=result.get("status", "success"),
                error=result.get("error")
            )
        except Exception as e:
            return MindLinkResponse(
                observation=f"Execution error: {str(e)}",
                status="error",
                error=str(e)
            )
