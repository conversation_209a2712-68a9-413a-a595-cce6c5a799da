"""
Tests for MindLink Agent library.
"""

import unittest
import os
import sys

# Add the parent directory to the path so we can import mindlink
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from mindlink import AgentOS, OpenRouterModel, DEFAULT_SYSTEM_PROMPT
from mindlink.tools.base import Tool, register_tool


class TestLibraryImports(unittest.TestCase):
    """
    Test that the library imports work correctly.
    """

    def test_imports(self):
        """
        Test that the library imports work correctly.
        """
        # Test that we can import the main components
        self.assertIsNotNone(AgentOS)
        self.assertIsNotNone(OpenRouterModel)
        self.assertIsNotNone(DEFAULT_SYSTEM_PROMPT)
        self.assertIsNotNone(Tool)
        self.assertIsNotNone(register_tool)


if __name__ == "__main__":
    unittest.main()
