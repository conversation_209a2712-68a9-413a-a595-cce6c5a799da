import pytest
from mindlink.models.openrouter import OpenRouterModel
import mindlink.tools.graph_tools  # ensure call graph tool is registered
import mindlink.tools.doc_tools    # ensure hover doc tool is registered

@pytest.fixture(autouse=True)
def stub_openrouter_generate(monkeypatch):
    # Predetermined LLM responses to invoke tools in sequence
    responses = [
        '{"action": {"tool_name": "wrap_transaction_wrappers", "parameters": {"path": "mindlink/tools/file_tools.py"}}, "reasoning": ""}',
        '{"action": {"tool_name": "run_code", "parameters": {"code": "print(1)"}}, "reasoning": ""}',
        '{"action": {"tool_name": "generate_call_graph", "parameters": {"path": "."}}, "reasoning": ""}',
        '{"action": {"tool_name": "hover_doc", "parameters": {"path": "mindlink/tools/file_tools.py", "line": 1, "column": 1}}, "reasoning": ""}',
        '{"action": {"tool_name": "snapshot", "parameters": {"name": "test_snap", "history": []}}, "reasoning": ""}',
        '{"action": {"tool_name": "finish", "parameters": {"result": "done"}}, "reasoning": ""}'
    ]
    def fake_generate(self, system_prompt, user_prompt, history=None):
        return responses.pop(0)
    monkeypatch.setattr(OpenRouterModel, 'generate', fake_generate)

import sys
sys.path.append(r'd:/کتابخانه پایتون/2')
from mindlink import run_agent

# Ultra-complex goal requiring multiple new features
goal = '''
Implement atomic transaction semantics for file operations:
1. Wrap all create_file and read_file calls in a transactional context so that if any step fails, previous file changes are rolled back.
2. Inject AST-based transaction wrappers into mindlink/tools/file_tools.py using insert_ast_node.
3. Generate unit tests simulating failures to verify rollback.
4. Use run_code to compile and execute a code snippet that demonstrates a rollback on error.
5. Generate a dependency/call graph showing the new transaction functions.
6. Provide inline documentation (hover_doc) for the transaction classes.
7. Snapshot the project and agent history.
8. Finish with a summary of actions taken.
'''

def test_ultra_complex_agent_integration():
    # Run agent with configured LLM provider to exercise full workflow
    success, result, history = run_agent(goal)
    assert success
    # Collect the sequence of tools used
    tool_sequence = [entry['request'].action.tool_name for entry in history]
    # Expected tools in the workflow
    expected_tools = [
        'wrap_transaction_wrappers',
        'run_code',
        'generate_call_graph',
        'hover_doc',
        'snapshot',
        'finish'
    ]
    for tool in expected_tools:
        assert tool in tool_sequence, f"Expected tool '{tool}' in agent workflow, got {tool_sequence}"
