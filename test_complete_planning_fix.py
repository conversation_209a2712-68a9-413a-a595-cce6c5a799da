#!/usr/bin/env python3
"""
Test script to verify the complete iterative planning fix for generate_large_file actions.
"""

import sys
import os
sys.path.insert(0, os.path.abspath('.'))

from mindlink.schemas.mindlink import MindLinkRequest, Action
from mindlink.utils.json_parser import safe_parse_llm_response

def test_parameter_normalization():
    """Test parameter normalization for various scenarios."""
    
    print("=== Testing Parameter Normalization Logic ===")
    
    # Test cases for different generate_large_file scenarios
    test_cases = [
        {
            "name": "Missing path parameter",
            "json": '''{
                "action": {
                    "tool_name": "generate_large_file",
                    "parameters": {
                        "content_description": "A Python script for data processing",
                        "lines": 300
                    }
                },
                "reasoning": "Creating a large Python file",
                "thought": "This will be a comprehensive data processing script"
            }'''
        },
        {
            "name": "Empty path parameter",
            "json": '''{
                "action": {
                    "tool_name": "generate_large_file",
                    "parameters": {
                        "content_description": "JavaScript utility functions",
                        "lines": 250,
                        "path": ""
                    }
                },
                "reasoning": "Creating JavaScript utilities",
                "thought": "Utility functions for web development"
            }'''
        },
        {
            "name": "Relative path parameter",
            "json": '''{
                "action": {
                    "tool_name": "generate_large_file",
                    "parameters": {
                        "content_description": "HTML template file",
                        "lines": 200,
                        "path": "templates/index.html"
                    }
                },
                "reasoning": "Creating HTML template",
                "thought": "Template for the main page"
            }'''
        },
        {
            "name": "Absolute path outside D:/3/",
            "json": '''{
                "action": {
                    "tool_name": "generate_large_file",
                    "parameters": {
                        "content_description": "Configuration file",
                        "lines": 100,
                        "path": "C:/temp/config.json"
                    }
                },
                "reasoning": "Creating config file",
                "thought": "Configuration for the application"
            }'''
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n--- Test Case {i}: {test_case['name']} ---")
        
        # Parse the JSON using safe_parse_llm_response
        parsed_req, error = safe_parse_llm_response(test_case['json'])
        
        if not parsed_req:
            print(f"❌ FAILED: Could not parse JSON - {error}")
            continue
            
        action = parsed_req.action
        print(f"Original parameters: {action.parameters}")
        
        # Apply the same parameter normalization logic as in agent.py
        if action.tool_name in ['create_file', 'write_file', 'read_file', 'delete_file', 'generate_large_file']:
            if 'path' in action.parameters and action.parameters['path']:
                # Normalize path to absolute path under D:/3/
                import os
                original_path = action.parameters['path']
                if not os.path.isabs(original_path):
                    action.parameters['path'] = f"D:/3/{original_path}"
                elif not original_path.startswith("D:/3/"):
                    # If it's absolute but not under D:/3/, make it relative to D:/3/
                    basename = os.path.basename(original_path)
                    action.parameters['path'] = f"D:/3/{basename}"
            elif action.tool_name == 'generate_large_file':
                # For generate_large_file, ensure path parameter exists
                if 'path' not in action.parameters or not action.parameters['path']:
                    # Generate a default path based on content description
                    content_desc = action.parameters.get('content_description', 'generated_file')
                    # Extract a reasonable filename from content description
                    import re
                    filename_match = re.search(r'\b(\w+)\.(py|js|txt|md|html|css|json)\b', content_desc)
                    if filename_match:
                        filename = filename_match.group(0)
                    else:
                        # Generate filename based on content type
                        if 'python' in content_desc.lower() or 'py' in content_desc.lower():
                            filename = 'generated_code.py'
                        elif 'javascript' in content_desc.lower() or 'js' in content_desc.lower():
                            filename = 'generated_code.js'
                        elif 'html' in content_desc.lower():
                            filename = 'generated_page.html'
                        else:
                            filename = 'generated_file.txt'
                    action.parameters['path'] = f"D:/3/{filename}"
        
        print(f"Normalized parameters: {action.parameters}")
        
        # Verify the path parameter exists and is properly formatted
        if 'path' in action.parameters and action.parameters['path']:
            path = action.parameters['path']
            if path.startswith('D:/3/'):
                print(f"✅ SUCCESS: Path properly normalized to: {path}")
            else:
                print(f"❌ FAILED: Path not properly normalized: {path}")
        else:
            print("❌ FAILED: Path parameter still missing or empty")

def test_echo_fallback_scenario():
    """Test the specific echo fallback scenario from plan_once."""
    
    print("\n\n=== Testing Echo Fallback Scenario ===")
    
    # Simulate the echo action with embedded generate_large_file JSON
    embedded_json = '''{
        "action": {
            "tool_name": "generate_large_file",
            "parameters": {
                "content_description": "Python data analysis script with pandas and numpy",
                "lines": 300
            }
        },
        "reasoning": "Creating a comprehensive data analysis script",
        "thought": "This will include data loading, cleaning, and visualization"
    }'''
    
    # This simulates what happens in plan_once when we get an echo action
    echo_action = Action(
        tool_name="echo",
        parameters={"message": embedded_json}
    )
    
    translation_req = MindLinkRequest(
        action=echo_action,
        reasoning="Fallback to echo due to parsing issues",
        thought="Using echo as fallback mechanism"
    )
    
    print(f"Original echo action: {translation_req.action.tool_name}")
    print(f"Embedded message length: {len(translation_req.action.parameters.get('message', ''))} characters")
    
    # Apply the exact logic from plan_once method
    if translation_req.action.tool_name == "echo":
        # Attempt to reparse an embedded tool call from the echo message
        raw_msg = translation_req.action.parameters.get("message", "")
        parsed_req, _ = safe_parse_llm_response(raw_msg)
        
        if parsed_req and parsed_req.action.tool_name and parsed_req.action.tool_name != "echo":
            print(f"Successfully parsed embedded action: {parsed_req.action.tool_name}")
            
            # Apply parameter normalization to the parsed action
            action = parsed_req.action
            print(f"Original embedded parameters: {action.parameters}")
            
            if action.tool_name in ['create_file', 'write_file', 'read_file', 'delete_file', 'generate_large_file']:
                if 'path' in action.parameters and action.parameters['path']:
                    # Normalize path to absolute path under D:/3/
                    import os
                    original_path = action.parameters['path']
                    if not os.path.isabs(original_path):
                        action.parameters['path'] = f"D:/3/{original_path}"
                    elif not original_path.startswith("D:/3/"):
                        # If it's absolute but not under D:/3/, make it relative to D:/3/
                        basename = os.path.basename(original_path)
                        action.parameters['path'] = f"D:/3/{basename}"
                elif action.tool_name == 'generate_large_file':
                    # For generate_large_file, ensure path parameter exists
                    if 'path' not in action.parameters or not action.parameters['path']:
                        # Generate a default path based on content description
                        content_desc = action.parameters.get('content_description', 'generated_file')
                        # Extract a reasonable filename from content description
                        import re
                        filename_match = re.search(r'\b(\w+)\.(py|js|txt|md|html|css|json)\b', content_desc)
                        if filename_match:
                            filename = filename_match.group(0)
                        else:
                            # Generate filename based on content type
                            if 'python' in content_desc.lower() or 'py' in content_desc.lower():
                                filename = 'generated_code.py'
                            elif 'javascript' in content_desc.lower() or 'js' in content_desc.lower():
                                filename = 'generated_code.js'
                            elif 'html' in content_desc.lower():
                                filename = 'generated_page.html'
                            else:
                                filename = 'generated_file.txt'
                        action.parameters['path'] = f"D:/3/{filename}"
            
            print(f"Final normalized parameters: {action.parameters}")
            
            # Verify the fix worked
            if 'path' in action.parameters and action.parameters['path']:
                print(f"✅ SUCCESS: Echo fallback scenario fixed! Path: {action.parameters['path']}")
                return [action]  # This is what plan_once would return
            else:
                print("❌ FAILED: Path parameter still missing after normalization")
        else:
            print("❌ FAILED: Could not parse embedded JSON from echo message")
    
    return []

if __name__ == "__main__":
    test_parameter_normalization()
    test_echo_fallback_scenario()
    print("\n=== All Tests Completed ===")