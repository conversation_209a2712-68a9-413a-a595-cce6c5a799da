#!/usr/bin/env python3
"""
Final test of GenerateLargeFileTool
"""

import sys
import os
sys.path.append('.')

from mindlink.tools.file_tools import GenerateLargeFileTool

def main():
    tool = GenerateLargeFileTool()
    
    print("Testing GenerateLargeFileTool with 1000-line target...")
    
    result = tool.execute(
        path='generated_demo.py',
        content_description='Create a comprehensive Python application with web scraping, data analysis, machine learning utilities, file operations, and advanced algorithms',
        target_line_count=1000,
        max_chunks=10
    )
    
    print("\n=== FINAL TEST RESULT ===")
    print(f"Status: {result['status']}")
    print(f"Lines written: {result['result']['lines_written']}")
    print(f"Chunks: {result['result']['chunks_written']}")
    print(f"Target met: {result['result']['target_lines_met']}")
    print(f"File path: {result['result']['file_path']}")
    
    # Check if file exists in working directory
    if os.path.exists('generated_demo.py'):
        print("\n✓ File successfully created in working directory!")
        with open('generated_demo.py', 'r') as f:
            lines = f.readlines()
            print(f"✓ File contains {len(lines)} lines")
            print("\n=== First 20 lines of generated file ===")
            for i, line in enumerate(lines[:20], 1):
                print(f"{i:2d}: {line.rstrip()}")
            if len(lines) > 20:
                print("...")
                print(f"\n=== Last 10 lines of generated file ===")
                for i, line in enumerate(lines[-10:], len(lines)-9):
                    print(f"{i:2d}: {line.rstrip()}")
    else:
        print("\n✗ File not found in working directory")

if __name__ == '__main__':
    main()