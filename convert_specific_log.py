import os
import json
from pathlib import Path

def convert_specific_log(filename, logs_dir=None):
    if logs_dir is None:
        logs_dir = os.path.join(os.path.dirname(__file__), 'logs')
    
    input_path = os.path.join(logs_dir, filename)
    
    if not os.path.exists(input_path):
        print(f"Error: File {input_path} not found.")
        return
    
    if not filename.endswith('.jsonl'):
        print("Error: File must have a .jsonl extension.")
        return
    
    output_path = os.path.splitext(input_path)[0] + '.json'
    
    print(f"Converting {filename} to JSON...")
    
    try:
        # Read the JSONL file and convert to JSON
        with open(input_path, 'r', encoding='utf-8') as f:
            log_entries = [json.loads(line) for line in f if line.strip()]
        
        # Write as formatted JSON
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(log_entries, f, indent=2, ensure_ascii=False)
        
        print(f"Successfully converted to {output_path}")
        return str(output_path)
    except Exception as e:
        print(f"Error during conversion: {str(e)}")
        return None

if __name__ == "__main__":
    import sys
    if len(sys.argv) > 1:
        convert_specific_log(sys.argv[1])
    else:
        print("Please provide the filename as an argument.")
