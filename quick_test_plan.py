import os
# Quick performance test for multi-step planning
os.environ["OPENROUTER_API_KEY"] = "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"

from agent_capability_benchmark import initialize_agent, test_multi_step_planning, BenchmarkScore
 
agent = initialize_agent()
scores = BenchmarkScore()
test_multi_step_planning(agent, scores)
print("Multi-step planning took", scores.execution_times.get("multi_step_planning"), "seconds") 