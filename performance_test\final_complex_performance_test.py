#!/usr/bin/env python3
"""
Final Complex Performance Test for MindLink Agent Core.
This script runs a range of performance and connectivity tests, including:
- LLM connectivity for OpenRouter and OpenAI
- AgentOS multi-step task execution
- Concurrent AgentOS runs
- Deep nested recursion benchmark
Collects timing and memory metrics for each test and prints a structured report.
"""
import os
import time
import gc
import psutil
from concurrent.futures import ThreadPoolExecutor
from mindlink.config import load_config
from mindlink.main import create_llm
from mindlink.agent import AgentOS

# Decorator to measure time and memory usage

def measure(func):
    def wrapper(*args, **kwargs):
        process = psutil.Process(os.getpid())
        gc.collect()
        mem_before = process.memory_info().rss / 1024 / 1024  # MB
        t0 = time.time()
        result = func(*args, **kwargs)
        t1 = time.time()
        gc.collect()
        mem_after = process.memory_info().rss / 1024 / 1024  # MB
        return {
            'result': result,
            'time': t1 - t0,
            'mem_delta_mb': mem_after - mem_before
        }
    return wrapper

@measure
 def test_llm_connectivity():
    cfg = load_config()
    metrics = {}
    # OpenRouter connectivity
    try:
        or_model = create_llm(cfg)
        resp = or_model.generate('Connectivity Test', 'Ping')
        metrics['OpenRouter'] = {'success': bool(resp), 'resp_snippet': resp[:80]}
    except Exception as e:
        metrics['OpenRouter'] = {'success': False, 'error': str(e)}
    # OpenAI connectivity
    api_key = os.getenv('OPENAI_API_KEY')
    if api_key:
        cfg['llm']['provider'] = 'openai'
        cfg['llm']['api_key'] = api_key
        try:
            ai_model = create_llm(cfg)
            resp2 = ai_model.generate('Connectivity Test', 'Ping')
            metrics['OpenAI'] = {'success': bool(resp2), 'resp_snippet': resp2[:80]}
        except Exception as e:
            metrics['OpenAI'] = {'success': False, 'error': str(e)}
    else:
        metrics['OpenAI'] = {'success': None, 'error': 'No API key'}
    return metrics

@measure
 def test_agent_multistep(goal):
    cfg = load_config()
    llm = create_llm(cfg)
    agent = AgentOS(llm=llm,
                   system_prompt_template=cfg['agent']['system_prompt_template'],
                   max_steps=cfg['agent']['max_steps'])
    success, result, history = agent.run(goal)
    return {'success': success, 'steps': len(history), 'output_snippet': str(result)[:100]}

@measure
 def test_concurrent_agent_runs(n, goal):
    def worker():
        cfg = load_config()
        llm = create_llm(cfg)
        agent = AgentOS(llm=llm,
                       system_prompt_template=cfg['agent']['system_prompt_template'],
                       max_steps=cfg['agent']['max_steps'])
        success, _, history = agent.run(goal)
        return {'success': success, 'steps': len(history)}

    with ThreadPoolExecutor(max_workers=n) as ex:
        futures = [ex.submit(worker) for _ in range(n)]
        return [f.result() for f in futures]

# Deep nested recursion benchmark
def nested_recursion(depth, max_depth):
    if depth > max_depth:
        return 1
    total = 0
    for _ in range(10):
        total += nested_recursion(depth + 1, max_depth)
    return total

@measure
 def test_nested_recursion(max_depth):
    return nested_recursion(1, max_depth)


def main():
    print('=== Final Complex Performance Test Report ===')
    # LLM Connectivity
    llm_m = test_llm_connectivity()
    print('\n[LLM Connectivity] time={:.2f}s memΔ={:.2f}MB'.format(llm_m['time'], llm_m['mem_delta_mb']))
    for service, data in llm_m['result'].items():
        print(f"- {service}: success={data.get('success')}{{', error='+data['error'] if 'error' in data else ''}}")

    # Multi-step task
    goal = "1. Create file 'hello.txt' with 'Hello World'; 2. Read it; 3. Delete it; 4. Return word count"
    ms = test_agent_multistep(goal)
    print(f"\n[Multi-step AgentOS] time={ms['time']:.2f}s memΔ={ms['mem_delta_mb']:.2f}MB success={ms['result']['success']} steps={ms['result']['steps']}")

    # Concurrent AgentOS runs
    cr = test_concurrent_agent_runs(5, goal)
    total_time = cr['time']
    print(f"\n[Concurrent AgentOS x5] time={total_time:.2f}s memΔ={cr['mem_delta_mb']:.2f}MB avg_time_per_run={total_time/5:.2f}s")
    for i, r in enumerate(cr['result'], 1):
        print(f"- Run {i}: success={r['success']} steps={r['steps']}")

    # Nested recursion
    nr = test_nested_recursion(4)
    print(f"\n[Nested Recursion depth=4] time={nr['time']:.2f}s memΔ={nr['mem_delta_mb']:.2f}MB result={nr['result']}")

if __name__ == '__main__':
    main()
