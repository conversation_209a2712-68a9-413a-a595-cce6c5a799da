import pytest
import os
import tempfile
import shutil
from pathlib import Path

from mindlink.tools.file_tools import SearchInFilesTool, create_file, SAFE_BASE_DIR

@pytest.fixture
def search_test_dir():
    """Create a temporary directory with test files for search tests."""
    SAFE_BASE_DIR.mkdir(parents=True, exist_ok=True)
    temp_dir = tempfile.mkdtemp(dir=str(SAFE_BASE_DIR))
    # Create some files with specific content
    file_contents = {
        "file1.txt": "Hello world from MindLink.\nThis is a test file for search functionality.",
        "file2.log": "Another test file here.\nSearch for a specific pattern.",
        "file3.txt": "No matching pattern in this one.",
    }
    subdir_path = os.path.join(temp_dir, "subdir")
    os.makedirs(subdir_path, exist_ok=True)
    file_contents[os.path.join("subdir", "s_file1.txt")] = "Deep search test.\nHello again, world."
    file_contents[os.path.join("subdir", "s_file2.log")] = "Log file in subdirectory."

    for rel_path, content in file_contents.items():
        abs_path = os.path.join(temp_dir, rel_path)
        # Use the library's create_file to ensure transactional behavior if applicable
        # For simplicity in test setup, direct file write is also okay if create_file has complex deps
        with open(abs_path, "w", encoding="utf-8") as f:
            f.write(content)
            
    yield temp_dir
    # Clean up after the test
    shutil.rmtree(temp_dir)

@pytest.fixture
def search_tool():
    """Provides an instance of the SearchInFilesTool."""
    return SearchInFilesTool()

def test_search_existing_pattern_single_file_type(search_test_dir, search_tool):
    """Test searching for an existing pattern in .txt files."""
    result = search_tool.execute(directory=search_test_dir, pattern="Hello world", file_mask="*.txt")
    assert result["status"] == "success"
    assert len(result["found_files"]) == 1
    assert result["found_files"][0]["file_path"] == os.path.join(search_test_dir, "file1.txt")
    assert len(result["found_files"][0]["matches"]) == 1
    assert result["found_files"][0]["matches"][0]["line_content"] == "Hello world from MindLink."

def test_search_existing_pattern_recursive(search_test_dir, search_tool):
    """Test searching for an existing pattern recursively."""
    result = search_tool.execute(directory=search_test_dir, pattern="world", recursive=True, file_mask="*.txt")
    assert result["status"] == "success"
    assert len(result["found_files"]) == 2
    file_paths_found = {f["file_path"] for f in result["found_files"]}
    expected_paths = {
        os.path.join(search_test_dir, "file1.txt"),
        os.path.join(search_test_dir, "subdir", "s_file1.txt")
    }
    assert file_paths_found == expected_paths

def test_search_non_existing_pattern(search_test_dir, search_tool):
    """Test searching for a pattern that does not exist."""
    result = search_tool.execute(directory=search_test_dir, pattern="nonexistentXYZ123", file_mask="*.txt")
    assert result["status"] == "success" # Tool succeeds, but finds nothing
    assert "No files found with pattern" in result["observation"]
    assert len(result["found_files"]) == 0

def test_search_different_file_mask(search_test_dir, search_tool):
    """Test searching with a different file mask (.log files)."""
    result = search_tool.execute(directory=search_test_dir, pattern="specific pattern", file_mask="*.log")
    assert result["status"] == "success"
    assert len(result["found_files"]) == 1
    assert result["found_files"][0]["file_path"] == os.path.join(search_test_dir, "file2.log")
    assert "Search for a specific pattern." in result["found_files"][0]["matches"][0]["line_content"]

def test_search_all_file_types_mask(search_test_dir, search_tool):
    """Test searching with a wildcard file mask (e.g., '*.*' or '*')."""
    result = search_tool.execute(directory=search_test_dir, pattern="test file", file_mask="*.*")
    assert result["status"] == "success"
    # Expecting file1.txt and file2.log
    assert len(result["found_files"]) == 2 
    file_paths_found = {f["file_path"] for f in result["found_files"]}
    expected_paths = {
        os.path.join(search_test_dir, "file1.txt"),
        os.path.join(search_test_dir, "file2.log")
    }
    assert file_paths_found == expected_paths

def test_search_non_recursive(search_test_dir, search_tool):
    """Test non-recursive search; should not find patterns in subdirectories."""
    result = search_tool.execute(directory=search_test_dir, pattern="Hello again", recursive=False, file_mask="*.txt")
    assert result["status"] == "success"
    assert "No files found with pattern" in result["observation"]
    assert len(result["found_files"]) == 0 # "Hello again" is in subdir/s_file1.txt

def test_search_empty_directory(search_tool):
    """Test searching in an empty directory."""
    SAFE_BASE_DIR.mkdir(parents=True, exist_ok=True)
    empty_dir = tempfile.mkdtemp(dir=str(SAFE_BASE_DIR))
    try:
        result = search_tool.execute(directory=empty_dir, pattern="anything", file_mask="*.txt")
        assert result["status"] == "success"
        assert "No files found with pattern" in result["observation"]
        assert len(result["found_files"]) == 0
    finally:
        shutil.rmtree(empty_dir)

def test_search_pattern_spanning_lines_not_supported_by_default(search_test_dir, search_tool):
    """Confirm default behavior for patterns that might span lines (usually not matched per line)."""
    # Create a file with a pattern that could be seen as spanning if not line-by-line
    file_path = os.path.join(search_test_dir, "multiline.txt")
    with open(file_path, "w") as f:
        f.write("first line\nsecond line")
    
    result = search_tool.execute(directory=search_test_dir, pattern="first line\\nsecond line", file_mask="*.txt")
    assert result["status"] == "success"
    # Standard line-by-line regex won't match this unless multiline flag is used in regex engine and supported by tool
    # Assuming the tool searches line by line without re.MULTILINE by default for the pattern itself.
    assert len(result["found_files"]) == 0

    result_first = search_tool.execute(directory=search_test_dir, pattern="first line", file_mask="*.txt")
    assert len(result_first["found_files"]) == 1
    assert result_first["found_files"][0]["file_path"] == file_path

    result_second = search_tool.execute(directory=search_test_dir, pattern="second line", file_mask="*.txt")
    assert len(result_second["found_files"]) == 1
    assert result_second["found_files"][0]["file_path"] == file_path


# Example of a performance-oriented test (can be very basic)
# More advanced performance tests would require larger datasets and more precise timing

@pytest.mark.performance
def test_search_performance_basic(search_test_dir, search_tool):
    """Basic performance check for search functionality."""
    # Create a slightly larger number of files for a basic performance feel
    # This is not a rigorous benchmark but a sanity check.
    num_extra_files = 50
    for i in range(num_extra_files):
        extra_file_path = os.path.join(search_test_dir, f"perf_file_{i}.txt")
        with open(extra_file_path, "w") as f:
            f.write(f"Content for performance test file {i}\nUnique pattern perf{i}")
    
    import time
    start_time = time.time()
    result = search_tool.execute(directory=search_test_dir, pattern="Unique pattern perf10", file_mask="*.txt")
    end_time = time.time()
    
    duration = end_time - start_time
    print(f"Search execution time for perf test: {duration:.4f} seconds")
    
    assert result["status"] == "success"
    assert len(result["found_files"]) == 1
    assert f"perf_file_10.txt" in result["found_files"][0]["file_path"]
    
    # A very loose assertion for performance, e.g., completes within a few seconds
    # This threshold would need adjustment based on typical machine performance and dataset size.
    assert duration < 5  # Example: expecting completion under 5 seconds for this small scale