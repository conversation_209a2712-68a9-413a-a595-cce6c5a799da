#!/usr/bin/env python3
"""
Debug script with detailed logging to understand GenerateLargeFileTool behavior
"""

import os
import sys
import logging
from pathlib import Path

# Add the project root to Python path
sys.path.insert(0, str(Path(__file__).parent))

# Enable debug logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

from mindlink.tools.file_tools import GenerateLargeFileTool

def debug_with_logging():
    """Debug the GenerateLargeFileTool with detailed logging"""
    tool = GenerateLargeFileTool()
    
    print("=== Debugging GenerateLargeFileTool with Logging ===")
    print("Testing with moderate target and detailed logging...")
    
    # Test with moderate target
    result = tool.execute(
        path="debug_logged_file.py",
        content_description="Create a Python class for managing a simple todo list with methods to add, remove, list, and mark tasks as complete. Include detailed comments and docstrings.",
        target_line_count=150,
        max_chunks=8,
        chunk_size_description="Generate about 30-50 lines of functional Python code with comments.",
        context_carryover_lines=10
    )
    
    print(f"\nResult: {result}")
    
    if result.get('status') == 'success':
        lines_written = result.get('result', {}).get('lines_written')
        chunks_written = result.get('result', {}).get('chunks_written')
        target_met = result.get('result', {}).get('target_lines_met')
        
        print(f"\n📝 Lines written: {lines_written}")
        print(f"🔢 Chunks written: {chunks_written}")
        print(f"🎯 Target met: {target_met}")
        print(f"📈 Target vs Actual: 150 vs {lines_written} ({lines_written/150*100:.1f}% of target)")
        
        if chunks_written >= 3:
            print("✅ Tool is generating multiple chunks")
        else:
            print("❌ Tool is stopping too early")
            
        if lines_written >= 100:
            print("✅ Tool is generating reasonable content")
        else:
            print("❌ Tool is not generating enough content")
    else:
        print(f"❌ Error: {result.get('error', 'Unknown error')}")
        print(f"Observation: {result.get('observation', 'No observation')}")

if __name__ == "__main__":
    debug_with_logging()