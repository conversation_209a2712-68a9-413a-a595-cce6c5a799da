from enum import Enum
from typing import Any, Dict, List, Optional, Union, Literal
from pydantic import BaseModel, Field, validator
import uuid
import os
import json
import hashlib # Added import
from datetime import datetime
from typing_extensions import TypedDict  # For Python < 3.8 compatibility

class EventType(str, Enum):
    # User interaction events
    USER_INPUT = "user_input"
    USER_FEEDBACK = "user_feedback"
    AGENT_CLARIFICATION_REQUEST = "agent_clarification_request"
    
    # Agent decision events
    AGENT_PLAN_GENERATED = "agent_plan_generated"
    AGENT_GOAL_COMPLETED = "agent_goal_completed"
    
    # Tool interaction events
    TOOL_CALL_START = "tool_call_start"
    TOOL_CALL_END = "tool_call_end"
    
    # LLM interaction events
    LLM_QUERY = "llm_query"
    LLM_RESPONSE = "llm_response"
    
    # System events
    ERROR_OCCURRED = "error_occurred"
    SYSTEM_STATE_UPDATE = "system_state_update"

class UserInputData(BaseModel):
    raw_text: str
    intent: Optional[str] = None

class PlanStep(BaseModel):
    step_id: str = Field(..., description="Unique identifier for the plan step")
    tool_name: str = Field(..., description="Name of the tool to be executed")
    parameters: Dict[str, Any] = Field(default_factory=dict, description="Parameters for the tool call")
    reasoning: Optional[str] = Field(None, description="Agent's reasoning for this step")

class AgentDecisionData(BaseModel):
    """Data related to agent decisions and planning."""
    plan: List[PlanStep] = Field(default_factory=list, description="List of planned steps")
    confidence_score: Optional[float] = Field(None, description="Agent's confidence in the plan (0.0 to 1.0)")
    chosen_action_step_id: Optional[str] = Field(None, description="ID of the step selected for execution")
    reasoning: Optional[str] = Field(None, description="Agent's reasoning for the decision")

class ToolCallData(BaseModel):
    """Data for tool execution events."""
    tool_name: str = Field(..., description="Name of the tool being called")
    parameters: Dict[str, Any] = Field(default_factory=dict, description="Parameters passed to the tool")
    parameters_hash: Optional[str] = Field(None, description="SHA256 hash of the tool parameters")
    start_time: Optional[datetime] = Field(None, description="When the tool execution started")
    end_time: Optional[datetime] = Field(None, description="When the tool execution completed")
    status: Optional[str] = Field(None, description="Execution status (e.g., 'started', 'completed', 'failed')")
    result: Optional[Dict[str, Any]] = Field(None, description="Result returned by the tool. For file ops, includes file_path, size_bytes, content_hash etc.")
    error: Optional[str] = Field(None, description="Error message if the tool failed")
    execution_time_ms: Optional[float] = Field(None, description="Execution time in milliseconds")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Tool-specific metadata, e.g., attempt number.")

class LLMQueryData(BaseModel):
    """Data for LLM query events."""
    model: str = Field(..., description="Name/ID of the LLM model used")
    prompt: str = Field(..., description="The prompt sent to the LLM")
    temperature: Optional[float] = Field(None, description="Sampling temperature used")
    max_tokens: Optional[int] = Field(None, description="Maximum number of tokens to generate")
    system_prompt: Optional[str] = Field(None, description="System prompt/instructions provided to the model")
    tools: Optional[List[Dict[str, Any]]] = Field(None, description="Tools/functions available to the model")

class LLMResponseData(BaseModel):
    """Data for LLM response events."""
    model: str = Field(..., description="Name/ID of the LLM model that generated the response")
    response: str = Field(..., description="The full response text from the LLM")
    prompt: Optional[str] = Field(None, description="The prompt that generated this response")
    prompt_tokens: Optional[int] = Field(None, description="Number of tokens in the prompt")
    completion_tokens: Optional[int] = Field(None, description="Number of tokens in the completion")
    total_tokens: Optional[int] = Field(None, description="Total tokens used (prompt + completion)")
    finish_reason: Optional[str] = Field(None, description="Reason the model stopped generating")
    latency_ms: Optional[float] = Field(None, description="Time taken to generate the response in milliseconds")

class UserFeedbackData(BaseModel):
    """Data for user feedback and corrections."""
    feedback_type: Literal["correction", "rating", "comment", "clarification"] = Field(
        ..., 
        description="Type of feedback provided by the user"
    )
    corrected_action: Optional[Dict[str, Any]] = Field(
        None, 
        description="The corrected action if the user is fixing an agent's action"
    )
    clarification_provided: Optional[str] = Field(
        None, 
        description="The user's response to a clarification request"
    )
    rating: Optional[float] = Field(
        None, 
        ge=0, 
        le=1, 
        description="Numeric rating from 0 to 1 (if applicable)"
    )
    comment: Optional[str] = Field(
        None, 
        description="Free-form feedback comment from the user"
    )
    implicit_feedback_notes: Optional[str] = Field(
        None, 
        description="Observations about the agent's performance"
    )

class AgentClarificationData(BaseModel):
    """Data for when an agent requests clarification."""
    question: str = Field(..., description="The clarification question asked by the agent")
    context: Optional[Dict[str, Any]] = Field(
        None, 
        description="Context for the clarification request"
    )
    required: bool = Field(
        True, 
        description="Whether a response is required to continue"
    )

class SystemStateData(BaseModel):
    """Data for system state updates."""
    state_summary: Dict[str, Any] = Field(
        ..., 
        description="Summary of the current system state"
    )
    context_window: Optional[List[Dict[str, Any]]] = Field(
        None, 
        description="Recent conversation history or context"
    )
    memory_summary: Optional[Dict[str, Any]] = Field(
        None, 
        description="Summary of relevant memories"
    )

class ErrorContextData(BaseModel):
    """Data for error events."""
    component: str = Field(..., description="Component that encountered the error")
    error_message: str = Field(..., description="Error message")
    severity: str = Field(..., description="Severity of the error")
    stack_trace: Optional[str] = Field(None, description="Stack trace for the error")

class LogEvent(BaseModel):
    """A single log event in the system."""
    event_id: str = Field(
        default_factory=lambda: str(uuid.uuid4()),
        description="Unique identifier for this event"
    )
    event_type: EventType = Field(
        ..., 
        description="Type of the event"
    )
    session_id: str = Field(
        ..., 
        description="ID of the session this event belongs to"
    )
    timestamp: datetime = Field(
        default_factory=datetime.utcnow,
        description="When the event occurred"
    )
    
    # Event-specific data (only one of these should be set based on event_type)
    user_input: Optional[UserInputData] = Field(
        None,
        description="Data for user input events"
    )
    user_feedback: Optional[UserFeedbackData] = Field(
        None,
        description="Data for user feedback and corrections"
    )
    agent_decision: Optional[AgentDecisionData] = Field(
        None,
        description="Data for agent decision events"
    )
    tool_call: Optional[ToolCallData] = Field(
        None,
        description="Data for tool execution events"
    )
    llm_query: Optional[LLMQueryData] = Field(
        None,
        description="Data for LLM query events"
    )
    llm_response: Optional[LLMResponseData] = Field(
        None,
        description="Data for LLM response events"
    )
    agent_clarification: Optional[AgentClarificationData] = Field(
        None,
        description="Data for agent clarification requests"
    )
    system_state: Optional[SystemStateData] = Field(
        None,
        description="Data for system state updates"
    )
    error: Optional[ErrorContextData] = Field(
        None,
        description="Error information for error events"
    )
    
    # Add metadata field for any additional context
    metadata: Dict[str, Any] = Field(
        default_factory=dict,
        description="Additional metadata for the event"
    )
    
    @validator('timestamp', pre=True, always=True)
    def set_timestamp(cls, v):
        return v or datetime.utcnow()

# --- Logger Setup (Basic Example - can be expanded) ---
LOGS_DIR = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'logs') # Define logs directory at project root

# Dictionary to store file handles for each session
_session_files = {}
_json_session_files = {}
_json_array_started = {}
_default_session_id: Optional[str] = None  # Fallback session ID when none provided

def _format_event_for_logging(event: LogEvent) -> Dict[str, Any]:
    """
    Format a LogEvent for structured, readable logging.
    
    Args:
        event: The LogEvent to format
        
    Returns:
        Formatted dictionary with consistent field ordering and structure
    """
    # Start with basic event info in a specific order
    formatted = {
        "event_id": event.event_id,
        "timestamp": event.timestamp.isoformat() if event.timestamp else None,
        "session_id": event.session_id,
        "event_type": event.event_type
    }
    
    # Add event-specific data based on type
    if event.user_input:
        formatted["user_input"] = {
            "text": event.user_input.raw_text,
            "intent": event.user_input.intent
        }
    
    if event.agent_decision:
        decision_data = {
            "confidence_score": event.agent_decision.confidence_score,
            "reasoning": event.agent_decision.reasoning
        }
        if event.agent_decision.plan:
            decision_data["plan"] = [
                {
                    "step_id": step.step_id,
                    "tool_name": step.tool_name,
                    "parameters": step.parameters,
                    "reasoning": step.reasoning
                } for step in event.agent_decision.plan
            ]
        if event.agent_decision.chosen_action_step_id:
            decision_data["chosen_step_id"] = event.agent_decision.chosen_action_step_id
        formatted["agent_decision"] = decision_data
    
    if event.tool_call:
        tool_data = {
            "tool_name": event.tool_call.get("tool_name") if isinstance(event.tool_call, dict) else event.tool_call.tool_name,
            "status": event.tool_call.get("status") if isinstance(event.tool_call, dict) else event.tool_call.status
        }
        
        # Add timing information if available
        if isinstance(event.tool_call, dict):
            if event.tool_call.get("start_time"):
                tool_data["start_time"] = event.tool_call["start_time"]
            if event.tool_call.get("end_time"):
                tool_data["end_time"] = event.tool_call["end_time"]
            if event.tool_call.get("execution_time_ms"):
                tool_data["execution_time_ms"] = round(event.tool_call["execution_time_ms"], 2)
            
            # Add parameters (hashed for security)
            if event.tool_call.get("parameters_hash"):
                tool_data["parameters_hash"] = event.tool_call["parameters_hash"]
            elif event.tool_call.get("parameters"):
                # Show limited parameter info for readability
                params = event.tool_call["parameters"]
                if isinstance(params, dict) and len(params) <= 3:
                    tool_data["parameters_preview"] = {k: str(v)[:50] + "..." if len(str(v)) > 50 else v for k, v in params.items()}
            
            # Add result summary
            if event.tool_call.get("result"):
                result = event.tool_call["result"]
                if isinstance(result, dict):
                    tool_data["result_summary"] = {
                        "success": event.tool_call.get("status") == "SUCCESS",
                        "observation": result.get("observation", "")[:100] + "..." if len(str(result.get("observation", ""))) > 100 else result.get("observation", "")
                    }
            
            # Add error if present
            if event.tool_call.get("error"):
                tool_data["error"] = event.tool_call["error"]
        
        formatted["tool_execution"] = tool_data
    
    if event.llm_query:
        formatted["llm_query"] = {
            "model": event.llm_query.model,
            "prompt_length": len(event.llm_query.prompt),
            "temperature": event.llm_query.temperature,
            "max_tokens": event.llm_query.max_tokens,
            "has_system_prompt": bool(event.llm_query.system_prompt),
            "tools_available": len(event.llm_query.tools) if event.llm_query.tools else 0
        }
    
    if event.llm_response:
        formatted["llm_response"] = {
            "model": event.llm_response.model,
            "response_length": len(event.llm_response.response),
            "prompt_tokens": event.llm_response.prompt_tokens,
            "completion_tokens": event.llm_response.completion_tokens,
            "total_tokens": event.llm_response.total_tokens,
            "finish_reason": event.llm_response.finish_reason,
            "latency_ms": round(event.llm_response.latency_ms, 2) if event.llm_response.latency_ms else None
        }
    
    if event.error:
        formatted["error_details"] = {
            "component": event.error.component,
            "severity": event.error.severity,
            "message": event.error.error_message,
            "has_stack_trace": bool(event.error.stack_trace)
        }
    
    if event.user_feedback:
        formatted["user_feedback"] = {
            "type": event.user_feedback.feedback_type,
            "rating": event.user_feedback.rating,
            "has_correction": bool(event.user_feedback.corrected_action),
            "has_comment": bool(event.user_feedback.comment)
        }
    
    if event.agent_clarification:
        formatted["clarification_request"] = {
            "question": event.agent_clarification.question,
            "required": event.agent_clarification.required,
            "has_context": bool(event.agent_clarification.context)
        }
    
    if event.system_state:
        formatted["system_state"] = {
            "state_summary": event.system_state.state_summary,
            "context_items": len(event.system_state.context_window) if event.system_state.context_window else 0,
            "has_memory_summary": bool(event.system_state.memory_summary)
        }
    
    # Add metadata if present
    if event.metadata:
        formatted["metadata"] = event.metadata
    
    # Remove None values for cleaner output
    return {k: v for k, v in formatted.items() if v is not None}

def log_event(event: LogEvent):
    """
    Log an event to both JSONL and JSON files with improved formatting.
    
    Args:
        event: The LogEvent instance to log
    """
    try:
        # Ensure the logs directory exists
        if not os.path.exists(LOGS_DIR):
            os.makedirs(LOGS_DIR)
            
        global _default_session_id
        # Determine session_id: use provided or fall back to a single default
        if event.session_id:
            session_id = event.session_id
        else:
            if _default_session_id is None:
                _default_session_id = str(uuid.uuid4())
            session_id = _default_session_id
        event.session_id = session_id  # Update event with resolved session_id
        
        # Create session-specific file paths
        jsonl_path = os.path.join(LOGS_DIR, f"session_{session_id}.jsonl")
        json_path = os.path.join(LOGS_DIR, f"session_{session_id}.json")
        
        # Convert event to dictionary with structured formatting
        event_dict = _format_event_for_logging(event)
        
        if session_id not in _session_files:
            # Now open fresh files for this session
            # JSONL file
            _session_files[session_id] = open(jsonl_path, 'a', encoding='utf-8')
            # JSON array file
            f_json = open(json_path, 'w', encoding='utf-8')
            f_json.write('[\n')
            _json_session_files[session_id] = f_json
            _json_array_started[session_id] = False
        
        # Get the file handle for this session
        log_file = _session_files[session_id]
        
        # Write to JSONL file (compact format for processing)
        log_file.write(json.dumps(event_dict, ensure_ascii=False, separators=(',', ':')) + '\n')
        log_file.flush()  # Ensure data is written to disk
        
        # Write to JSON file (human-readable format)
        f_json = _json_session_files[session_id]
        formatted_json = json.dumps(event_dict, ensure_ascii=False, indent=2)
        if not _json_array_started[session_id]:
            f_json.write(formatted_json)
            _json_array_started[session_id] = True
        else:
            f_json.write(',\n\n' + formatted_json)
        f_json.flush()
        
    except Exception as e:
        print(f"Failed to write log event: {e}")

def close_logs():
    """Closes all log files and clears the session files."""
    for file in _session_files.values():
        try:
            file.close()
        except Exception as e:
            print(f"Error closing log file: {e}")
    _session_files.clear()
    
    # Finalize and close JSON array files
    for sid, f_json in _json_session_files.items():
        try:
            if _json_array_started.get(sid, False):
                f_json.write('\n]')
                f_json.flush()
            f_json.close()
        except Exception as e:
            print(f"Error closing JSON log file: {e}")
    _json_session_files.clear()
    _json_array_started.clear()

    # Deletion of log files is commented out to prevent WinError 32.
    # Consider implementing a log rotation strategy instead.
    # try:
    #     for fname in os.listdir(LOGS_DIR):
    #         fpath = os.path.join(LOGS_DIR, fname)
    #         try:
    #             if os.path.isfile(fpath) or os.path.islink(fpath):
    #                 os.unlink(fpath)
    #             elif os.path.isdir(fpath):
    #                 shutil.rmtree(fpath)
    #         except Exception as e:
    #             print(f"Error deleting {fpath}: {e}")
    # except Exception as e:
    #     print(f"Error listing log directory {LOGS_DIR}: {e}")

def convert_to_json(jsonl_path: str, output_path: str = None, indent: int = 2) -> str:
    """Converts a JSONL file to a JSON file.
    
    Args:
        jsonl_path: Path to the input JSONL file
        output_path: Path to save the JSON file. If None, adds '.json' to input path
        indent: Number of spaces for JSON indentation
        
    Returns:
        Path to the created JSON file
    """
    if not output_path:
        if jsonl_path.endswith('.jsonl'):
            output_path = jsonl_path[:-6] + '.json'
        else:
            output_path = jsonl_path + '.json'
    
    try:
        # Read all lines from the JSONL file
        with open(jsonl_path, 'r', encoding='utf-8') as f:
            lines = [line.strip() for line in f if line.strip()]
        
        # Parse each line as JSON
        data = [json.loads(line) for line in lines]
        
        # Write as formatted JSON
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=indent, ensure_ascii=False)
            
        return output_path
    except Exception as e:
        print(f"Error converting {jsonl_path} to JSON: {e}")
        raise

def convert_logs_to_json(logs_dir: str = None, output_dir: str = None, indent: int = 2) -> list[str]:
    """Converts all JSONL log files in a directory to JSON format.
    
    Args:
        logs_dir: Directory containing JSONL log files. Defaults to LOGS_DIR
        output_dir: Directory to save JSON files. Defaults to same as input
        indent: Number of spaces for JSON indentation
        
    Returns:
        List of paths to created JSON files
    """
    if logs_dir is None:
        logs_dir = LOGS_DIR
    if output_dir is None:
        output_dir = logs_dir
    
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    json_files = []
    
    # Process each JSONL file
    for filename in os.listdir(logs_dir):
        if filename.endswith('.jsonl'):
            input_path = os.path.join(logs_dir, filename)
            output_path = os.path.join(output_dir, filename[:-6] + '.json')
            
            try:
                convert_to_json(input_path, output_path, indent)
                json_files.append(output_path)
                print(f"Converted {filename} to JSON")
            except Exception as e:
                print(f"Failed to convert {filename}: {e}")
    
    return json_files

# --- Helper functions to create specific log events ---

def create_user_input_log(
    session_id: str, 
    raw_text: str, 
    intent: Optional[str] = None,
    metadata: Optional[Dict[str, Any]] = None
) -> LogEvent:
    """Create a log event for user input.
    
    Args:
        session_id: The session ID
        raw_text: The raw text input from the user
        intent: The detected intent (if any)
        metadata: Additional metadata for the event
        
    Returns:
        A LogEvent instance
    """
    return LogEvent(
        session_id=session_id,
        event_type=EventType.USER_INPUT,
        user_input=UserInputData(
            raw_text=raw_text,
            intent=intent
        ),
        metadata=metadata or {}
    )

def create_user_feedback_log(
    session_id: str,
    feedback_type: Literal["correction", "rating", "comment", "clarification"],
    corrected_action: Optional[Dict[str, Any]] = None,
    clarification_provided: Optional[str] = None,
    rating: Optional[float] = None,
    comment: Optional[str] = None,
    implicit_feedback_notes: Optional[str] = None,
    metadata: Optional[Dict[str, Any]] = None
) -> LogEvent:
    """Create a log event for user feedback or correction.
    
    Args:
        session_id: The session ID
        feedback_type: Type of feedback (correction, rating, comment, clarification)
        corrected_action: The corrected action if user is fixing an agent's action
        clarification_provided: User's response to a clarification request
        rating: Numeric rating from 0 to 1 (if applicable)
        comment: Free-form feedback comment
        implicit_feedback_notes: Observations about the agent's performance
        metadata: Additional metadata for the event
        
    Returns:
        A LogEvent instance
    """
    return LogEvent(
        session_id=session_id,
        event_type=EventType.USER_FEEDBACK,
        user_feedback=UserFeedbackData(
            feedback_type=feedback_type,
            corrected_action=corrected_action,
            clarification_provided=clarification_provided,
            rating=rating,
            comment=comment,
            implicit_feedback_notes=implicit_feedback_notes
        ),
        metadata=metadata or {}
    )

def create_agent_plan_generated_log(
    session_id: str, 
    plan: List[PlanStep], 
    confidence: Optional[float] = None,
    chosen_step_id: Optional[str] = None,
    reasoning: Optional[str] = None,
    metadata: Optional[Dict[str, Any]] = None
) -> LogEvent:
    """Create a log event for when an agent generates a plan.
    
    Args:
        session_id: The session ID
        plan: The list of planned steps
        confidence: The agent's confidence in the plan (0.0 to 1.0)
        chosen_step_id: The ID of the chosen step (if any)
        reasoning: The agent's reasoning for the plan
        metadata: Additional metadata for the event
        
    Returns:
        A LogEvent instance
    """
    return LogEvent(
        session_id=session_id,
        event_type=EventType.AGENT_PLAN_GENERATED,
        agent_decision=AgentDecisionData(
            plan=plan,
            confidence_score=confidence,
            chosen_action_step_id=chosen_step_id,
            reasoning=reasoning
        ),
        metadata=metadata or {}
    )

def create_tool_call_start_log(
    session_id: str,
    tool_name: str,
    parameters: Dict[str, Any],
    metadata: Optional[Dict[str, Any]] = None
) -> LogEvent:
    """Create a log event for when a tool call starts.
    
    Args:
        session_id: The session ID
        tool_name: The name of the tool being called
        parameters: The parameters passed to the tool
        metadata: Additional metadata for the event
        
    Returns:
        A LogEvent instance
    """
    return LogEvent(
        session_id=session_id,
        event_type=EventType.TOOL_CALL_START,
        tool_call=ToolCallData(
            tool_name=tool_name,
            parameters=parameters,
            start_time=datetime.utcnow(),
            status="started"
        ),
        metadata=metadata or {}
    )

def create_tool_call_end_log(
    session_id: str,
    tool_name: str,
    parameters: Dict[str, Any],
    start_time: datetime,
    result: Optional[Dict[str, Any]] = None,
    status: str = "SUCCESS", # Expected "SUCCESS" or "FAILURE"
    error: Optional[str] = None,
    execution_time_ms: Optional[float] = None,
    tool_metadata: Optional[Dict[str, Any]] = None,
    metadata: Optional[Dict[str, Any]] = None
) -> LogEvent:
    """Create a log event for when a tool call ends.
    
    Args:
        session_id: The session ID
        tool_name: The name of the tool that was called
        parameters: The parameters passed to the tool
        start_time: When the tool execution started
        result: The result returned by the tool
        status: The status of the tool call ("SUCCESS" or "FAILURE")
        error: Error message if the tool call failed
        execution_time_ms: Execution time in milliseconds
        tool_metadata: Tool-specific metadata (e.g., attempt number)
        metadata: Additional metadata for the event (top-level LogEvent metadata)
        
    Returns:
        A LogEvent instance
    """
    # 1. Calculate parameters_hash
    # Ensure parameters is a dict, default to empty if None, to prevent json.dumps error
    parameters_to_hash = parameters if parameters is not None else {}
    parameters_json = json.dumps(parameters_to_hash, sort_keys=True, ensure_ascii=False)
    calculated_parameters_hash = hashlib.sha256(parameters_json.encode('utf-8')).hexdigest()

    # 2. Prepare data for ToolCallData
    current_end_time = datetime.utcnow()

    calculated_execution_time_ms = execution_time_ms
    if calculated_execution_time_ms is None and start_time:
        calculated_execution_time_ms = (current_end_time - start_time).total_seconds() * 1000

    internal_tool_data = ToolCallData(
        tool_name=tool_name,
        parameters=parameters_to_hash, # Pass original parameters to the model instance
        parameters_hash=calculated_parameters_hash, # Pass raw hash to the model instance
        start_time=start_time,
        end_time=current_end_time,
        status=status,
        result=result,
        error=error,
        execution_time_ms=calculated_execution_time_ms,
        metadata=tool_metadata # This is for tool_call.metadata
    )

    # 3. Construct the tool_call dictionary for the LogEvent
    log_tool_call_dict = internal_tool_data.model_dump(exclude_none=True)
    
    # Remove original 'parameters' from the dictionary to be logged
    if 'parameters' in log_tool_call_dict:
        del log_tool_call_dict['parameters']
    
    # Add "sha256-" prefix to parameters_hash for logging
    if internal_tool_data.parameters_hash: # Check if hash exists
        log_tool_call_dict['parameters_hash'] = f"sha256-{internal_tool_data.parameters_hash}"
    else:
        # If for some reason parameters_hash was None in the model (e.g. parameters were None and hash was empty)
        # ensure the key is not present or is None, depending on desired output for missing hash.
        # model_dump(exclude_none=True) should handle this, but being explicit is fine.
        if 'parameters_hash' in log_tool_call_dict: # It would be there if calculated_parameters_hash was an empty string
             del log_tool_call_dict['parameters_hash']


    # 4. Create and return the LogEvent
    return LogEvent(
        session_id=session_id,
        event_type=EventType.TOOL_CALL_END,
        tool_call=log_tool_call_dict, # Use the carefully constructed dictionary
        metadata=metadata or {} # This is for LogEvent.metadata
    )

def create_llm_query_log(
    session_id: str,
    model: str,
    prompt: str,
    temperature: Optional[float] = None,
    max_tokens: Optional[int] = None,
    system_prompt: Optional[str] = None,
    tools: Optional[List[Dict[str, Any]]] = None,
    metadata: Optional[Dict[str, Any]] = None
) -> LogEvent:
    """Create a log event for an LLM query.
    
    Args:
        session_id: The session ID
        model: The name/ID of the LLM model
        prompt: The prompt sent to the LLM
        temperature: The temperature setting used
        max_tokens: The maximum number of tokens to generate
        system_prompt: System prompt/instructions provided to the model
        tools: Tools/functions available to the model
        metadata: Additional metadata for the event
        
    Returns:
        A LogEvent instance
    """
    return LogEvent(
        session_id=session_id,
        event_type=EventType.LLM_QUERY,
        llm_query=LLMQueryData(
            model=model,
            prompt=prompt,
            temperature=temperature,
            max_tokens=max_tokens,
            system_prompt=system_prompt,
            tools=tools
        ),
        metadata=metadata or {}
    )

def create_llm_response_log(
    session_id: str,
    model: str,
    response: str,
    prompt: Optional[str] = None,
    prompt_tokens: Optional[int] = None,
    completion_tokens: Optional[int] = None,
    total_tokens: Optional[int] = None,
    finish_reason: Optional[str] = None,
    latency_ms: Optional[float] = None,
    metadata: Optional[Dict[str, Any]] = None
) -> LogEvent:
    """Create a log event for an LLM response.
    
    Args:
        session_id: The session ID
        model: The name/ID of the LLM model
        response: The response received from the LLM
        prompt: The prompt that generated this response
        prompt_tokens: Number of tokens in the prompt
        completion_tokens: Number of tokens in the completion
        total_tokens: Total tokens used (prompt + completion)
        finish_reason: Reason the model stopped generating
        latency_ms: Time taken to generate the response in milliseconds
        metadata: Additional metadata for the event
        
    Returns:
        A LogEvent instance
    """
    return LogEvent(
        session_id=session_id,
        event_type=EventType.LLM_RESPONSE,
        llm_response=LLMResponseData(
            model=model,
            response=response,
            prompt=prompt,
            prompt_tokens=prompt_tokens,
            completion_tokens=completion_tokens,
            total_tokens=total_tokens,
            finish_reason=finish_reason,
            latency_ms=latency_ms
        ),
        metadata=metadata or {}
    )

def create_agent_clarification_request_log(
    session_id: str,
    question: str,
    context: Optional[Dict[str, Any]] = None,
    required: bool = True,
    metadata: Optional[Dict[str, Any]] = None
) -> LogEvent:
    """Create a log event when an agent requests clarification.
    
    Args:
        session_id: The session ID
        question: The clarification question asked by the agent
        context: Additional context for the clarification request
        required: Whether a response is required to continue
        metadata: Additional metadata for the event
        
    Returns:
        A LogEvent instance
    """
    return LogEvent(
        session_id=session_id,
        event_type=EventType.AGENT_CLARIFICATION_REQUEST,
        agent_clarification=AgentClarificationData(
            question=question,
            context=context,
            required=required
        ),
        metadata=metadata or {}
    )

def create_agent_goal_completed_log(
    session_id: str,
    status: Dict[str, Any],
    metadata: Optional[Dict[str, Any]] = None
) -> LogEvent:
    """Create a log event for when an agent completes a goal.
    
    Args:
        session_id: The session ID
        status: The status of the completed goal
        metadata: Additional metadata for the event
        
    Returns:
        A LogEvent instance
    """
    return LogEvent(
        session_id=session_id,
        event_type=EventType.AGENT_GOAL_COMPLETED,
        agent_decision=AgentDecisionData(
            plan=[],
            status=status
        ),
        metadata=metadata or {}
    )

def create_system_state_update_log(
    session_id: str,
    state_summary: Dict[str, Any],
    context_window: Optional[List[Dict[str, Any]]] = None,
    memory_summary: Optional[Dict[str, Any]] = None,
    metadata: Optional[Dict[str, Any]] = None
) -> LogEvent:
    """Create a log event for system state updates.
    
    Args:
        session_id: The session ID
        state_summary: Summary of the current system state
        context_window: Recent conversation history or context
        memory_summary: Summary of relevant memories
        metadata: Additional metadata for the event
        
    Returns:
        A LogEvent instance
    """
    return LogEvent(
        session_id=session_id,
        event_type=EventType.SYSTEM_STATE_UPDATE,
        system_state=SystemStateData(
            state_summary=state_summary,
            context_window=context_window,
            memory_summary=memory_summary
        ),
        metadata=metadata or {}
    )

def create_error_occurred_log(
    session_id: str,
    component: str,
    error_message: str,
    severity: str = "error",
    stack_trace: Optional[str] = None,
    metadata: Optional[Dict[str, Any]] = None
) -> LogEvent:
    """Create a log event for when an error occurs.
    
    Args:
        session_id: The session ID
        component: The component where the error occurred
        error_message: The error message
        severity: The severity of the error (info, warning, error, critical)
        stack_trace: The stack trace (if any)
        metadata: Additional metadata for the event
        
    Returns:
        A LogEvent instance
    """
    return LogEvent(
        session_id=session_id,
        event_type=EventType.ERROR_OCCURRED,
        error=ErrorContextData(
            component=component,
            error_message=error_message,
            severity=severity,
            stack_trace=stack_trace
        ),
        metadata=metadata or {}
    )

# Example usage (for testing this file directly):
if __name__ == '__main__':
    test_session_id = str(uuid.uuid4())
    print(f"Test Session ID: {test_session_id}")

    # 1. User Input
    user_log = create_user_input_log(test_session_id, "سلام، آب و هوای تهران چطور است؟", "get_weather")
    log_event(user_log)
    print(f"Logged: {user_log.event_type.value}")

    # 2. Agent Plan
    plan_steps = [
        PlanStep(step_id="1", tool_name="get_weather_tool", parameters={"city": "Tehran"}, reasoning="User asked for weather in Tehran.")
    ]
    plan_log = create_agent_plan_generated_log(test_session_id, plan_steps, confidence=0.9)
    log_event(plan_log)
    print(f"Logged: {plan_log.event_type.value}")

    # 3. LLM Query
    llm_q_log = create_llm_query_log(test_session_id, "Prompt to LLM to decide action", "gpt-4")
    log_event(llm_q_log)
    print(f"Logged: {llm_q_log.event_type.value}")

    # 4. LLM Response
    llm_r_log = create_llm_response_log(test_session_id, "Prompt...", "LLM Response...", "gpt-4", 150, 2500.5)
    log_event(llm_r_log)
    print(f"Logged: {llm_r_log.event_type.value}")

    # 5. Tool Call Start
    tool_start_log = create_tool_call_start_log(test_session_id, "get_weather_tool", {"city": "Tehran"})
    log_event(tool_start_log)
    print(f"Logged: {tool_start_log.event_type.value}")

    # Simulate tool execution
    import time
    time.sleep(0.1)

    # 6. Tool Call End (Success)
    tool_end_log_success = create_tool_call_end_log(
        session_id=test_session_id,
        tool_name="get_weather_tool",
        parameters={"city": "Tehran"},
        start_time=tool_start_log.tool_call.start_time, # Using start_time from the start log
        result={"temperature": "30C", "condition": "Sunny"},
        status="SUCCESS",
        execution_time_ms=120.3,
        tool_metadata={"attempt": 1},
        metadata={"correlation_id": "test-corr-id-success"}
    )
    log_event(tool_end_log_success)
    print(f"Logged: {tool_end_log_success.event_type.value} (SUCCESS)")

    # 7. Tool Call End (Failure)
    # For this failure example, let's create a dummy start_time as if a start event was logged
    dummy_start_time_failure = datetime.utcnow() - timedelta(seconds=1) # Simulate it started 1 sec ago
    tool_end_log_failure = create_tool_call_end_log(
        session_id=test_session_id,
        tool_name="some_other_tool",
        parameters={"param": "value"},
        start_time=dummy_start_time_failure,
        result=None, # No result for failure
        status="FAILURE",
        execution_time_ms=50.0,
        error="Tool failed unexpectedly", # Modified to pass error as string directly
        tool_metadata={"attempt": 3},
        metadata={"correlation_id": "test-corr-id-failure"}
    )
    log_event(tool_end_log_failure)
    print(f"Logged: {tool_end_log_failure.event_type.value} (FAILURE)")

    # 8. Error Occurred
    error_log = create_error_occurred_log(test_session_id, "AgentOS", "Something went wrong during processing", "ERROR", "Full stack trace here...")
    log_event(error_log)
    print(f"Logged: {error_log.event_type.value}")

    # 9. Goal Completed
    goal_log = create_agent_goal_completed_log(test_session_id, {"summary": "Weather for Tehran retrieved successfully."})
    log_event(goal_log)
    print(f"Logged: {goal_log.event_type.value}")

    print(f"\nAll test logs written to individual files in {LOGS_DIR}")
