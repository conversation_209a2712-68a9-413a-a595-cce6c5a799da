import sys
import os
import time
import json
import shutil
from datetime import datetime
sys.path.append(r'd:/کتابخانه پایتون/2')
from mindlink import run_agent
from mindlink.tools.file_tools import safe_cleanup_directory

print("=" * 80)
print("MINDLINK AGENT PERFORMANCE TEST")
print("=" * 80)

# Measure the execution time of agent calls
def run_timed_test(goal, test_name):
    print(f"\nRunning test: {test_name}")
    print(f"Goal: {goal[:150]}..." if len(goal) > 150 else f"Goal: {goal}")
    
    start_time = time.time()
    success, result, history = run_agent(goal)
    end_time = time.time()
    
    execution_time = end_time - start_time
    
    print(f"Success: {success}")
    print(f"Execution time: {execution_time:.2f} seconds")
    print(f"Number of steps: {len(history)}")
    
    # Count token usage
    total_tokens = sum(msg.get("tokens", 0) for msg in history if isinstance(msg, dict) and "tokens" in msg)
    print(f"Total tokens used: {total_tokens}")
    
    return {
        "test_name": test_name,
        "success": success,
        "execution_time": execution_time,
        "steps": len(history),
        "tokens": total_tokens,
        "result_summary": result[:200] + "..." if len(result) > 200 else result
    }

# Create a directory for our test files
test_dir = "performance_test_results"
os.makedirs(test_dir, exist_ok=True)

# Define complex test cases
test_cases = [
    {
        "name": "File Operations Test", 
        "goal": (
            "Create a complex directory structure with the following:\n"
            "1. A root directory called 'project'\n"
            "2. Inside 'project', create subdirectories: 'src', 'tests', 'docs', and 'config'\n"
            "3. In 'src', create a file called 'main.py' with a simple Python program that prints 'Hello World'\n"
            "4. In 'tests', create a test file called 'test_main.py' that imports the main module and tests it\n"
            "5. In 'docs', create a README.md with documentation for the project\n"
            "6. In 'config', create a config.json file with some example configuration\n"
            "7. List all the files and directories you created\n"
            "8. Finally, read the content of each file to verify"
        )
    },
    {
        "name": "Complex Reasoning Test",
        "goal": (
            "Create a Python script that solves the following problem:\n\n"
            "Given an array of integers, find all unique triplets in the array that sum to a specific target value.\n"
            "For example, given the array [1, 0, -1, 2, -2, -4] and a target of 0, return [[-4, 2, 2], [-2, 0, 2], [-2, 1, 1], [-1, 0, 1]].\n\n"
            "The solution should:\n"
            "1. Have O(n^2) time complexity (better than the naive O(n^3) approach)\n"
            "2. Handle duplicate values correctly\n"
            "3. Include detailed comments explaining the algorithm\n"
            "4. Include test cases\n"
            "5. After writing the code, analyze its time and space complexity"
        )
    },
    {
        "name": "Multi-Step Planning Test",
        "goal": (
            "Design a simplified library management system with the following requirements:\n\n"
            "1. Create a Python module structure with the following classes: Book, Member, Library\n"
            "2. The Book class should have attributes: id, title, author, available\n"
            "3. The Member class should have: id, name, books_borrowed\n"
            "4. The Library class should manage books and members with methods for borrowing/returning books\n"
            "5. Implement proper error handling for cases like borrowing an unavailable book\n"
            "6. Include docstrings for all classes and methods\n"
            "7. Create a simple demo script that shows the functionality\n"
            "8. Write unit tests for the Library class\n"
            "9. Generate a README file with usage examples"
        )
    }
]

# Run all tests and collect results
results = []
for test_case in test_cases:
    # Clean any previous test files using the improved safe_cleanup_directory function
    test_case_dir = os.path.join(test_dir, test_case["name"].replace(" ", "_").lower())
    os.makedirs(test_case_dir, exist_ok=True)
    safe_cleanup_directory(test_case_dir, recreate=True)
    
    # Switch to test case directory
    original_dir = os.getcwd()
    os.chdir(test_case_dir)
    
    try:
        # Run the test
        result = run_timed_test(test_case["goal"], test_case["name"])
        results.append(result)
    except Exception as e:
        print(f"Error running test '{test_case['name']}': {e}")
    finally:
        # Go back to the original directory
        os.chdir(original_dir)

# Generate a report
report = {
    "timestamp": datetime.now().isoformat(),
    "test_results": results,
    "summary": {
        "total_tests": len(results),
        "successful_tests": sum(1 for r in results if r["success"]),
        "average_execution_time": sum(r["execution_time"] for r in results) / len(results) if results else 0,
        "average_steps": sum(r["steps"] for r in results) / len(results) if results else 0,
        "average_tokens": sum(r["tokens"] for r in results) / len(results) if results else 0,
    }
}

# Print summary
print("\n" + "=" * 80)
print("TEST SUMMARY")
print("=" * 80)
print(f"Total tests: {report['summary']['total_tests']}")
print(f"Successful tests: {report['summary']['successful_tests']}")
print(f"Average execution time: {report['summary']['average_execution_time']:.2f} seconds")
print(f"Average steps: {report['summary']['average_steps']:.1f}")
print(f"Average tokens: {report['summary']['average_tokens']:.1f}")

# Save report to file
with open(os.path.join(original_dir, "performance_test_report.json"), "w") as f:
    json.dump(report, f, indent=2)

print(f"\nDetailed report saved to performance_test_report.json")
print("=" * 80) 