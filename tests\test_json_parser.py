"""
Tests for MindLink Agent Core JSON parser.
"""

import unittest
import json # For comparing parsed objects

from mindlink.utils.json_parser import parse_llm_response, extract_json_from_text, create_fallback_request

# Helper for generating a large code string
LARGE_PYTHON_CODE_BLOCK = """
def complex_function(a, b, c):
    # This is a complex function with multiple nested structures.
    # It's designed to be a large string embedded within JSON.
    result = 0
    if a > 10:
        for i in range(a):
            if b < 5:
                result += (i * b) / (c + 1)
                # Check for specific conditions
                if result % 2 == 0:
                    print(f"Intermediate even result: {result}")
                else:
                    # Handle odd results with a nested loop
                    for j in range(b):
                        result -= j
                        if c % (j + 1) == 0:
                            # Deeply nested condition
                            result *= (c // (j + 1))
                            # Example of string with quotes: "Hello World"
                            # Example of escaped quotes: \\"Escaped\\"
                            dummy_str = "This is a string with \\"quotes\\" and newlines.\\n"
                            print(dummy_str * (result % 3 + 1))
            else:
                result -= (i * a) // (c + 1)
                if result < 0:
                    result = abs(result)
                    # Another level of nesting
                    k = 0
                    while k < 5:
                        result += k
                        k += 1
                        if k == 3 and result > 100:
                            # A comment within a loop
                            # Let's add more lines to make it larger
                            # Line 1
                            # Line 2
                            # ... (imagine many more lines here to reach 500-1000)
                            # Line 500
                            break # Exit while loop
    else:
        result = (a + b + c) * 100
        # A simple else block
        # Adding more lines for size
        # Python code can have many forms.
        # This includes list comprehensions, dicts, etc.
        my_list = [x*x for x in range(a if a > 0 else 1)]
        my_dict = {f"key_{y}": y for y in range(b if b > 0 else 1)}
        if not my_list and not my_dict:
            result = -1
            
    # Final processing
    # This should simulate a reasonably complex and large code block.
    # Line X
    # Line Y
    # ...
    # Line Z (to ensure it's large)
    # Actual line count will depend on loop iterations in real use,
    # but for testing string embedding, the raw string length matters.
    # Let's make it a bit longer explicitly for the test.
    # Repeat a block of comments to increase size.
    # Section 1
    # Line
    # Line
    # Line
    # Section 2
    # Line
    # Line
    # Line
    # Section 3
    # Line
    # Line
    # Line
    # Section 4
    # Line
    # Line
    # Line
    # Section 5 (enough to make it a "large" string)
    # Line
    # Line
    # Line
    return result

# Example usage:
# print(complex_function(15, 3, 2))
# print(complex_function(5, 10, 1))
""" * 5 # Repeating the block to make it substantially large for testing

class TestJsonParser(unittest.TestCase):
    """
    Test JSON parser utilities.
    """
    
    def test_extract_json_from_text_with_backticks(self):
        """
        Test extracting JSON from text with backticks.
        """
        text = """
        I'll use the create_file tool to create a new file.
        
        ```json
        {
          "action": {
            "tool_name": "create_file",
            "parameters": {
              "path": "hello.txt",
              "content": "Hello, MindLink!"
            }
          },
          "reasoning": "I need to create a file as requested"
        }
        ```
        """
        
        json_str = extract_json_from_text(text)
        self.assertIsNotNone(json_str, "JSON string should be extracted")
        if json_str: # To help type checker and avoid error on None
            self.assertIn("create_file", json_str) 
    
    def test_extract_json_from_text_without_backticks(self):
        """
        Test extracting JSON from text without backticks.
        """
        text = """
        I'll use the create_file tool to create a new file.
        
        {
          "action": {
            "tool_name": "create_file",
            "parameters": {
              "path": "hello.txt",
              "content": "Hello, MindLink!"
            }
          },
          "reasoning": "I need to create a file as requested"
        }
        """
        
        json_str = extract_json_from_text(text)
        self.assertIsNotNone(json_str, "JSON string should be extracted")
        if json_str:
            self.assertIn("create_file", json_str)
    
    def test_parse_llm_response_valid(self):
        """
        Test parsing a valid LLM response.
        """
        response = """
        ```json
        {
          "action": {
            "tool_name": "create_file",
            "parameters": {
              "path": "hello.txt",
              "content": "Hello, MindLink!"
            }
          },
          "reasoning": "I need to create a file as requested"
        }
        ```
        """
        
        request, error = parse_llm_response(response)
        
        self.assertIsNone(error)
        self.assertIsNotNone(request)
        if request: 
            self.assertEqual(request.action.tool_name, "create_file")
            self.assertEqual(request.action.parameters["path"], "hello.txt")
            self.assertEqual(request.action.parameters["content"], "Hello, MindLink!")
            self.assertEqual(request.reasoning, "I need to create a file as requested")
    
    def test_parse_llm_response_invalid_trailing_comma(self):
        """
        Test parsing an LLM response with trailing commas within a fenced block.
        It should be sanitized and parsed correctly.
        """
        response = """
        Some introductory text from the LLM.
        ```json
        {
          "action": {
            "tool_name": "create_file",
            "parameters": {
              "path": "hello.txt",
              "content": "Hello, MindLink!"
            },
          },
          "reasoning": "I need to create a file as requested"
        }
        ```
        And some concluding text.
        """
        
        request, error = parse_llm_response(response)
        
        self.assertIsNone(error, f"Error parsing response: {error}")
        self.assertIsNotNone(request)
        if request: 
            self.assertEqual(request.action.tool_name, "create_file")
            self.assertEqual(request.action.parameters.get("path"), "hello.txt")
            self.assertEqual(request.action.parameters.get("content"), "Hello, MindLink!")
            self.assertEqual(request.reasoning, "I need to create a file as requested")

    def test_create_fallback_request_truncation_direct(self):
        """
        Test that create_fallback_request truncates long error messages when called directly.
        """
        long_error_message = "b" * 1000 
        expected_truncated_message_length = 500
        
        fallback_request = create_fallback_request(long_error_message)
        
        self.assertEqual(fallback_request.action.tool_name, "echo")
        
        action_params = fallback_request.action.parameters
        self.assertIn("message", action_params)
        
        prefix = "Error parsing response: "
        suffix = ". Please try again with proper JSON format."
        
        self.assertTrue(action_params["message"].startswith(prefix))
        self.assertTrue(action_params["message"].endswith(suffix))
        
        truncated_part = action_params["message"][len(prefix):-len(suffix)]
        
        self.assertTrue(len(truncated_part) == expected_truncated_message_length + 3, 
                        f"Truncated message part '{truncated_part}' has unexpected length. Expected {expected_truncated_message_length + 3}, got {len(truncated_part)}")
        self.assertTrue(truncated_part.endswith("..."), "Truncated message should end with '...'")
        self.assertEqual(truncated_part[:-3], "b" * expected_truncated_message_length, 
                         "Base of truncated message is not as expected.")

    # --- New tests for large embedded code strings ---

    def _assert_json_extraction(self, input_text, expected_json_obj, msg=""):
        extracted_json_str = extract_json_from_text(input_text)
        self.assertIsNotNone(extracted_json_str, f"{msg}: JSON string should be extracted, but got None. Input: {input_text[:300]}...")
        if extracted_json_str:
            try:
                extracted_obj = json.loads(extracted_json_str)
                self.assertEqual(extracted_obj, expected_json_obj, f"{msg}: Parsed JSON objects do not match.")
            except json.JSONDecodeError as e:
                self.fail(f"{msg}: Extracted string is not valid JSON: {e}. String: {extracted_json_str[:300]}...")

    def test_fenced_json_with_large_code_block(self):
        """Scenario 1: Large code block within fenced JSON (with lang specifier)."""
        expected_obj = {"tool": "create_file", "content": LARGE_PYTHON_CODE_BLOCK, "path": "script.py"}
        json_string = json.dumps(expected_obj) # Escapes newlines etc.
        text_input = f"Some text before.\n```json\n{json_string}\n```\nSome text after."
        self._assert_json_extraction(text_input, expected_obj, "Fenced JSON with lang specifier")

    def test_fenced_json_with_large_code_block_no_lang(self):
        """Scenario 1 Variation: Large code block within fenced JSON (no lang specifier)."""
        expected_obj = {"code": LARGE_PYTHON_CODE_BLOCK, "filename": "test.py"}
        json_string = json.dumps(expected_obj)
        text_input = f"```\n{json_string}\n```"
        self._assert_json_extraction(text_input, expected_obj, "Fenced JSON no lang specifier")

    def test_unfenced_json_with_large_code_block(self):
        """Scenario 2: Large code block within raw JSON (no fences)."""
        expected_obj = {"script_content": LARGE_PYTHON_CODE_BLOCK, "details": {"type": "python"}}
        json_string = json.dumps(expected_obj)
        # Input is just the JSON string for this test of balanced block extraction
        self._assert_json_extraction(json_string, expected_obj, "Unfenced JSON")

    def test_fenced_json_large_code_trailing_comma(self):
        """Scenario 3: Fenced JSON with large code and a trailing comma."""
        # Note: json.dumps doesn't add trailing commas. We construct it manually.
        expected_obj = {"code": LARGE_PYTHON_CODE_BLOCK, "type": "python_script"}
        # Manually create JSON string with trailing comma inside the main object
        json_string_with_comma = f'{{\n  "code": {json.dumps(LARGE_PYTHON_CODE_BLOCK)},\n  "type": "python_script",\n}}' # Trailing comma after "type"
        
        text_input = f"```json\n{json_string_with_comma}\n```"
        self._assert_json_extraction(text_input, expected_obj, "Fenced JSON with trailing comma")

    def test_unfenced_json_large_code_trailing_comma(self):
        """Scenario 3: Unfenced JSON with large code and a trailing comma."""
        expected_obj = {"code": LARGE_PYTHON_CODE_BLOCK, "lang": "python"}
        json_string_with_comma = f'{{\n  "code": {json.dumps(LARGE_PYTHON_CODE_BLOCK)},\n  "lang": "python",\n}}'
        self._assert_json_extraction(json_string_with_comma, expected_obj, "Unfenced JSON with trailing comma")
        
    def test_multiple_json_blocks_with_large_code(self):
        """Scenario 4: Multiple JSON blocks, one with large code. Expect last valid one."""
        first_obj = {"message": "This is the first JSON."}
        second_obj_large_code = {"id": "json2", "code_payload": LARGE_PYTHON_CODE_BLOCK, "comment": "This one has the large code."}
        
        first_json_str = json.dumps(first_obj)
        second_json_str_large_code = json.dumps(second_obj_large_code)

        text_input = f"""
        Some garbage text here.
        Then a JSON block:
        ```json
        {first_json_str}
        ```
        More text and explanation.
        And here is another, more important JSON block:
        ```json
        {second_json_str_large_code}
        ```
        Final comments.
        """
        # The current extract_json_from_text processes reversed matches from finditer/findall.
        # So, it should find the *last* valid JSON block from fences first.
        self._assert_json_extraction(text_input, second_obj_large_code, "Multiple JSON blocks, expecting last")

    def test_json_within_html_pre_code_large_code(self):
        """Test JSON with large code embedded in HTML <pre><code> block."""
        expected_obj = {"id": "html_test", "code": LARGE_PYTHON_CODE_BLOCK}
        json_string = json.dumps(expected_obj)
        text_input = f"<p>Some HTML</p><pre><code>{json_string}</code></pre><p>More HTML</p>"
        self._assert_json_extraction(text_input, expected_obj, "JSON in HTML pre-code block")


if __name__ == "__main__":
    unittest.main()
