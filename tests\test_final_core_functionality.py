"""
Final test for core functionality of MindLink Agent Core.

This test focuses on verifying that the core functionality of the library works correctly,
including the AgentOS, tools, and models.
"""

import pytest
import os
import tempfile
import shutil
import json
from pathlib import Path

# Import core components
from mindlink.agent import AgentOS
from mindlink.models.openrouter import OpenRouterModel
from mindlink.config import DEFAULT_SYSTEM_PROMPT

# Import tools
from mindlink.tools.file_tools import (
    TransactionManager,
    create_file,
    read_file,
    list_files,
    path_exists,
    CreateFileTool,
    ReadFileTool,
    ListFilesTool
)
from mindlink.tools.base import tool_registry

# Import schemas
from mindlink.schemas.mindlink import MindLinkRequest, MindLinkResponse, Action
from mindlink.executor import ToolExecutor


class MockLLM(OpenRouterModel):
    """Mock LLM for testing AgentOS."""

    def __init__(self, temp_dir):
        super().__init__(model_name="deepseek-r1")  # Use a valid model name
        self.response_index = 0
        self.temp_dir = temp_dir
        self.responses = [
            json.dumps({
                "action": {
                    "tool_name": "create_file",
                    "parameters": {
                        "path": os.path.join(self.temp_dir, "agent_test.txt"),
                        "content": "Created by AgentOS"
                    }
                },
                "reasoning": "I need to create a file"
            }),
            json.dumps({
                "action": {
                    "tool_name": "read_file",
                    "parameters": {
                        "path": os.path.join(self.temp_dir, "agent_test.txt")
                    }
                },
                "reasoning": "I need to read the file I just created"
            }),
            json.dumps({
                "action": {
                    "tool_name": "finish",
                    "parameters": {
                        "result": "Task completed successfully"
                    }
                },
                "reasoning": "I have completed the task"
            })
        ]

    def generate(self, system_prompt, user_prompt, history=None):
        response = self.responses[self.response_index]
        self.response_index = (self.response_index + 1) % len(self.responses)
        return response


def test_tool_registry():
    """Test that all expected tools are registered."""
    expected_tools = [
        "create_file",
        "read_file",
        "list_files",
        "path_exists",
        "finish"
    ]

    for tool_name in expected_tools:
        assert tool_name in tool_registry, f"Tool {tool_name} not found in registry"


def test_file_tools():
    """Test that file tools work correctly."""
    # Create a temporary directory
    temp_dir = tempfile.mkdtemp()
    try:
        # Test CreateFileTool
        create_tool = CreateFileTool()
        result = create_tool.execute(
            path=os.path.join(temp_dir, "test.txt"),
            content="Test content"
        )
        assert result["status"] == "success"

        # Test ReadFileTool
        read_tool = ReadFileTool()
        result = read_tool.execute(path=os.path.join(temp_dir, "test.txt"))
        assert result["status"] == "success"
        assert result["observation"] == "Test content"

        # Test ListFilesTool
        list_tool = ListFilesTool()
        result = list_tool.execute(directory=temp_dir)
        assert result["status"] == "success"
        assert "test.txt" in result["observation"]
    finally:
        # Clean up
        shutil.rmtree(temp_dir)


def test_tool_executor():
    """Test that ToolExecutor works correctly."""
    # Create a temporary directory
    temp_dir = tempfile.mkdtemp()
    try:
        executor = ToolExecutor()

        # Test create_file
        request = MindLinkRequest(
            action=Action(
                tool_name="create_file",
                parameters={
                    "path": os.path.join(temp_dir, "executor_test.txt"),
                    "content": "Created by ToolExecutor"
                }
            ),
            reasoning="Testing ToolExecutor with create_file"
        )
        response = executor.execute(request)
        assert response.status == "success"

        # Test read_file
        request = MindLinkRequest(
            action=Action(
                tool_name="read_file",
                parameters={
                    "path": os.path.join(temp_dir, "executor_test.txt")
                }
            ),
            reasoning="Testing ToolExecutor with read_file"
        )
        response = executor.execute(request)
        assert response.status == "success"
        assert response.observation == "Created by ToolExecutor"
    finally:
        # Clean up
        shutil.rmtree(temp_dir)


def test_agent_os():
    """Test that AgentOS works correctly."""
    # Create a temporary directory
    temp_dir = tempfile.mkdtemp()
    try:
        # Create AgentOS with mock LLM
        mock_llm = MockLLM(temp_dir)
        agent = AgentOS(
            llm=mock_llm,
            system_prompt_template=DEFAULT_SYSTEM_PROMPT,
            max_steps=10
        )

        # Run the agent
        success, result, history = agent.run("Create a file and read it")

        # Verify results
        assert success, "AgentOS should have succeeded"
        assert "Task completed successfully" in result
        assert len(history) == 3
        assert history[0]["request"].action.tool_name == "create_file"
        assert history[1]["request"].action.tool_name == "read_file"
        assert history[2]["request"].action.tool_name == "finish"

        # Verify file was created
        assert os.path.exists(os.path.join(temp_dir, "agent_test.txt"))
        with open(os.path.join(temp_dir, "agent_test.txt"), "r") as f:
            content = f.read()
            assert content == "Created by AgentOS"
    finally:
        # Clean up
        shutil.rmtree(temp_dir)


if __name__ == "__main__":
    pytest.main(["-xvs", __file__])
