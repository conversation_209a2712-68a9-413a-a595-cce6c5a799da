import sys
sys.path.append('d:/کتابخانه پایتون/2-')

# Import all tools to ensure they're registered
from mindlink.tools import *
from mindlink.tools.base import tool_registry

print("Available tools in registry:")
for tool_name, tool_cls in tool_registry.items():
    print(f"- {tool_name}: {tool_cls.__name__}")

print(f"\nTotal tools: {len(tool_registry)}")

# Check if generate_large_file is available
if 'generate_large_file' in tool_registry:
    print("\n✓ generate_large_file tool is available")
else:
    print("\n✗ generate_large_file tool is NOT available")
    
# Check if file_write is available
if 'file_write' in tool_registry:
    print("✓ file_write tool is available")
else:
    print("✗ file_write tool is NOT available")