import os
import pytest
from mindlink.tools.batch_execute_tool import BatchExecuteTool


def test_batch_execute_rollback_on_error(tmp_path):
    """
    Ensure that if any action in the batch fails, all previous operations are rolled back.
    """
    file_path = tmp_path / "test.txt"
    plan = [
        {"tool_name": "create_file", "parameters": {"path": str(file_path), "content": "hello"}},
        {"tool_name": "run_shell_command", "parameters": {"command": "exit 1"}}
    ]
    tool = BatchExecuteTool()
    result = tool.execute(plan=plan)
    assert result["status"] == "error"
    # The file created by the first action should not exist after rollback
    assert not file_path.exists()


def test_batch_execute_unknown_tool():
    """
    Ensure that plans with unknown tools are rejected immediately.
    """
    plan = [{"tool_name": "unknown_tool", "parameters": {}}]
    tool = BatchExecuteTool()
    result = tool.execute(plan=plan)
    assert result["status"] == "error"
    assert "Tool 'unknown_tool' not found" in result.get("error", "") 