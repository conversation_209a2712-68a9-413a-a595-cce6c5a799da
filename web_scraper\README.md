# Web Scraper

This is a simple web scraper that fetches the content from a given URL, extracts all links from the page, and saves them to a CSV file.

## Usage

1. Ensure you have Python installed on your system.
2. Install the required libraries:
   ```bash
   pip install requests beautifulsoup4
   ```
3. Run the script with the URL as an argument:
   ```bash
   python main.py <URL>
   ```

## Example

```bash
python main.py https://example.com
```

This will create a `links.csv` file with all the links extracted from the page.