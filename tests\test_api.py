import pytest
import os
from fastapi.testclient import Test<PERSON>lient
from mindlink.main import app
import mindlink.tools.file_tools as ft

@pytest.fixture(autouse=True)
def clear_file_caches():
    """Ensure file caches are cleared before each test in this module."""
    if hasattr(ft, '_read_file_cache'):
        ft._read_file_cache.clear()
    if hasattr(ft, '_persistent_read_file_cache'):
        ft._persistent_read_file_cache.clear()
    # If the caches might not exist yet (e.g., module not fully imported or initialized),
    # this fixture won't create them, only clear if they exist.

def test_list_files(tmp_path):
    # Setup a temporary directory with one file
    test_dir = tmp_path / "test_dir"
    test_dir.mkdir()
    file_path = test_dir / "file1.txt"
    file_path.write_text("content")

    with TestClient(app) as client:
        response = client.post(
            "/invoke_tool",
            json={"action": {"tool_name": "list_files", "parameters": {"directory": str(test_dir)}}}
        )
    assert response.status_code == 200
    data = response.json()
    assert "file1.txt" in data["observation"]
    assert data["status"] == "success"

def test_create_and_read_file(tmp_path):
    # Setup directory
    test_dir = tmp_path / "test_dir2"
    test_dir.mkdir()
    file_path = test_dir / "file2.txt"

    with TestClient(app) as client:
        # Create file
        create_resp = client.post(
            "/invoke_tool",
            json={"action": {"tool_name": "create_file", "parameters": {"path": str(file_path), "content": "hello"}}}
        )
        assert create_resp.status_code == 200
        create_data = create_resp.json()
        assert create_data["status"] == "success"

        # Read file
        read_resp = client.post(
            "/invoke_tool",
            json={"action": {"tool_name": "read_file", "parameters": {"path": str(file_path)}}}
        )
    assert read_resp.status_code == 200
    read_data = read_resp.json()
    assert "hello" in read_data["observation"]
