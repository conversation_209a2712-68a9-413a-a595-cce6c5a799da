import pytest
import mindlink.tools.file_tools as ft
from pathlib import Path

def test_read_file_caching(tmp_path, monkeypatch):
    """Test that read_file caches file contents and uses cache across reads until cleared."""
    # Monkeypatch the SAFE_BASE_DIR to point to a temporary directory
    monkeypatch.setattr(ft, 'SAFE_BASE_DIR', tmp_path)
    # Ensure cache is empty
    ft._read_file_cache.clear()
    # Create a test file under tmp_path
    filename = 'test_cache.txt'
    filepath = tmp_path / filename
    filepath.write_text('first_version', encoding='utf-8')
    # First read should load from disk
    result1 = ft.read_file(filename)
    assert result1 == 'first_version'
    # Modify file on disk
    filepath.write_text('second_version', encoding='utf-8')
    # Second read should return cached content, not updated value
    result2 = ft.read_file(filename)
    assert result2 == 'first_version'
    # Clear cache and read again to get updated content
    ft._read_file_cache.clear()
    result3 = ft.read_file(filename)
    assert result3 == 'second_version' 


def test_read_file_persistent_caching(tmp_path, monkeypatch):
    """Test that read_file with persist_cache=True uses the persistent cache."""
    # Monkeypatch the SAFE_BASE_DIR to point to a temporary directory
    monkeypatch.setattr(ft, 'SAFE_BASE_DIR', tmp_path)
    # Ensure caches are empty
    ft._read_file_cache.clear()
    if hasattr(ft, '_persistent_read_file_cache'):
        ft._persistent_read_file_cache.clear()
    else:
        # If the attribute doesn't exist, create it for the test, though this indicates an issue
        # in the main code or previous steps if it's expected to be there.
        ft._persistent_read_file_cache = {}

    filename = 'test_persistent_cache.txt'
    filepath = tmp_path / filename
    safe_filepath_str = str(ft.ensure_safe_path(str(filepath)))

    # 1. Initial read with persist_cache=True
    filepath.write_text('content_v1', encoding='utf-8')
    result1 = ft.read_file(str(filepath), persist_cache=True)
    assert result1 == 'content_v1'
    assert safe_filepath_str in ft._read_file_cache
    assert ft._read_file_cache[safe_filepath_str] == 'content_v1'
    assert safe_filepath_str in ft._persistent_read_file_cache
    assert ft._persistent_read_file_cache[safe_filepath_str] == 'content_v1'

    # 2. Modify file on disk
    filepath.write_text('content_v2', encoding='utf-8')

    # 3. Read again (default persist_cache=False) - should come from persistent cache
    result2 = ft.read_file(str(filepath))
    assert result2 == 'content_v1' # Persistently cached value
    # Regular cache might also have it if not cleared, but persistent takes precedence
    assert safe_filepath_str in ft._persistent_read_file_cache 

    # 4. Read again with persist_cache=True - should still come from persistent cache
    result3 = ft.read_file(str(filepath), persist_cache=True)
    assert result3 == 'content_v1' # Persistently cached value

    # 5. Clear only the regular cache
    ft._read_file_cache.clear()
    assert safe_filepath_str not in ft._read_file_cache
    assert safe_filepath_str in ft._persistent_read_file_cache # Still in persistent

    # 6. Read again - should still come from persistent cache
    result4 = ft.read_file(str(filepath))
    assert result4 == 'content_v1'

    # 7. Clear persistent cache
    ft._persistent_read_file_cache.clear()
    assert safe_filepath_str not in ft._persistent_read_file_cache

    # 8. Read again (default persist_cache=False) - should load from disk (v2) into regular cache
    result5 = ft.read_file(str(filepath))
    assert result5 == 'content_v2'
    assert safe_filepath_str in ft._read_file_cache
    assert ft._read_file_cache[safe_filepath_str] == 'content_v2'
    assert safe_filepath_str not in ft._persistent_read_file_cache # Not in persistent

    # 9. Modify file on disk again
    filepath.write_text('content_v3', encoding='utf-8')
    ft._read_file_cache.clear() # Clear regular cache to ensure disk read

    # 10. Read again with persist_cache=True - should load from disk (v3) into both caches
    result6 = ft.read_file(str(filepath), persist_cache=True)
    assert result6 == 'content_v3'
    assert safe_filepath_str in ft._read_file_cache
    assert ft._read_file_cache[safe_filepath_str] == 'content_v3'
    assert safe_filepath_str in ft._persistent_read_file_cache
    assert ft._persistent_read_file_cache[safe_filepath_str] == 'content_v3'