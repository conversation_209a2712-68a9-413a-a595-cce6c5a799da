"""Test script for the enhanced logging system."""
import os
import uuid
import time
from datetime import datetime
from typing import List, Dict, Any, Optional

from mindlink.logging_utils import (
    LogEvent,
    EventType,
    PlanStep,
    create_user_input_log,
    create_user_feedback_log,
    create_agent_plan_generated_log,
    create_tool_call_start_log,
    create_tool_call_end_log,
    create_llm_query_log,
    create_llm_response_log,
    create_agent_clarification_request_log,
    create_agent_goal_completed_log,
    create_system_state_update_log,
    create_error_occurred_log,
    log_event,
    close_logs,
    convert_logs_to_json
)

def test_enhanced_logging():
    """Test the enhanced logging system with all event types."""
    # Create a test session
    session_id = str(uuid.uuid4())
    print(f"Test Session ID: {session_id}")
    
    # 1. Test user input logging
    user_input = create_user_input_log(
        session_id=session_id,
        raw_text="Hello, can you help me book a flight?",
        intent="book_flight",
        metadata={"source": "web", "user_id": "user123"}
    )
    log_event(user_input)
    print("✓ Logged user input")
    
    # 2. Test user feedback logging
    feedback = create_user_feedback_log(
        session_id=session_id,
        feedback_type="correction",
        corrected_action={"tool": "book_flight", "params": {"destination": "NYC"}},
        comment="Please book a flight to New York instead",
        implicit_feedback_notes="User corrected destination from SFO to NYC",
        metadata={"feedback_id": "fb_123"}
    )
    log_event(feedback)
    print("✓ Logged user feedback")
    
    # 3. Test agent plan generation
    plan_steps = [
        PlanStep(
            step_id="1",
            tool_name="search_flights",
            parameters={"destination": "NYC", "dates": "2023-12-15/2023-12-22"},
            reasoning="Searching for available flights to New York"
        ),
        PlanStep(
            step_id="2",
            tool_name="book_flight",
            parameters={"flight_id": "{step_1_output.best_flight_id}"},
            reasoning="Book the best available flight"
        )
    ]
    
    plan = create_agent_plan_generated_log(
        session_id=session_id,
        plan=plan_steps,
        confidence=0.9,
        chosen_step_id="1",
        reasoning="User wants to book a flight to New York",
        metadata={"plan_id": "plan_456"}
    )
    log_event(plan)
    print("✓ Logged agent plan")
    
    # 4. Test tool call start
    tool_start = create_tool_call_start_log(
        session_id=session_id,
        tool_name="search_flights",
        parameters={"destination": "NYC", "dates": "2023-12-15/2023-12-22"},
        metadata={"attempt": 1}
    )
    log_event(tool_start)
    print("✓ Logged tool call start")
    
    # Simulate tool execution
    time.sleep(0.1)
    
    # 5. Test tool call end
    tool_end = create_tool_call_end_log(
        session_id=session_id,
        tool_name="search_flights",
        parameters={"destination": "NYC", "dates": "2023-12-15/2023-12-22"},
        result={
            "flights": [
                {"id": "flt123", "airline": "Delta", "price": 299, "stops": 0},
                {"id": "flt456", "airline": "United", "price": 279, "stops": 1}
            ],
            "best_flight_id": "flt123"
        },
        status="completed",
        execution_time_ms=105.5,
        metadata={"attempt": 1}
    )
    log_event(tool_end)
    print("✓ Logged tool call end")
    
    # 6. Test LLM query
    llm_query = create_llm_query_log(
        session_id=session_id,
        model="gpt-4",
        prompt="What's the best flight option for NYC?",
        temperature=0.7,
        max_tokens=500,
        system_prompt="You are a helpful travel assistant.",
        tools=[{"name": "search_flights", "description": "Search for flights"}],
        metadata={"query_id": "q_789"}
    )
    log_event(llm_query)
    print("✓ Logged LLM query")
    
    # 7. Test LLM response
    llm_response = create_llm_response_log(
        session_id=session_id,
        model="gpt-4",
        response="Here's the best flight option: Delta flight #123 for $299 with no stops.",
        prompt="What's the best flight option for NYC?",
        prompt_tokens=25,
        completion_tokens=15,
        total_tokens=40,
        finish_reason="stop",
        latency_ms=1250.75,
        metadata={"query_id": "q_789"}
    )
    log_event(llm_response)
    print("✓ Logged LLM response")
    
    # 8. Test agent clarification request
    clarification = create_agent_clarification_request_log(
        session_id=session_id,
        question="Would you like to book the morning or afternoon flight?",
        context={"options": ["Morning (8:00 AM)", "Afternoon (2:00 PM)"]},
        required=True,
        metadata={"interaction_id": "int_101"}
    )
    log_event(clarification)
    print("✓ Logged clarification request")
    
    # 9. Test system state update
    state_update = create_system_state_update_log(
        session_id=session_id,
        state_summary={
            "conversation_turns": 5,
            "active_tools": ["search_flights"],
            "user_preferences": {"class": "economy", "window_seat": True}
        },
        context_window=[
            {"role": "user", "content": "Book me a flight to New York"},
            {"role": "assistant", "content": "I found some flights to New York..."}
        ],
        memory_summary={
            "user_mentions": ["New York", "flight"],
            "previous_bookings": ["LAX-JFK 2023-10-15"]
        },
        metadata={"state_version": "1.0"}
    )
    log_event(state_update)
    print("✓ Logged system state update")
    
    # 10. Test error logging
    try:
        # Simulate an error
        1 / 0
    except Exception as e:
        import traceback
        error = create_error_occurred_log(
            session_id=session_id,
            component="flight_booking",
            error_message=str(e),
            severity="error",
            stack_trace=traceback.format_exc(),
            metadata={"retry_count": 2}
        )
        log_event(error)
        print("✓ Logged error")
    
    # 11. Test goal completion
    goal_complete = create_agent_goal_completed_log(
        session_id=session_id,
        status={
            "success": True,
            "result": "Flight booked successfully",
            "booking_id": "BK123456",
            "details": {"flight": "DL123", "date": "2023-12-15", "passengers": 1}
        },
        metadata={"goal_id": "goal_202"}
    )
    log_event(goal_complete)
    print("✓ Logged goal completion")
    
    # Close logs
    close_logs()
    
    # Convert logs to JSON for inspection
    logs_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'mindlink', 'logs')
    json_files = convert_logs_to_json(logs_dir)
    
    print("\nTest completed successfully!")
    print(f"Log files created: {len(json_files)}")
    for json_file in json_files:
        print(f"- {json_file}")
    
    return session_id

if __name__ == "__main__":
    print("=== Testing Enhanced Logging System ===\n")
    test_enhanced_logging()
    print("\n=== Testing Tool Call End Event Serialization ===")
    test_tool_call_end_event_serialization() # Call the new test
    print("\n✓ Tool Call End Event Serialization test completed.")

def test_tool_call_end_event_serialization():
    """
    Tests the specific serialization of a tool_call_end event,
    ensuring all fields, including nested metadata and correct timestamps, are present.
    """
    import json # Ensure json is imported for this test
    # Imports are handled at the top of the file by the next SEARCH/REPLACE block

    session_id = str(uuid.uuid4())
    tool_name = "test_serialization_tool"
    parameters = {"input_param": "test_value", "numeric_param": 42}
    
    # Ensure start_time is timezone-aware (UTC) for consistent ISO formatting
    start_time = (datetime.now(timezone.utc) - timedelta(seconds=10))
    # Pydantic will include microseconds, so we keep them.
    
    # Approximate event_timestamp for assertion range, also timezone-aware
    # This is for the LogEvent's own timestamp
    approx_log_event_timestamp = datetime.now(timezone.utc)

    result = {"detail": "Serialization test successful", "item_count": 3}
    status = "SUCCESS"
    error_message = None # Explicitly None for a success case
    execution_time_ms = 9876.54
    tool_meta = {"retry_attempt": 1, "diagnostic_id": "diag-abc-123"}
    top_level_meta = {"test_run_id": "run-789", "trigger_source": "manual_test"}

    # Create the event
    log_event_obj = create_tool_call_end_log(
        session_id=session_id,
        tool_name=tool_name,
        parameters=parameters,
        start_time=start_time, 
        result=result,
        status=status,
        error=error_message,
        execution_time_ms=execution_time_ms,
        tool_metadata=tool_meta, 
        metadata=top_level_meta 
    )

    # Serialize to JSON
    # Pydantic v2 uses model_dump_json, v1 uses json()
    if hasattr(log_event_obj, "model_dump_json"):
        event_json_str = log_event_obj.model_dump_json(exclude_none=True)
    else:
        event_json_str = log_event_obj.json(exclude_none=True)


    # Parse back to Python dict
    parsed_event_dict = json.loads(event_json_str)

    # --- Assertions ---
    assert parsed_event_dict["event_type"] == EventType.TOOL_CALL_END.value
    assert parsed_event_dict["session_id"] == session_id
    assert "event_id" in parsed_event_dict
    
    # Assert LogEvent's own timestamp
    log_event_dt = datetime.fromisoformat(parsed_event_dict["timestamp"])
    assert abs((log_event_dt - approx_log_event_timestamp).total_seconds()) < 5, \
        f"LogEvent timestamp {log_event_dt} out of expected range around {approx_log_event_timestamp}"

    # Assert tool_call data
    tool_call_data = parsed_event_dict.get("tool_call")
    assert tool_call_data is not None, "tool_call object missing"
    
    assert tool_call_data["tool_name"] == tool_name
    assert tool_call_data["parameters"] == parameters
    
    # Assert ToolCallData timestamps
    # start_time should be exactly what we provided, in ISO format. Pydantic handles timezone.
    parsed_start_time = datetime.fromisoformat(tool_call_data["start_time"])
    assert parsed_start_time == start_time.astimezone(timezone.utc) # Compare timezone-aware datetimes
    
    assert "end_time" in tool_call_data
    tool_end_dt = datetime.fromisoformat(tool_call_data["end_time"])
    # The tool_end_dt is set by datetime.utcnow() inside create_tool_call_end_log, 
    # which is similar to approx_log_event_timestamp
    assert abs((tool_end_dt - approx_log_event_timestamp).total_seconds()) < 5, \
        f"ToolCallData end_time {tool_end_dt} out of expected range around {approx_log_event_timestamp}"
    assert tool_end_dt >= parsed_start_time, "Tool end_time must be after or same as start_time"

    assert tool_call_data["status"] == status
    assert tool_call_data["result"] == result
    assert tool_call_data.get("error") == error_message # Should be None or not present if exclude_none=True
    assert tool_call_data["execution_time_ms"] == execution_time_ms
    
    # Assert nested tool metadata (ToolCallData.metadata)
    assert tool_call_data["metadata"] == tool_meta, "ToolCallData.metadata mismatch"
    
    # Assert top-level event metadata (LogEvent.metadata)
    assert parsed_event_dict["metadata"] == top_level_meta, "LogEvent.metadata mismatch"

    print(f"✓ tool_call_end event for '{tool_name}' serialized and verified successfully.")
