"""
Development Workflow Simulation Test for MindLink Agent.

This test evaluates the MindLink agent's capabilities in a realistic software
development workflow, from requirements analysis to implementation, testing,
debugging, and documentation.
"""

import os
import sys
import time
import tempfile
import logging
import json
import re
from pathlib import Path
from typing import List, Dict, Any, Tuple
import unittest

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('development_workflow_test.log')
    ]
)
logger = logging.getLogger(__name__)

# Add the current directory to the path so we can import mindlink
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Try to import MindLink
try:
    from mindlink.agent import AgentOS
    from mindlink.models.openai import OpenAIModel
    from mindlink.models.openrouter import OpenRouterModel
    from mindlink.config import DEFAULT_SYSTEM_PROMPT
    
    IMPORTS_SUCCESSFUL = True
    logger.info("[SUCCESS] MindLink agent imported successfully")
except ImportError as e:
    IMPORTS_SUCCESSFUL = False
    logger.error(f"[ERROR] Failed to import MindLink: {e}")
    sys.exit(1)

# Define project requirements for the test
PROJECT_REQUIREMENTS = """
Create a weather data analysis tool with the following features:

1. Data Ingestion:
   - Load weather data from CSV files
   - Support for multiple data formats (CSV, JSON)
   - Basic data validation

2. Data Analysis:
   - Calculate basic statistics (min, max, average, median)
   - Find temperature trends over time
   - Identify weather anomalies

3. Visualization:
   - Generate simple text-based charts
   - Output summary reports

4. User Interface:
   - Command-line interface with help text
   - Support for configuration files

The tool should follow good software engineering practices including:
- Proper error handling
- Unit tests
- Documentation
- Modular design
"""

# Sample weather data for testing
SAMPLE_WEATHER_DATA = """date,temperature,humidity,pressure,weather_condition
2023-01-01,32,85,1012,Rainy
2023-01-02,28,65,1015,Cloudy
2023-01-03,35,70,1010,Sunny
2023-01-04,30,75,1013,Partly Cloudy
2023-01-05,26,80,1014,Rainy
2023-01-06,31,60,1016,Sunny
2023-01-07,33,65,1015,Sunny
"""

class DevelopmentMetrics:
    """Container for development workflow metrics."""
    def __init__(self):
        self.requirements_understanding = 0.0
        self.code_quality = 0.0
        self.test_coverage = 0.0
        self.documentation_quality = 0.0
        self.error_handling = 0.0
        self.debugging_effectiveness = 0.0
        self.iteration_efficiency = 0.0
        self.overall_score = 0.0
        
        # Time metrics
        self.requirement_analysis_time = 0.0
        self.implementation_time = 0.0
        self.testing_time = 0.0
        self.debugging_time = 0.0
        self.documentation_time = 0.0
        self.total_time = 0.0
        
        # Detailed results
        self.steps_taken = []
        self.code_issues = []
        self.test_results = {}

def initialize_agent():
    """Initialize the MindLink agent with available API keys."""
    # Try OpenAI first, then OpenRouter
    api_key = os.getenv("OPENAI_API_KEY")
    if api_key:
        logger.info("[INFO] Using OpenAI for agent")
        llm = OpenAIModel(api_key=api_key)
    else:
        api_key = os.getenv("OPENROUTER_API_KEY")
        if api_key:
            logger.info("[INFO] Using OpenRouter for agent")
            llm = OpenRouterModel(api_key=api_key)
        else:
            logger.error("[ERROR] No API keys found for any LLM provider")
            return None
    
    agent = AgentOS(
        llm=llm,
        system_prompt_template=DEFAULT_SYSTEM_PROMPT,
        max_steps=50  # Increased for complex development tasks
    )
    
    return agent

def analyze_requirements(agent, requirements, metrics):
    """Have the agent analyze project requirements."""
    logger.info("[PHASE] Requirement Analysis")
    
    prompt = f"""
    Analyze the following project requirements and create a design document that includes:
    1. A high-level architecture overview
    2. Key modules/components and their responsibilities
    3. Data structures and interfaces
    4. Implementation plan with prioritized features
    
    Requirements:
    {requirements}
    """
    
    start_time = time.time()
    success, result, history = agent.run(prompt)
    metrics.requirement_analysis_time = time.time() - start_time
    
    logger.info(f"Requirement analysis completed in {metrics.requirement_analysis_time:.2f} seconds")
    
    # Evaluate requirements understanding
    understanding_criteria = [
        "data ingestion" in result.lower(),
        "data analysis" in result.lower(),
        "visualization" in result.lower(),
        "user interface" in result.lower(),
        "architecture" in result.lower(),
        "module" in result.lower() or "component" in result.lower(),
        "csv" in result.lower() and "json" in result.lower(),
        "error handling" in result.lower(),
        "test" in result.lower(),
        "documentation" in result.lower()
    ]
    
    metrics.requirements_understanding = sum(1 for c in understanding_criteria if c) / len(understanding_criteria) * 100
    logger.info(f"Requirements understanding score: {metrics.requirements_understanding:.1f}%")
    
    return result

def implement_project(agent, requirements, design_doc, metrics):
    """Have the agent implement the project based on requirements and design."""
    logger.info("[PHASE] Implementation")
    
    prompt = f"""
    Implement the weather data analysis tool based on the requirements and design document.
    Create all the necessary files with proper code organization.
    
    Include:
    1. A main.py file as the entry point
    2. Modules for data loading, analysis, and visualization
    3. Basic test cases
    4. README.md with usage instructions
    
    Requirements:
    {requirements}
    
    Design Document:
    {design_doc}
    """
    
    start_time = time.time()
    success, result, history = agent.run(prompt)
    metrics.implementation_time = time.time() - start_time
    
    logger.info(f"Implementation completed in {metrics.implementation_time:.2f} seconds")
    
    # List created files
    created_files = []
    for entry in history:
        if hasattr(entry, 'request') and hasattr(entry['request'], 'action'):
            action = entry['request'].action
            if hasattr(action, 'tool_name') and action.tool_name == 'create_file':
                if hasattr(action, 'params') and 'path' in action.params:
                    created_files.append(action.params['path'])
    
    logger.info(f"Files created: {', '.join(created_files)}")
    
    return result, created_files

def create_test_data(agent, metrics):
    """Have the agent create test data for the project."""
    logger.info("[PHASE] Test Data Creation")
    
    prompt = """
    Create a sample weather data CSV file called 'weather_data.csv' with realistic data
    that can be used to test the weather data analysis tool. The file should have at least
    7 days of data with date, temperature, humidity, pressure, and weather_condition columns.
    """
    
    start_time = time.time()
    success, result, history = agent.run(prompt)
    test_data_time = time.time() - start_time
    
    logger.info(f"Test data creation completed in {test_data_time:.2f} seconds")
    
    # Check if weather_data.csv was created
    csv_created = os.path.exists("weather_data.csv")
    logger.info(f"Test data CSV file created: {csv_created}")
    
    # If not created by the agent, create it manually
    if not csv_created:
        with open("weather_data.csv", "w") as f:
            f.write(SAMPLE_WEATHER_DATA)
        logger.info("Created sample weather data manually")
    
    return result

def run_tests(agent, metrics):
    """Have the agent run tests for the project."""
    logger.info("[PHASE] Testing")
    
    prompt = """
    Run tests for the weather data analysis tool:
    1. Test data loading with the weather_data.csv file
    2. Test basic statistics calculation
    3. Test report generation
    4. Test error handling with invalid inputs
    
    Report any issues found during testing.
    """
    
    start_time = time.time()
    success, result, history = agent.run(prompt)
    metrics.testing_time = time.time() - start_time
    
    logger.info(f"Testing completed in {metrics.testing_time:.2f} seconds")
    
    # Analyze test coverage based on result
    test_types = [
        "data loading" in result.lower(),
        "statistics" in result.lower() or "calculation" in result.lower(),
        "report" in result.lower() or "visualization" in result.lower(),
        "error" in result.lower() or "exception" in result.lower() or "invalid" in result.lower()
    ]
    
    metrics.test_coverage = sum(1 for t in test_types if t) / len(test_types) * 100
    logger.info(f"Test coverage score: {metrics.test_coverage:.1f}%")
    
    # Extract test results from the response
    issues_found = "issue" in result.lower() or "error" in result.lower() or "fail" in result.lower()
    metrics.test_results["issues_found"] = issues_found
    metrics.test_results["test_output"] = result
    
    return result, issues_found

def fix_issues(agent, issues_report, metrics):
    """Have the agent fix issues found during testing."""
    logger.info("[PHASE] Debugging and Issue Resolution")
    
    prompt = f"""
    Fix the issues identified during testing of the weather data analysis tool:
    
    Testing Results:
    {issues_report}
    
    Identify the root causes of the issues and modify the necessary files to fix them.
    """
    
    start_time = time.time()
    success, result, history = agent.run(prompt)
    metrics.debugging_time = time.time() - start_time
    
    logger.info(f"Issue resolution completed in {metrics.debugging_time:.2f} seconds")
    
    # Evaluate debugging effectiveness
    fixed_issues = "fixed" in result.lower() or "resolved" in result.lower() or "corrected" in result.lower()
    metrics.debugging_effectiveness = 100 if fixed_issues else 0
    
    logger.info(f"Debugging effectiveness: {metrics.debugging_effectiveness:.1f}%")
    
    return result

def create_documentation(agent, metrics):
    """Have the agent create comprehensive documentation."""
    logger.info("[PHASE] Documentation")
    
    prompt = """
    Create comprehensive documentation for the weather data analysis tool:
    1. Update the README.md with detailed usage instructions
    2. Add code comments where needed
    3. Create a simple user guide explaining how to use each feature
    4. Document the data formats supported by the tool
    """
    
    start_time = time.time()
    success, result, history = agent.run(prompt)
    metrics.documentation_time = time.time() - start_time
    
    logger.info(f"Documentation completed in {metrics.documentation_time:.2f} seconds")
    
    # Evaluate documentation quality
    doc_criteria = [
        "usage" in result.lower() or "how to use" in result.lower(),
        "install" in result.lower() or "setup" in result.lower(),
        "feature" in result.lower() or "functionality" in result.lower(),
        "data format" in result.lower() or "input format" in result.lower(),
        "example" in result.lower(),
        "command" in result.lower() or "option" in result.lower()
    ]
    
    metrics.documentation_quality = sum(1 for c in doc_criteria if c) / len(doc_criteria) * 100
    logger.info(f"Documentation quality score: {metrics.documentation_quality:.1f}%")
    
    return result

def add_feature(agent, metrics):
    """Have the agent add a new feature to the project."""
    logger.info("[PHASE] Feature Addition")
    
    prompt = """
    Add a new feature to the weather data analysis tool:
    
    Implement a weather forecast prediction functionality that:
    1. Uses the historical data to make a simple prediction for the next day
    2. Bases the prediction on the average of the last 3 days' values
    3. Outputs the prediction in the same format as the input data
    4. Includes appropriate tests for this new functionality
    
    Update any necessary files and documentation.
    """
    
    start_time = time.time()
    success, result, history = agent.run(prompt)
    feature_addition_time = time.time() - start_time
    
    logger.info(f"Feature addition completed in {feature_addition_time:.2f} seconds")
    
    # Evaluate feature implementation
    feature_criteria = [
        "forecast" in result.lower() or "prediction" in result.lower(),
        "average" in result.lower() or "historical" in result.lower(),
        "test" in result.lower(),
        "implement" in result.lower() or "added" in result.lower() or "created" in result.lower(),
        "update" in result.lower() or "modified" in result.lower()
    ]
    
    feature_quality = sum(1 for c in feature_criteria if c) / len(feature_criteria) * 100
    logger.info(f"Feature implementation quality: {feature_quality:.1f}%")
    
    return result

def analyze_code_quality(agent, metrics):
    """Have the agent analyze code quality."""
    logger.info("[PHASE] Code Quality Analysis")
    
    prompt = """
    Analyze the code quality of the weather data analysis tool:
    1. Check for adherence to PEP 8 style guidelines
    2. Evaluate code organization and modularity
    3. Assess error handling and edge cases
    4. Review variable naming and documentation
    5. Suggest improvements where appropriate
    """
    
    start_time = time.time()
    success, result, history = agent.run(prompt)
    code_analysis_time = time.time() - start_time
    
    logger.info(f"Code quality analysis completed in {code_analysis_time:.2f} seconds")
    
    # Extract code quality issues
    issues = re.findall(r'(issue|problem|improvement|suggestion|recommendation):\s*([^\n]+)', result, re.IGNORECASE)
    metrics.code_issues = [issue[1].strip() for issue in issues]
    
    # Evaluate code quality
    quality_indicators = [
        "good" in result.lower() or "well" in result.lower() or "proper" in result.lower(),
        "modular" in result.lower() or "organized" in result.lower(),
        "readable" in result.lower() or "clear" in result.lower(),
        "documented" in result.lower() or "comments" in result.lower(),
        "error handling" in result.lower() or "exception" in result.lower()
    ]
    
    metrics.code_quality = sum(1 for i in quality_indicators if i) / len(quality_indicators) * 100
    
    # Adjust for number of issues found
    issue_penalty = min(len(metrics.code_issues) * 5, 30)  # Max 30% penalty for issues
    metrics.code_quality = max(0, metrics.code_quality - issue_penalty)
    
    logger.info(f"Code quality score: {metrics.code_quality:.1f}%")
    logger.info(f"Issues identified: {len(metrics.code_issues)}")
    
    return result

def calculate_overall_score(metrics):
    """Calculate the overall development workflow score."""
    weights = {
        'requirements_understanding': 0.15,
        'code_quality': 0.25,
        'test_coverage': 0.15,
        'documentation_quality': 0.15,
        'error_handling': 0.15,
        'debugging_effectiveness': 0.15
    }
    
    # Set error handling score based on other metrics
    metrics.error_handling = (metrics.code_quality + metrics.test_coverage) / 2
    
    # Calculate weighted score
    weighted_score = (
        weights['requirements_understanding'] * metrics.requirements_understanding +
        weights['code_quality'] * metrics.code_quality +
        weights['test_coverage'] * metrics.test_coverage +
        weights['documentation_quality'] * metrics.documentation_quality +
        weights['error_handling'] * metrics.error_handling +
        weights['debugging_effectiveness'] * metrics.debugging_effectiveness
    )
    
    metrics.overall_score = weighted_score
    
    # Calculate iteration efficiency
    total_development_time = (
        metrics.requirement_analysis_time +
        metrics.implementation_time +
        metrics.testing_time +
        metrics.debugging_time +
        metrics.documentation_time
    )
    metrics.total_time = total_development_time
    
    # Higher is better - based on ratio of overall score to development time
    # Normalized to 0-100 scale
    base_efficiency = metrics.overall_score / (total_development_time / 60)  # Per minute
    metrics.iteration_efficiency = min(100, base_efficiency * 2)  # Scale for readability
    
    return metrics.overall_score

def generate_report(metrics):
    """Generate a comprehensive development workflow report."""
    report = {
        "test_date": time.strftime("%Y-%m-%d %H:%M:%S"),
        "scores": {
            "requirements_understanding": metrics.requirements_understanding,
            "code_quality": metrics.code_quality,
            "test_coverage": metrics.test_coverage,
            "documentation_quality": metrics.documentation_quality,
            "error_handling": metrics.error_handling,
            "debugging_effectiveness": metrics.debugging_effectiveness,
            "iteration_efficiency": metrics.iteration_efficiency,
            "overall_score": metrics.overall_score
        },
        "time_metrics": {
            "requirement_analysis_time": metrics.requirement_analysis_time,
            "implementation_time": metrics.implementation_time,
            "testing_time": metrics.testing_time,
            "debugging_time": metrics.debugging_time,
            "documentation_time": metrics.documentation_time,
            "total_time": metrics.total_time
        },
        "issues": metrics.code_issues,
        "test_results": metrics.test_results
    }
    
    # Save JSON report
    with open("development_workflow_report.json", "w") as f:
        json.dump(report, f, indent=2)
    
    # Create Markdown report
    with open("development_workflow_report.md", "w") as f:
        f.write("# MindLink Agent Development Workflow Test Report\n\n")
        f.write(f"**Test Date:** {report['test_date']}\n\n")
        
        f.write("## Performance Scores\n\n")
        f.write(f"- **Overall Score:** {report['scores']['overall_score']:.1f}%\n")
        f.write(f"- **Requirements Understanding:** {report['scores']['requirements_understanding']:.1f}%\n")
        f.write(f"- **Code Quality:** {report['scores']['code_quality']:.1f}%\n")
        f.write(f"- **Test Coverage:** {report['scores']['test_coverage']:.1f}%\n")
        f.write(f"- **Documentation Quality:** {report['scores']['documentation_quality']:.1f}%\n")
        f.write(f"- **Error Handling:** {report['scores']['error_handling']:.1f}%\n")
        f.write(f"- **Debugging Effectiveness:** {report['scores']['debugging_effectiveness']:.1f}%\n")
        f.write(f"- **Iteration Efficiency:** {report['scores']['iteration_efficiency']:.1f}%\n\n")
        
        f.write("## Time Metrics\n\n")
        f.write(f"- **Requirement Analysis:** {report['time_metrics']['requirement_analysis_time']:.2f} seconds\n")
        f.write(f"- **Implementation:** {report['time_metrics']['implementation_time']:.2f} seconds\n")
        f.write(f"- **Testing:** {report['time_metrics']['testing_time']:.2f} seconds\n")
        f.write(f"- **Debugging:** {report['time_metrics']['debugging_time']:.2f} seconds\n")
        f.write(f"- **Documentation:** {report['time_metrics']['documentation_time']:.2f} seconds\n")
        f.write(f"- **Total Development Time:** {report['time_metrics']['total_time']:.2f} seconds\n\n")
        
        f.write("## Code Quality Issues\n\n")
        if report['issues']:
            for issue in report['issues']:
                f.write(f"- {issue}\n")
        else:
            f.write("No significant code quality issues identified.\n")
        
        # Overall quality rating
        score = report['scores']['overall_score']
        if score >= 90:
            quality = "EXCELLENT"
        elif score >= 80:
            quality = "GOOD"
        elif score >= 70:
            quality = "SATISFACTORY"
        elif score >= 50:
            quality = "NEEDS IMPROVEMENT"
        else:
            quality = "POOR"
        
        f.write(f"\n## Overall Development Quality: {quality}\n\n")
        
        f.write("## Development Strengths\n\n")
        
        # Identify top 3 strengths
        scores = report['scores'].copy()
        del scores['overall_score']  # Remove overall score
        strengths = sorted(scores.items(), key=lambda x: x[1], reverse=True)[:3]
        
        for strength, score in strengths:
            f.write(f"- **{strength.replace('_', ' ').title()}** ({score:.1f}%)\n")
        
        f.write("\n## Areas for Improvement\n\n")
        
        # Identify bottom 3 areas
        weaknesses = sorted(scores.items(), key=lambda x: x[1])[:3]
        
        for weakness, score in weaknesses:
            f.write(f"- **{weakness.replace('_', ' ').title()}** ({score:.1f}%)\n")
    
    logger.info("Development workflow report generated")
    
    # Print summary to console
    print("\n" + "="*50)
    print(" "*10 + "DEVELOPMENT WORKFLOW TEST RESULTS")
    print("="*50)
    print(f"Overall Score: {report['scores']['overall_score']:.1f}%")
    print(f"Development Quality: {quality}")
    print(f"Total Development Time: {report['time_metrics']['total_time']:.2f} seconds")
    print("="*50 + "\n")
    
    return report

def run_development_workflow_test():
    """Run the complete development workflow test."""
    print("\n=== MindLink Agent Development Workflow Test ===\n")
    
    # Initialize metrics
    metrics = DevelopmentMetrics()
    
    # Initialize agent
    agent = initialize_agent()
    if not agent:
        logger.error("[ERROR] Failed to initialize agent")
        return False
    
    # Create a temporary directory for testing
    with tempfile.TemporaryDirectory() as temp_dir:
        logger.info(f"Created test directory: {temp_dir}")
        os.chdir(temp_dir)
        
        try:
            # 1. Requirement Analysis
            design_doc = analyze_requirements(agent, PROJECT_REQUIREMENTS, metrics)
            
            # 2. Project Implementation
            implementation_result, created_files = implement_project(agent, PROJECT_REQUIREMENTS, design_doc, metrics)
            
            # 3. Create Test Data
            test_data_result = create_test_data(agent, metrics)
            
            # 4. Testing
            test_result, issues_found = run_tests(agent, metrics)
            
            # 5. Fix Issues (if needed)
            if issues_found:
                fix_result = fix_issues(agent, test_result, metrics)
            else:
                metrics.debugging_effectiveness = 100  # No issues to fix
                logger.info("No issues found during testing, skipping debugging phase")
            
            # 6. Documentation
            doc_result = create_documentation(agent, metrics)
            
            # 7. Add Feature
            feature_result = add_feature(agent, metrics)
            
            # 8. Code Quality Analysis
            code_quality_result = analyze_code_quality(agent, metrics)
            
            # 9. Calculate Overall Score
            overall_score = calculate_overall_score(metrics)
            
            # 10. Generate Report
            report = generate_report(metrics)
            
            return overall_score >= 70  # Consider success if score is at least 70%
            
        except Exception as e:
            logger.error(f"Error during development workflow test: {e}")
            import traceback
            traceback.print_exc()
            return False

if __name__ == "__main__":
    if run_development_workflow_test():
        sys.exit(0)
    else:
        sys.exit(1) 