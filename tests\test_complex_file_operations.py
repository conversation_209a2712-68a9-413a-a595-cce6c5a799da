import pytest
import os
import tempfile
import shutil
from pathlib import Path

# Import the components we need to test
from mindlink.tools.file_tools import (
    TransactionManager,
    create_file,
    read_file,
    list_files,
    path_exists,
    CreateFileTool,
    ReadFileTool,
    ListFilesTool,
    PathExistsTool
)

@pytest.fixture
def temp_test_dir():
    """Create a temporary directory for testing."""
    temp_dir = tempfile.mkdtemp()
    yield temp_dir
    # Clean up after the test
    shutil.rmtree(temp_dir)

def test_nested_transactions(temp_test_dir):
    """Test nested transactions with multiple file operations."""
    # Create paths for our test files
    file1_path = os.path.join(temp_test_dir, "file1.txt")
    file2_path = os.path.join(temp_test_dir, "file2.txt")
    subdir_path = os.path.join(temp_test_dir, "subdir")
    file3_path = os.path.join(subdir_path, "file3.txt")
    
    # Test successful nested transactions
    with TransactionManager():
        create_file(file1_path, "Content for file 1")
        
        with TransactionManager():
            create_file(file2_path, "Content for file 2")
            
            # Create a subdirectory with a file
            os.makedirs(subdir_path, exist_ok=True)
            with TransactionManager():
                create_file(file3_path, "Content for file 3")
    
    # Verify all files were created
    assert path_exists(file1_path)
    assert path_exists(file2_path)
    assert path_exists(file3_path)
    
    # Verify file contents
    assert read_file(file1_path) == "Content for file 1"
    assert read_file(file2_path) == "Content for file 2"
    assert read_file(file3_path) == "Content for file 3"
    
    # Test transaction rollback
    with pytest.raises(RuntimeError):
        with TransactionManager():
            # Modify existing file
            create_file(file1_path, "Modified content for file 1")
            
            # Create a new file that should be rolled back
            rollback_file = os.path.join(temp_test_dir, "should_rollback.txt")
            create_file(rollback_file, "This file should be rolled back")
            
            # Raise an exception to trigger rollback
            raise RuntimeError("Intentional error to test rollback")
    
    # Verify the original file wasn't modified
    assert read_file(file1_path) == "Content for file 1"
    
    # Verify the new file was rolled back (doesn't exist)
    assert not path_exists(os.path.join(temp_test_dir, "should_rollback.txt"))

def test_tool_classes(temp_test_dir):
    """Test the tool classes that wrap the file operations."""
    # Test CreateFileTool
    create_tool = CreateFileTool()
    file_path = os.path.join(temp_test_dir, "created_by_tool.txt")
    result = create_tool.execute(path=file_path, content="Created by tool")
    
    assert result["status"] == "success"
    assert path_exists(file_path)
    
    # Test ReadFileTool
    read_tool = ReadFileTool()
    result = read_tool.execute(path=file_path)
    
    assert result["status"] == "success"
    assert result["observation"] == "Created by tool"
    
    # Test ListFilesTool
    # Create a few more files for listing
    os.makedirs(os.path.join(temp_test_dir, "list_test"), exist_ok=True)
    create_file(os.path.join(temp_test_dir, "list_test/file1.txt"), "File 1")
    create_file(os.path.join(temp_test_dir, "list_test/file2.txt"), "File 2")
    
    list_tool = ListFilesTool()
    result = list_tool.execute(directory=os.path.join(temp_test_dir, "list_test"))
    
    assert result["status"] == "success"
    assert "file1.txt" in result["observation"]
    assert "file2.txt" in result["observation"]
    
    # Test PathExistsTool
    exists_tool = PathExistsTool()
    result = exists_tool.execute(path=file_path)
    assert result["status"] == "success"
    assert result["observation"] == "True"
    
    # Test non-existent path
    result = exists_tool.execute(path=os.path.join(temp_test_dir, "nonexistent.txt"))
    assert result["status"] == "success"
    assert result["observation"] == "False"

def test_complex_transaction_scenario(temp_test_dir):
    """Test a complex scenario with multiple transactions and operations."""
    # Create a directory structure
    project_dir = os.path.join(temp_test_dir, "project")
    src_dir = os.path.join(project_dir, "src")
    test_dir = os.path.join(project_dir, "tests")
    
    # Use nested transactions to set up the structure
    with TransactionManager():
        # Create the directory structure
        os.makedirs(src_dir, exist_ok=True)
        os.makedirs(test_dir, exist_ok=True)
        
        # Create some source files
        create_file(os.path.join(src_dir, "main.py"), "print('Hello, world!')")
        create_file(os.path.join(src_dir, "utils.py"), "def add(a, b): return a + b")
        
        # Create some test files
        create_file(os.path.join(test_dir, "test_main.py"), "def test_main(): pass")
        create_file(os.path.join(test_dir, "test_utils.py"), "def test_add(): assert add(1, 2) == 3")
        
        # Try a nested transaction that fails
        try:
            with TransactionManager():
                create_file(os.path.join(src_dir, "will_fail.py"), "This should be rolled back")
                raise ValueError("Intentional error")
        except ValueError:
            # This exception should be caught, and the inner transaction rolled back
            pass
    
    # Verify the directory structure
    assert path_exists(src_dir)
    assert path_exists(test_dir)
    
    # Verify the files that should exist
    assert path_exists(os.path.join(src_dir, "main.py"))
    assert path_exists(os.path.join(src_dir, "utils.py"))
    assert path_exists(os.path.join(test_dir, "test_main.py"))
    assert path_exists(os.path.join(test_dir, "test_utils.py"))
    
    # Verify the file that should have been rolled back doesn't exist
    assert not path_exists(os.path.join(src_dir, "will_fail.py"))
    
    # Verify file contents
    assert read_file(os.path.join(src_dir, "main.py")) == "print('Hello, world!')"
    assert read_file(os.path.join(src_dir, "utils.py")) == "def add(a, b): return a + b"
