import os
import sys
import json
import pytest

# Ensure project root is on import path
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from fastapi.testclient import TestClient
# Import app from main inside the test functions to avoid circular imports
# from mindlink.main import app
from mindlink import run_agent
from mindlink.models.openrouter import OpenRouterModel


def test_http_file_ops(tmp_path):
    # Import app from main inside the test function to avoid circular imports
    from mindlink.main import app

    # Setup test directory and initial file
    test_dir = tmp_path / "api_dir"
    test_dir.mkdir()
    file1 = test_dir / "a.txt"
    file1.write_text("123")

    client = TestClient(app)
    # list_files
    resp = client.post(
        "/invoke_tool",
        json={"action": {"tool_name": "list_files", "parameters": {"directory": str(test_dir)}}}
    )
    assert resp.status_code == 200
    data = resp.json()
    assert file1.name in data["observation"]

    # create_file
    file2 = test_dir / "b.txt"
    resp = client.post(
        "/invoke_tool",
        json={"action": {"tool_name": "create_file", "parameters": {"path": str(file2), "content": "hello"}}}
    )
    assert resp.status_code == 200
    assert resp.json()["status"] == "success"

    # read_file
    resp = client.post(
        "/invoke_tool",
        json={"action": {"tool_name": "read_file", "parameters": {"path": str(file2)}}}
    )
    assert resp.status_code == 200
    assert "hello" in resp.json()["observation"]


def test_run_agent_file_flow(tmp_path, monkeypatch):
    # Stub LLM to drive a create_file -> read_file -> finish sequence
    file_path = tmp_path / "flow.txt"
    responses = [
        json.dumps({"action": {"tool_name": "create_file", "parameters": {"path": str(file_path), "content": "ok"}}, "reasoning": ""}),
        json.dumps({"action": {"tool_name": "read_file", "parameters": {"path": str(file_path)}} , "reasoning": ""}),
        json.dumps({"action": {"tool_name": "finish", "parameters": {"result": "done"}}, "reasoning": ""}),
    ]

    def fake_generate(self, system_prompt, user_prompt, history=None):
        return responses.pop(0)

    monkeypatch.setattr(OpenRouterModel, "generate", fake_generate)

    success, result, history = run_agent("dummy goal")
    assert success
    # file was created
    assert file_path.exists()
    # verify tool sequence
    tool_names = [entry["request"].action.tool_name for entry in history]
    assert "create_file" in tool_names
    assert "read_file" in tool_names
