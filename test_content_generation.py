#!/usr/bin/env python3

import sys
import os
sys.path.append('.')

from mindlink.agent import Agent<PERSON>
from mindlink.models.llm import LLMInterface

# Create a mock LLM for testing
class MockLLM(LLMInterface):
    def __init__(self):
        pass
    
    def generate(self, system_prompt, user_prompt, history=None, stream=False, callback=None):
        return "Mock response"

def test_content_generation():
    print("Testing content generation with improved logic...\n")
    
    # Create agent with mock LLM
    mock_llm = MockLLM()
    agent = AgentOS(llm=mock_llm, system_prompt_template="Test template")
    
    # Test different target line counts
    test_cases = [30, 50, 100, 200]
    
    for target_lines in test_cases:
        print(f"\n=== Testing target: {target_lines} lines ===")
        
        # Test Python file
        py_ctx = {
            'file_description_base': 'Test Python module',
            'lines_per_file': target_lines,
            'naming_pattern': 'test.py'
        }
        content = agent._generate_default_file_content(py_ctx, 1)
        actual_lines = len(content.split('\n'))
        accuracy = (actual_lines / target_lines) * 100
        
        print(f"Python file:")
        print(f"  Target: {target_lines} lines")
        print(f"  Actual: {actual_lines} lines")
        print(f"  Accuracy: {accuracy:.1f}%")
        
        # Test JavaScript file
        js_ctx = {
            'file_description_base': 'Test JavaScript module',
            'lines_per_file': target_lines,
            'naming_pattern': 'test.js'
        }
        js_content = agent._generate_default_file_content(js_ctx, 1)
        js_actual_lines = len(js_content.split('\n'))
        js_accuracy = (js_actual_lines / target_lines) * 100
        
        print(f"JavaScript file:")
        print(f"  Target: {target_lines} lines")
        print(f"  Actual: {js_actual_lines} lines")
        print(f"  Accuracy: {js_accuracy:.1f}%")
        
        # Show sample content for first test case
        if target_lines == 30:
            print(f"\nSample Python content (first 15 lines):")
            sample_lines = content.split('\n')[:15]
            for i, line in enumerate(sample_lines, 1):
                print(f"{i:2d}: {line}")
            print("...")

if __name__ == "__main__":
    test_content_generation()