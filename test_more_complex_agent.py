import sys, os
sys.path.append(r'd:/کتابخانه پایتون/2')
from mindlink import run_agent

goal = """
Create a full Python project called 'awesome_project' with:
1. A package directory 'awesome_project' containing '__init__.py'
2. A 'setup.py' file with project metadata (name, version='0.1.0', author)
3. A 'README.md' with project title and basic usage
4. A 'requirements.txt' listing 'requests' and 'pytest'
5. A 'tests' directory with 'test_sample.py' asserting True
6. A '.gitignore' ignoring '__pycache__'
7. List the project root directory
8. Read 'README.md' and 'setup.py' to verify content
9. Confirm successful creation of all files and directories
10. Finish execution
"""

success, result, history = run_agent(goal)
print("Success:", success)
print("Result:", result)
print("History:")
for idx, entry in enumerate(history, start=1):
    action = entry['request'].action.tool_name
    obs = entry['response'].observation.replace('\n', ' ')[:80]
    print(f"{idx}. {action}: {obs}...")
