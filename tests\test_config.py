import unittest
from mindlink.config import load_config, DEFAULT_CONFIG, OPENAI_CONFIG, OPENROUTER_CONFIG

class TestConfig(unittest.TestCase):

    def test_load_config_reflects_max_steps(self):
        """Test that load_config() reflects agent.max_steps = 200."""
        config = load_config()
        self.assertEqual(config['agent']['max_steps'], 200)

    def test_default_llm_configs_max_tokens(self):
        """Test that default LLM configurations have max_tokens = 32000."""
        # Check the direct default config objects
        self.assertEqual(OPENAI_CONFIG['max_tokens'], 32000)
        self.assertEqual(OPENROUTER_CONFIG['max_tokens'], 32000)

        # Check loaded config to ensure it propagates
        config = load_config()
        # Assuming OPENROUTER_CONFIG is the default LLM in DEFAULT_CONFIG
        if config['llm']['provider'] == 'openrouter':
            self.assertEqual(config['llm']['max_tokens'], 32000)
        
        # If we want to be absolutely sure about OpenAI config through load_config,
        # we might need a way to tell load_config to use OpenAI,
        # or inspect DEFAULT_CONFIG more directly if it held multiple LLM configs.
        # For now, we assume that if DEFAULT_CONFIG.llm is OpenAI, it would reflect.
        # Let's check the source DEFAULT_CONFIG which is updated.
        
        # Find OpenAI config if it's part of a list or if we need to simulate it being primary
        # However, DEFAULT_CONFIG directly references one LLM config.
        # The previous changes updated OPENAI_CONFIG and OPENROUTER_CONFIG directly,
        # and DEFAULT_CONFIG uses one of them.
        
        # Let's verify the original DEFAULT_CONFIG dictionary's components that were modified
        self.assertEqual(DEFAULT_CONFIG['llm']['max_tokens'], 32000, 
                         "Default LLM in DEFAULT_CONFIG should have max_tokens set to 32000")

        # To be thorough, if we imagine a scenario where DEFAULT_CONFIG could list multiple LLMs
        # or we switch the default, this test would need adjustment.
        # But based on current structure, checking the one selected as default is key.
        # And we already checked the specific OPENAI_CONFIG and OPENROUTER_CONFIG dicts.

if __name__ == '__main__':
    unittest.main()
