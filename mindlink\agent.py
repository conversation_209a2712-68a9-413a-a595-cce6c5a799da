"""
Main Agent OS implementation for MindLink Agent Core.
"""

import json
import logging
import os
import random # Added for jitter in retry logic
import time
import traceback
import uuid # Added for session_id
import requests # Added for HTTPError handling in retry logic
from typing import Dict, Any, List, Optional, Tuple, Callable
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Initialize logger for this module
logger = logging.getLogger(__name__)

from .models.llm import LLMInterface
from .models.openrouter import OpenRouterModel
from .schemas.mindlink import MindLinkRequest, MindLinkResponse, Action
from .tools.base import Tool, tool_registry
from .utils.json_parser import safe_parse_llm_response, create_fallback_request
from .command_comprehension import CommandComprehensionEngine, ConfidenceLevel
# Added for logging
from .logging_utils import (
    log_event,
    create_user_input_log,
    create_agent_plan_generated_log,
    create_tool_call_start_log,
    create_tool_call_end_log,
    create_llm_query_log,
    create_llm_response_log,
    create_error_occurred_log,
    create_agent_goal_completed_log,
    PlanStep,
    EventType,
    close_logs
)
# FAILOVER_CONFIGS removed - using single OpenRouter model only


class AgentOS:
    """
    The Agent Operating System that manages the agent loop.
    """

    def __init__(self,
                 llm: LLMInterface,
                 system_prompt_template: str,
                 max_steps: int = 15,
                 max_parsing_retries: int = 3,
                 retry_delay: float = 2.0,
                 max_api_retries: int = 5, # New parameter for API call retries
                 enable_failover: bool = True,
                 enable_command_comprehension: bool = True):
        """
        Initialize the Agent OS.

        Args:
            llm: LLM interface implementation
            system_prompt_template: Template for the system prompt
            max_steps: Maximum number of steps to execute
            max_parsing_retries: Maximum number of retries for parsing failures
            retry_delay: Delay between retries in seconds
            enable_failover: Whether to enable automatic failover between providers
            enable_command_comprehension: Whether to enable intelligent command preprocessing
        """
        self.llm = llm
        self.system_prompt_template = system_prompt_template
        self.max_steps = max_steps
        self.max_parsing_retries = max_parsing_retries
        self.retry_delay = retry_delay
        self.max_api_retries = max_api_retries # Initialize new parameter
        self.enable_failover = enable_failover
        self.enable_command_comprehension = enable_command_comprehension
        self.history = []
        self.session_id: str = "" # Will be set at the start of a run
        # ADDED: Context for handling multi-file requests
        self._multi_file_request_context: Optional[Dict[str, Any]] = None
        
        # Initialize failover LLMs
        self.failover_llms = []
        if enable_failover:
            self._initialize_failover_llms()
        
        # Setup a translator using Mistral OpenRouter if available
        if isinstance(self.llm, OpenRouterModel):
            self.translator_llm = OpenRouterModel(
                model_name="mistral-small-3.1",
                api_key=self.llm.api_key,
                temperature=self.llm.params.get("temperature"),
                max_tokens=self.llm.params.get("max_tokens"),
                top_p=self.llm.params.get("top_p")
            )
        else:
            self.translator_llm = None
            
        # Initialize command comprehension engine
        self.command_comprehension_engine = None
        if enable_command_comprehension:
            try:
                # Get API key from the LLM instance or environment
                api_key = None
                if isinstance(self.llm, OpenRouterModel):
                    api_key = self.llm.api_key
                else:
                    api_key = os.getenv('OPENROUTER_API_KEY')
                
                if api_key:
                    self.command_comprehension_engine = CommandComprehensionEngine(api_key)
                    logging.info("Command comprehension engine initialized successfully")
                else:
                    logging.warning("No OpenRouter API key available for command comprehension")
            except Exception as e:
                logging.error(f"Failed to initialize command comprehension engine: {e}")
                
        # Initialize a fresh logging session on startup
        close_logs()
        self.session_id = str(uuid.uuid4())

    def _initialize_failover_llms(self):
        """
        Initialize failover LLM instances from configuration.
        Note: Failover disabled - using single OpenRouter model only.
        """
        # Failover functionality removed - only using single Mistral model
        pass

    def clear_history(self):
        """
        Clear the agent's history to avoid state leakage between runs.
        """
        self.history = []

    def _generate_with_failover(self, system_prompt: str, user_prompt: str, history: List[Dict[str, str]] = None) -> str:
        """
        Generate response with failover mechanism across multiple LLM providers.
        Uses exponential backoff for retry delays.
        
        Args:
            system_prompt: System prompt for the LLM
            user_prompt: User prompt for the LLM
            history: Conversation history
            
        Returns:
            Generated response string
            
        Raises:
            Exception: If all LLM providers fail
        """
        llms_to_try = [self.llm] + self.failover_llms if self.enable_failover else [self.llm]
        
        for i, llm in enumerate(llms_to_try):
            try:
                logging.info(f"Attempting generation with {llm.__class__.__name__} (attempt {i+1}/{len(llms_to_try)})")
                response = llm.generate(system_prompt, user_prompt, history)
                
                # Check for empty response
                if not response or response.strip() == "":
                    raise ValueError("Empty response from LLM")
                    
                logging.info(f"Successfully generated response with {llm.__class__.__name__}")
                return response
                
            except Exception as e:
                logging.warning(f"LLM {llm.__class__.__name__} failed: {e}")
                if i == len(llms_to_try) - 1:  # Last attempt
                    raise Exception(f"All LLM providers failed. Last error: {e}")
                else:
                    # Exponential backoff: base_delay * (2 ^ attempt_number)
                    backoff_delay = self.retry_delay * (2 ** i)
                    logging.info(f"Trying next LLM provider in {backoff_delay:.1f}s...")
                    time.sleep(backoff_delay)
        
        raise Exception("No LLM providers available")

    def _get_tool_descriptions(self, for_multi_file_step: bool = False) -> str:
        """
        Get formatted descriptions of all available tools.

        Returns:
            Formatted tool descriptions
        """
        descriptions = []

        for name, tool_cls in tool_registry.items():
            if for_multi_file_step and name != 'create_file':
                continue # Only show create_file tool for this specific step
            schema = tool_cls.get_schema()

            # Format the parameters
            params_schema = schema["parameters"].get("properties", {})
            params_desc = []

            for param_name, param_info in params_schema.items():
                required = param_name in schema["parameters"].get("required", [])
                req_str = " (required)" if required else " (optional)"
                desc = param_info.get("description", "")
                params_desc.append(f"  - {param_name}: {desc}{req_str}")

            # Format the tool description
            tool_desc = f"Tool: {name}\n"
            tool_desc += f"Description: {schema['description']}\n"

            if params_desc:
                tool_desc += "Parameters:\n"
                tool_desc += "\n".join(params_desc)

            descriptions.append(tool_desc)

        return "\n\n".join(descriptions)

    def _build_system_prompt(self, for_multi_file_step: bool = False) -> str:
        """
        Build the system prompt from the template.

        Returns:
            The formatted system prompt
        """
        # Get tool descriptions, possibly filtered for multi-file step
        tools_section_str = self._get_tool_descriptions(for_multi_file_step=for_multi_file_step)

        # Use simple replace to avoid format string issues with JSON braces
        base_prompt = self.system_prompt_template.replace("{tool_descriptions}", tools_section_str)

        # This line is replaced by the logic above using _get_tool_descriptions
        # base_prompt = self.system_prompt_template.replace("{tool_descriptions}", "\n".join(tools_desc))
        json_instruction = (
            "\n\nWhen responding, output only a valid JSON object with this schema:\n"
            "{\"action\": {\"tool_name\": <string>, \"parameters\": <object>}, \"reasoning\": <string>, \"thought\": <string>}\n"
            "Do not include any additional text or code fences."
        )
        return base_prompt + json_instruction

    def _build_user_prompt(self, goal: str, observations: List[str], for_multi_file_step: bool = False) -> str:
        """
        Build the user prompt including the full action history and previous observations.
        """
        prev_obs = "\n".join(observations)
        actions = [entry['request'].action.tool_name for entry in self.history]
        actions_desc = "\n".join(f"{i+1}. {a}" for i, a in enumerate(actions)) if actions else "None"

        if for_multi_file_step:
            # Extract line count from goal if present
            lines_requirement = ""
            import re
            lines_match = re.search(r'(\d+)\s*lines?', goal)
            if lines_match:
                target_lines = int(lines_match.group(1))
                lines_requirement = f"\nIMPORTANT: The file MUST contain approximately {target_lines} lines of functional code. Generate substantial, meaningful content to reach this line count. Do not create minimal placeholder code.\n"
            
            # Simplified prompt for focused multi-file creation step
            prompt = (
                f"You are in a multi-file creation process. Your current sub-goal is: {goal}\n"
                f"CRITICAL: You MUST ONLY use the 'create_file' tool. Do NOT use 'generate_large_file' or any other tool.\n"
                f"Required parameters: 'path' (filename) and 'content' (full file content).\n"
                f"{lines_requirement}"
                f"MANDATORY: Generate complete, functional, substantial code that meets the exact line count requirement.\n"
                f"The file MUST be self-contained with proper classes, multiple methods, comprehensive error handling, logging, and meaningful business logic.\n"
                f"Include extensive imports, detailed documentation, multiple utility functions, and substantial implementation code.\n"
                f"DO NOT create minimal placeholder code - generate production-ready, feature-rich content.\n"
                f"Previous observations during this multi-file task: {'None' if not observations else ''.join(observations)}"
            )
        else:
            # Standard prompt construction
            prompt = (
            f"Goal: {goal}\n\n"
            f"Actions taken so far:\n{actions_desc}\n\n"
            f"Previous observations:\n{prev_obs}"
        )
        log_event(create_llm_query_log(
            self.session_id,
            self.llm.model_name if hasattr(self.llm, 'model_name') else 'unknown_model',
            prompt
        ))
        return prompt

    def _execute_tool(self, request: MindLinkRequest) -> MindLinkResponse:
        """
        Execute the requested tool.

        Args:
            request: The MindLinkRequest containing the action to execute

        Returns:
            MindLinkResponse with the result of the action
        """
        tool_name = request.action.tool_name
        parameters = request.action.parameters or {}

        # Log Tool Call Start
        monotonic_start_time = time.monotonic() # For duration calculation
        tool_start_event = create_tool_call_start_log(self.session_id, tool_name, parameters)
        log_event(tool_start_event)
        actual_start_time = tool_start_event.tool_call.start_time

        # Check if tool exists
        if tool_name not in tool_registry:
            duration_ms = (time.monotonic() - monotonic_start_time) * 1000
            error_details_for_tool_meta = {"error_message": f"Tool '{tool_name}' not found", "reason": "registry_lookup_failed"}
            log_event(create_tool_call_end_log(
                session_id=self.session_id,
                tool_name=tool_name,
                parameters=parameters,
                start_time=actual_start_time,
                status="FAILURE",
                execution_time_ms=duration_ms,
                tool_metadata=error_details_for_tool_meta
            ))
            log_event(create_error_occurred_log(self.session_id, "ToolExecution", f"Tool '{tool_name}' not found", "ERROR"))
            return MindLinkResponse(
                observation=f"Error: Tool '{tool_name}' not found. Available tools: {', '.join(tool_registry.keys())}",
                status="error",
                error=f"Tool '{tool_name}' not found"
            )

        # Instantiate the tool
        tool_cls = tool_registry[tool_name]
        tool = tool_cls()

        # Special handling for create_file tool which is often problematic
        if tool_name == "create_file" and (
            parameters.get("path") is None or
            not isinstance(parameters.get("path"), str) or
            not parameters.get("path").strip()
        ):
            # Try to extract path from the reasoning or action name
            if hasattr(request, "reasoning") and request.reasoning:
                import re
                path_match = re.search(r"['\"]([^'\"\\]+\.(py|js|txt|md|html|css|json))['\"]", request.reasoning)
                if path_match:
                    parameters["path"] = path_match.group(1)

            # If still no path, use a default
            if parameters.get("path") is None or not isinstance(parameters.get("path"), str) or not parameters.get("path").strip():
                parameters["path"] = "output.txt"
                warning_msg = f"WARNING: Using default filename 'output.txt' for create_file as no valid path was provided. Reasoning: {request.reasoning if hasattr(request, 'reasoning') else 'N/A'}"
                print(warning_msg)
                # Log this specific warning as an INFO level error log for traceability
                log_event(create_error_occurred_log(self.session_id, "ToolExecution", warning_msg, "INFO"))

        # Apply parameter normalization before validation
        if tool_name in ['create_file', 'write_file', 'read_file', 'delete_file', 'generate_large_file']:
            if 'path' in parameters and parameters['path']:
                # Normalize path to absolute path under D:/3/
                import os
                original_path = parameters['path']
                if not os.path.isabs(original_path):
                    parameters['path'] = f"D:/3/{original_path}"
                elif not original_path.startswith("D:/3/"):
                    # If it's absolute but not under D:/3/, make it relative to D:/3/
                    basename = os.path.basename(original_path)
                    parameters['path'] = f"D:/3/{basename}"
            elif tool_name == 'generate_large_file':
                # For generate_large_file, ensure path parameter exists
                if 'path' not in parameters or not parameters['path']:
                    # Generate a default path based on content description
                    content_desc = parameters.get('content_description', 'generated_file')
                    # Extract a reasonable filename from content description
                    import re
                    filename_match = re.search(r'\b(\w+)\.(py|js|txt|md|html|css|json)\b', content_desc)
                    if filename_match:
                        filename = filename_match.group(0)
                    else:
                        # Generate filename based on content type
                        if 'python' in content_desc.lower() or 'py' in content_desc.lower():
                            filename = 'generated_code.py'
                        elif 'javascript' in content_desc.lower() or 'js' in content_desc.lower():
                            filename = 'generated_code.js'
                        elif 'html' in content_desc.lower():
                            filename = 'generated_page.html'
                        else:
                            filename = 'generated_file.txt'
                    parameters['path'] = f"D:/3/{filename}"

        try:
            # Validate parameters for all tools using Pydantic model validation
            if hasattr(tool, "parameters_model"):
                # Introspect required parameters from the Pydantic model
                param_model_cls = tool.parameters_model
                try:
                    # Pydantic v2 fields
                    fields_info = getattr(param_model_cls, "model_fields", None)
                    if fields_info is None:
                        # Fallback to Pydantic v1 __fields__
                        fields_info = getattr(param_model_cls, "__fields__", {})
                    required_fields = [name for name, info in fields_info.items() if getattr(info, "required", False)]
                except Exception:
                    required_fields = []

                missing_params = [f for f in required_fields if f not in parameters or parameters[f] is None]
                if missing_params:
                    duration_ms = (time.monotonic() - monotonic_start_time) * 1000
                    error_msg = f"Missing required parameters for tool '{tool_name}': {', '.join(missing_params)}"
                    error_details_for_tool_meta = {"error_message": error_msg, "reason": "missing_required_parameters"}
                    log_event(create_tool_call_end_log(
                        session_id=self.session_id,
                        tool_name=tool_name,
                        parameters=parameters,
                        start_time=actual_start_time,
                        status="FAILURE",
                        execution_time_ms=duration_ms,
                        tool_metadata=error_details_for_tool_meta
                    ))
                    log_event(create_error_occurred_log(self.session_id, "ToolValidation", error_msg, "ERROR"))
                    return MindLinkResponse(
                        observation=f"Error: Missing required parameters for tool '{tool_name}': {', '.join(missing_params)}'",
                        status="error",
                        error=f"Missing required parameters: {', '.join(missing_params)}"
                    )

                from pydantic import ValidationError
                try:
                    validated_params = param_model_cls(**parameters)
                    # Dump validated params for execution
                    if hasattr(validated_params, "model_dump"):
                        parameters = validated_params.model_dump()
                    else:
                        parameters = validated_params.dict()
                except ValidationError as e:
                    errors_list = e.errors()
                    missing_fields = [err["loc"][0] for err in errors_list if err.get("type", "").endswith("missing")]
                    if missing_fields:
                        duration_ms = (time.monotonic() - monotonic_start_time) * 1000
                        error_msg = f"Missing required parameters for tool '{tool_name}': {', '.join(missing_fields)}"
                        error_details_for_tool_meta = {"error_message": error_msg, "reason": "pydantic_validation_missing_fields"}
                        log_event(create_tool_call_end_log(
                            session_id=self.session_id,
                            tool_name=tool_name,
                            parameters=parameters,
                            start_time=actual_start_time,
                            status="FAILURE",
                            execution_time_ms=duration_ms,
                            tool_metadata=error_details_for_tool_meta
                        ))
                        log_event(create_error_occurred_log(self.session_id, "ToolValidation", error_msg, "ERROR"))
                        return MindLinkResponse(
                            observation=f"Error: Missing required parameters for tool '{tool_name}': {', '.join(missing_fields)}'",
                            status="error",
                            error=f"Missing required parameters: {', '.join(missing_fields)}"
                        )
                    duration_ms = (time.monotonic() - monotonic_start_time) * 1000
                    error_msg = f"Error validating parameters for tool '{tool_name}': {str(e)}"
                    error_details_for_tool_meta = {"error_message": str(e), "reason": "pydantic_validation_error"}
                    log_event(create_tool_call_end_log(
                        session_id=self.session_id,
                        tool_name=tool_name,
                        parameters=parameters,
                        start_time=actual_start_time,
                        status="FAILURE",
                        execution_time_ms=duration_ms,
                        tool_metadata=error_details_for_tool_meta
                    ))
                    log_event(create_error_occurred_log(self.session_id, "ToolValidation", error_msg, "ERROR", stack_trace=traceback.format_exc()))
                    return MindLinkResponse(
                        observation=f"Error validating parameters for tool '{tool_name}': {str(e)}",
                        status="error",
                        error=str(e)
                    )

            # Note: The create_tool_call_start_log was moved to the beginning of this function.
            # No need to call it again here.

            # Execute the tool
            result = tool.execute(**parameters)

            # Convert to MindLinkResponse
            response = MindLinkResponse(
                observation=result["observation"],
                status=result.get("status", "success"), # Assuming "success" or "error" from tool
                error=result.get("error")
            )
            duration_ms = (time.monotonic() - monotonic_start_time) * 1000
            # Map tool's "success"/"error" to "SUCCESS"/"FAILURE" for logging
            log_status = "SUCCESS" if response.status.lower() == "success" else "FAILURE"
            tool_call_metadata = {"error_message": response.error} if response.error else None
            if response.error and log_status == "SUCCESS": # Should not happen if status mapping is correct
                log_status = "FAILURE" # Ensure consistency

            log_event(create_tool_call_end_log(
                session_id=self.session_id,
                tool_name=tool_name,
                parameters=parameters,
                start_time=actual_start_time,
                status=log_status,
                result={"observation": response.observation}, # Pass observation as part of result
                error=response.error,
                execution_time_ms=duration_ms,
                tool_metadata=tool_call_metadata
            ))
            return response

        except Exception as e:
            tb_str = traceback.format_exc()
            print(f"Error executing tool {tool_name}: {e}\n{tb_str}")
            duration_ms = (time.monotonic() - monotonic_start_time) * 1000
            error_details_for_tool_meta = {"error_message": str(e), "stack_trace": tb_str, "reason": "tool_execution_exception"}
            log_event(create_tool_call_end_log(
                session_id=self.session_id,
                tool_name=tool_name,
                parameters=parameters,
                start_time=actual_start_time,
                status="FAILURE",
                execution_time_ms=duration_ms,
                tool_metadata=error_details_for_tool_meta,
                error=str(e) # Pass the error string to the error field as well
            ))
            log_event(create_error_occurred_log(self.session_id, "ToolExecution", str(e), "CRITICAL", stack_trace=tb_str))
            return MindLinkResponse(
                observation=f"Error executing tool {tool_name}: {e}",
                status="error",
                error=str(e)
            )

    def _get_llm_response(self, system_prompt: str, user_prompt: str) -> Tuple[Optional[MindLinkRequest], str]:
        raw_response = ""
        parsing_retries = 0
        llm_query_start_time = time.monotonic()
        # Note: LLM Query log is now created in _build_user_prompt before this method is called.
        # However, we log LLM_RESPONSE here.

        while parsing_retries <= self.max_parsing_retries:
            try:
                # Progress tracking for long operations
                if parsing_retries > 0:
                    print(f"🔄 Retrying LLM parsing... (attempt {parsing_retries + 1}/{self.max_parsing_retries + 1})")
                
                api_retries = 0
                max_api_retries = self.max_api_retries # Assuming self.max_api_retries is defined, e.g., 5
                base_retry_delay = self.retry_delay # Assuming self.retry_delay is defined, e.g., 1 second

                while api_retries <= max_api_retries:
                    try:
                        raw_response = self._generate_with_failover(system_prompt, user_prompt, self.history)
                        llm_latency_ms = (time.monotonic() - llm_query_start_time) * 1000
                        tokens_used = getattr(self.llm, 'last_tokens_used', None)
                        log_event(create_llm_response_log(
                            self.session_id,
                            self.llm.model_name if hasattr(self.llm, 'model_name') else 'unknown_model',
                            raw_response,
                            prompt=user_prompt,
                            total_tokens=tokens_used,
                            latency_ms=llm_latency_ms
                        ))
                        break # Success, exit retry loop
                    except requests.exceptions.HTTPError as http_err:
                        api_retries += 1
                        status_code = http_err.response.status_code
                        error_message = f"LLM API request failed with status {status_code} (attempt {api_retries}/{max_api_retries + 1}): {http_err}."
                        print(error_message)
                        log_event(create_error_occurred_log(self.session_id, "LLMInterface", f"HTTPError {status_code}: {http_err}", "ERROR", stack_trace=traceback.format_exc()))
                        
                        if status_code in [429, 500, 502, 503, 504] and api_retries <= max_api_retries:
                            backoff_delay = base_retry_delay * (2 ** (api_retries - 1))
                            jitter = random.uniform(0, backoff_delay * 0.1) # Add some jitter
                            actual_delay = backoff_delay + jitter
                            print(f"⏳ API rate limit or server error. Waiting {actual_delay:.1f}s before API retry...")
                            time.sleep(actual_delay)
                        else:
                            # Non-retryable HTTP error or max retries exceeded
                            log_event(create_llm_response_log(
                                self.session_id,
                                self.llm.model_name if hasattr(self.llm, 'model_name') else 'unknown_model',
                                "", # No raw response
                                prompt=user_prompt,
                                metadata={"error": f"Final API attempt failed: {http_err}"}
                            ))
                            fallback_request = create_fallback_request(f"Failed to get LLM response due to API error: {http_err}")
                            return fallback_request, "" # Return fallback
                    except Exception as e: # Catch other potential errors during _generate_with_failover
                        api_retries += 1 # Count as an attempt
                        error_message = f"Error during LLM generation (attempt {api_retries}/{max_api_retries + 1}): {e}."
                        print(error_message)
                        log_event(create_error_occurred_log(self.session_id, "LLMInterface", f"Generation Error: {e}", "CRITICAL", stack_trace=traceback.format_exc()))
                        if api_retries > max_api_retries:
                            log_event(create_llm_response_log(
                                self.session_id,
                                self.llm.model_name if hasattr(self.llm, 'model_name') else 'unknown_model',
                                "", # No raw response
                                prompt=user_prompt,
                                metadata={"error": f"Final generation attempt failed: {e}"}
                            ))
                            fallback_request = create_fallback_request(f"Failed to get LLM response after multiple attempts: {e}")
                            return fallback_request, "" # Return fallback
                        # Exponential backoff for other errors as well
                        backoff_delay = base_retry_delay * (2 ** (api_retries - 1))
                        jitter = random.uniform(0, backoff_delay * 0.1)
                        actual_delay = backoff_delay + jitter
                        print(f"⏳ Unexpected error during generation. Waiting {actual_delay:.1f}s before retry...")
                        time.sleep(actual_delay)

                # If loop finishes due to max_api_retries, it means all retries failed.
                if api_retries > max_api_retries:
                    final_error_msg = "Exhausted all API retries for LLM generation."
                    print(f"❌ {final_error_msg}")
                    log_event(create_llm_response_log(
                        self.session_id,
                        self.llm.model_name if hasattr(self.llm, 'model_name') else 'unknown_model',
                        "", # No raw response
                        prompt=user_prompt,
                        metadata={"error": final_error_msg}
                    ))
                    fallback_request = create_fallback_request(final_error_msg)
                    return fallback_request, ""

                parsed_request, _ = safe_parse_llm_response(raw_response)
                if parsing_retries > 0:
                    print(f"✅ Parsing successful after {parsing_retries + 1} attempts")
                return parsed_request, raw_response
            except json.JSONDecodeError as e:
                parsing_retries += 1
                error_message = f"LLM response parsing failed (attempt {parsing_retries}/{self.max_parsing_retries + 1}): {e}. Raw response: {raw_response[:500]}"
                print(error_message)
                log_event(create_error_occurred_log(self.session_id, "LLMInterface", f"JSONDecodeError: {e}", "WARNING", stack_trace=traceback.format_exc()))
                if parsing_retries > self.max_parsing_retries:
                    # Log final parsing failure as an error
                    log_event(create_llm_response_log(
                        self.session_id,
                        self.llm.model_name if hasattr(self.llm, 'model_name') else 'unknown_model',
                        raw_response,
                        prompt=user_prompt,
                        metadata={"error": f"Final parsing attempt failed: {e}"}
                    ))
                    print(f"❌ All parsing attempts failed after {self.max_parsing_retries + 1} tries")
                    fallback_request = create_fallback_request(f"Failed to parse LLM response after multiple retries: {e}")
                    return fallback_request, raw_response # Return fallback
                
                # Exponential backoff for parsing retries
                backoff_delay = self.retry_delay * (2 ** (parsing_retries - 1))
                print(f"⏳ Waiting {backoff_delay:.1f}s before retry...")
                time.sleep(backoff_delay)
            except Exception as e: # Catch other unexpected errors during LLM interaction or parsing
                llm_latency_ms = (time.monotonic() - llm_query_start_time) * 1000
                tb_str = traceback.format_exc()
                error_message = f"Unexpected error during LLM interaction or response parsing: {e}. Raw response: {raw_response[:500]}"
                print(error_message + f"\n{tb_str}")
                log_event(create_llm_response_log(
                    self.session_id,
                    self.llm.model_name if hasattr(self.llm, 'model_name') else 'unknown_model',
                    raw_response,
                    prompt=user_prompt,
                    latency_ms=llm_latency_ms,
                    metadata={"error": str(e)}
                ))
                log_event(create_error_occurred_log(self.session_id, "LLMInterface", str(e), "ERROR", stack_trace=tb_str))
                fallback_request = create_fallback_request(f"Unexpected error processing LLM response: {e}")
                return fallback_request, raw_response # Return fallback

        # Should not be reached if loop logic is correct, but as a safeguard:
        final_error_msg = "Exceeded max parsing retries for LLM response."
        log_event(create_error_occurred_log(self.session_id, "LLMInterface", final_error_msg, "ERROR"))
        return create_fallback_request(final_error_msg), raw_response

    def _get_tools_prompt(self) -> str:
        """Generate a prompt section describing all available tools and their schemas."""
        from mindlink.tools.base import tool_registry
        
        tools_info = []
        for tool_name, tool_cls in tool_registry.items():
            try:
                tool = tool_cls()
                schema = tool.get_schema()
                tools_info.append({
                    'name': tool_name,
                    'description': tool.description,
                    'parameters': schema.get('parameters', {})
                })
            except Exception as e:
                print(f"Error getting schema for tool {tool_name}: {e}")
                continue
                
        tools_prompt = "Available tools and their schemas (use these exact parameter names):\n"
        for tool in tools_info:
            params_desc = []
            for param_name, param_schema in tool['parameters'].get('properties', {}).items():
                param_type = param_schema.get('type', 'any')
                param_desc = param_schema.get('description', 'No description')
                params_desc.append(f"    • {param_name} ({param_type}): {param_desc}")
                
            tools_prompt += (
                f"\nTool: {tool['name']}\n"
                f"Description: {tool['description']}\n"
                f"Parameters:\n" + "\n".join(params_desc) + "\n"
            )
            
        return tools_prompt

    def _translate_nl_to_tool_call(self, goal: str) -> Optional[MindLinkRequest]:
        """
        Translate a natural language goal into a structured MindLinkRequest tool call.
        
        This method uses the connected language model to understand the user's intent
        and convert it into a structured tool call with appropriate parameters.
        """
        translator = getattr(self, "translator_llm", None)
        if not translator:
            return None
            
        # Get information about all available tools
        tools_prompt = self._get_tools_prompt()
        
        system_prompt = (
            "You are an AI assistant that translates natural language requests into structured tool calls.\n\n"
            f"{tools_prompt}\n"
            "Your task is to understand the user's request and select the most appropriate tool to accomplish it.\n"
            "For file operations, use these path conventions:\n"
            "- Paths should be absolute (e.g., 'D:/3/example.txt')\n"
            "- For new files, place them under 'D:/3/'\n\n"
            "Respond with a JSON object in this exact format:\n"
            "{\"action\": {\"tool_name\": \"tool_name\", \"parameters\": {\"param1\": value1, ...}}, \"reasoning\": \"...\"}\n"
            "IMPORTANT: Only include the JSON object in your response, nothing else.\n\n"
            "SPECIAL INSTRUCTIONS FOR FILE TOOLS:\n"
            "- For 'create_file' tool: The 'content' parameter MUST be the full, literal string content of the file.\n"
            "- For 'generate_large_file' tool: ALWAYS provide both 'path' (absolute file path) and 'content_description' parameters.\n"
            "- DO NOT use Python expressions, string concatenation, or other shortcuts for 'content', even for very long strings.\n"
            "- Ensure all special characters within the 'content' string (like newlines \\n, quotes \", backslashes \\\\) are properly escaped for JSON format.\n"
            "- When creating multiple files, generate unique file names with appropriate extensions." 

        )
        
        try:
            # Get the raw response from the LLM
            raw = translator.generate(system_prompt, goal, history=[])
            
            # Parse the response into a MindLinkRequest
            parsed_req, _ = safe_parse_llm_response(raw)
            
            if not parsed_req or not parsed_req.action.tool_name:
                print(f"Failed to parse tool call from response: {raw}")
                return None
                
            # Validate the tool exists
            tool_name = parsed_req.action.tool_name
            from mindlink.tools.base import tool_registry
            if tool_name not in tool_registry:
                print(f"Unknown tool: {tool_name}")
                return None
                
            # Get the tool's parameter model for validation
            tool_cls = tool_registry[tool_name]
            tool = tool_cls()
            
            # Normalize parameters
            params = parsed_req.action.parameters or {}
            
            # Handle file path normalization for file operations
            if tool_name in ('create_file', 'write_file', 'edit_file', 'generate_large_file'):
                path = params.get('path')
                if path and not path.startswith('D:/'):
                    params['path'] = f"D:/3/{path.lstrip('/')}"
                elif tool_name == 'generate_large_file' and (not path or not path.strip()):
                    # Generate a default path based on content description
                    content_desc = params.get('content_description', 'generated_file')
                    # Extract a reasonable filename from content description
                    import re
                    filename_match = re.search(r'\b(\w+)\.(py|js|txt|md|html|css|json)\b', content_desc)
                    if filename_match:
                        filename = filename_match.group(0)
                    else:
                        # Generate filename based on content type
                        if 'python' in content_desc.lower() or 'py' in content_desc.lower():
                            filename = 'generated_code.py'
                        elif 'javascript' in content_desc.lower() or 'js' in content_desc.lower():
                            filename = 'generated_code.js'
                        elif 'html' in content_desc.lower():
                            filename = 'generated_page.html'
                        else:
                            filename = 'generated_file.txt'
                    params['path'] = f"D:/3/{filename}"
            
            # Update the request with normalized parameters
            parsed_req.action.parameters = params
            
            return parsed_req
            
        except Exception as e:
            print(f"Error in _translate_nl_to_tool_call: {str(e)}")
            import traceback
            traceback.print_exc()
            return None

    def plan_once(self, goal: str) -> List[Action]:
        """
        Generate a plan for the given goal without executing tools.
        Returns a list of Actions.
        """
        # Attempt direct NL→tool translation
        translation_req = self._translate_nl_to_tool_call(goal)
        if translation_req and translation_req.action.tool_name:
            # Handle JSON‐parse fallback: switch to direct chat
            if translation_req.action.tool_name == "echo":
                # Attempt to reparse an embedded tool call from the echo message
                raw_msg = translation_req.action.parameters.get("message", "")
                from mindlink.utils.json_parser import safe_parse_llm_response
                parsed_req, _ = safe_parse_llm_response(raw_msg)
                if parsed_req and parsed_req.action.tool_name and parsed_req.action.tool_name != "echo":
                    # Apply parameter normalization to the parsed action
                    action = parsed_req.action
                    if action.tool_name in ['create_file', 'write_file', 'read_file', 'delete_file', 'generate_large_file']:
                        if 'path' in action.parameters and action.parameters['path']:
                            # Normalize path to absolute path under D:/3/
                            import os
                            original_path = action.parameters['path']
                            if not os.path.isabs(original_path):
                                action.parameters['path'] = f"d:/کتابخانه پایتون/2-/{original_path}"
                            elif not original_path.startswith("D:/3/"):
                                # If it's absolute but not under D:/3/, make it relative to D:/3/
                                basename = os.path.basename(original_path)
                                action.parameters['path'] = f"d:/کتابخانه پایتون/2-/{basename}"
                        elif action.tool_name == 'generate_large_file':
                            # For generate_large_file, ensure path parameter exists
                            if 'path' not in action.parameters or not action.parameters['path']:
                                # Generate a default path based on content description
                                content_desc = action.parameters.get('content_description', 'generated_file')
                                # Extract a reasonable filename from content description
                                import re
                                filename_match = re.search(r'\b(\w+)\.(py|js|txt|md|html|css|json)\b', content_desc)
                                if filename_match:
                                    filename = filename_match.group(0)
                                else:
                                    # Generate filename based on content type
                                    if 'python' in content_desc.lower() or 'py' in content_desc.lower():
                                        filename = 'generated_code.py'
                                    elif 'javascript' in content_desc.lower() or 'js' in content_desc.lower():
                                        filename = 'generated_code.js'
                                    elif 'html' in content_desc.lower():
                                        filename = 'generated_page.html'
                                    else:
                                        filename = 'generated_file.txt'
                                action.parameters['path'] = f"d:/کتابخانه پایتون/2-/{filename}"
                    return [action]
                # Fallback: use chat for free-form responses
                chat_resp = self._generate_with_failover(
                    "You are a helpful assistant. Provide a natural language response.",
                    goal,
                    self.history
                )
                return [
                    Action(tool_name="echo", parameters={"message": chat_resp})
                ]
            # Check if the goal mentions multiple files - if so, use iterative planning
            import re
            # Improved regex patterns to catch more multi-file requests
            multi_file_patterns = [
                r'create\s+(\d+|one|two|three|four|five|six|seven|eight|nine|ten)\s+(.+?)\s*files?(?:\s+each\s+with\s+(\d+)\s+lines?)?(?:\s+named\s+(.+))?',
                r'(\d+|one|two|three|four|five|six|seven|eight|nine|ten)\s+(.+?)\s*files?(?:\s+each\s+with\s+(\d+)\s+lines?)?(?:\s+named\s+(.+))?',
                r'create\s+(.+?)\s*files?\s*-\s*(\d+|one|two|three|four|five|six|seven|eight|nine|ten)(?:\s+each\s+with\s+(\d+)\s+lines?)?(?:\s+named\s+(.+))?'
            ]
            
            multi_file_match = None
            for pattern in multi_file_patterns:
                multi_file_match = re.search(pattern, goal, re.IGNORECASE)
                if multi_file_match:
                    break

            # Also check for explicit multi-file indicators
            if not multi_file_match:
                multi_indicators = [r'multiple\s+files?', r'several\s+files?', r'many\s+files?', r'\d+\s*x\s*files?']
                for indicator in multi_indicators:
                    if re.search(indicator, goal, re.IGNORECASE):
                        # Default to 5 files if no specific number found
                        num_match = re.search(r'(\d+)', goal)
                        num_files = int(num_match.group(1)) if num_match else 5
                        self._multi_file_request_context = {
                            'num_files_remaining': num_files,
                            'file_description_base': 'python file',
                            'lines_per_file': None,
                            'naming_pattern': None,
                            'files_created_count': 0,
                            'original_goal': goal
                        }
                        break

            if multi_file_match:
                groups = multi_file_match.groups()
                # Handle different pattern matches
                if len(groups) >= 2:
                    num_files_str = groups[0].lower() if groups[0] else '1'
                    file_description_base = groups[1].strip() if groups[1] else 'python file'
                    lines_str = groups[2] if len(groups) > 2 and groups[2] else None
                    naming_pattern = groups[3] if len(groups) > 3 and groups[3] else None

                    num_map = {'one': 1, 'two': 2, 'three': 3, 'four': 4, 'five': 5, 'six': 6, 'seven': 7, 'eight': 8, 'nine': 9, 'ten': 10}
                    try:
                        num_files = int(num_files_str) if num_files_str.isdigit() else num_map.get(num_files_str, 1)
                    except ValueError:
                        num_files = 1
                    
                    # Validate and create multi-file context with enhanced error handling
                    if num_files >= 2 and num_files <= 20:  # Reasonable limits
                        # Validate file description
                        if not file_description_base or len(file_description_base.strip()) < 3:
                            file_description_base = 'generated file'
                            logger.warning(f"Using default file description for multi-file request")
                        
                        # Validate lines per file
                        validated_lines = None
                        if lines_str and lines_str.isdigit():
                            lines_count = int(lines_str)
                            if 5 <= lines_count <= 1000:  # Reasonable range
                                validated_lines = lines_count
                            else:
                                logger.warning(f"Lines per file {lines_count} outside reasonable range, ignoring")
                        
                        # Validate naming pattern
                        validated_pattern = naming_pattern
                        if naming_pattern and '{i}' not in naming_pattern:
                            logger.warning(f"Naming pattern '{naming_pattern}' missing {{i}} placeholder, will use default")
                            validated_pattern = None
                        
                        self._multi_file_request_context = {
                            'num_files_remaining': num_files,
                            'file_description_base': file_description_base.strip(),
                            'lines_per_file': validated_lines,
                            'naming_pattern': validated_pattern,
                            'files_created_count': 0,
                            'original_goal': goal,
                            'creation_timestamp': time.time()  # For debugging
                        }
                        # Log multi-file context creation for debugging
                        logger.info(f"Multi-file context created: {num_files} files, description: '{file_description_base}', lines: {validated_lines}")
                    elif num_files > 20:
                        logger.warning(f"Requested {num_files} files exceeds maximum limit of 20, falling back to standard planning")
                    else:
                        logger.info(f"Single file request ({num_files}), using standard planning")
            
            # If we have multi-file context, skip direct translation to force iterative planning
            if not self._multi_file_request_context and translation_req and translation_req.action.tool_name:
                 return [translation_req.action]
            # If not a multi-file request and no direct translation, fall through to general iterative planning
        # Iterative planning loop
        self.clear_history()
        observations: List[str] = []
        system_prompt = self._build_system_prompt()
        plan: List[Action] = []
        step_count = 0
        
        # Analyze goal complexity to determine appropriate step limit
        goal_complexity = self._analyze_goal_complexity(goal)
        dynamic_max_steps = min(self.max_steps, goal_complexity)
        
        while step_count < dynamic_max_steps:
            if self._multi_file_request_context and self._multi_file_request_context['num_files_remaining'] > 0:
                # Construct a specific goal for the next file
                ctx = self._multi_file_request_context
                file_num = ctx['files_created_count'] + 1
                total_files = ctx['num_files_remaining'] + ctx['files_created_count']
                
                # Log multi-file step
                logger.info(f"Multi-file planning step: Creating file {file_num} of {total_files}")
                
                current_file_goal = f"Create file {file_num} of {total_files}. "
                current_file_goal += f"Description: {ctx['file_description_base']}. "
                if ctx['lines_per_file']:
                    current_file_goal += f"CRITICAL REQUIREMENT: The file MUST contain exactly {ctx['lines_per_file']} lines of functional, meaningful code. Do not create minimal placeholder content. Generate substantial classes, methods, and logic to reach this exact line count. "
                
                # Determine filename
                filename_to_create = f"file_{file_num}.py" # Default
                if ctx['naming_pattern']:
                    if '{i}' in ctx['naming_pattern']:
                        filename_to_create = ctx['naming_pattern'].format(i=file_num)
                    elif ',' in ctx['naming_pattern']:
                        names = [name.strip() for name in ctx['naming_pattern'].split(',')]
                        if file_num <= len(names):
                            filename_to_create = names[file_num-1]
                    # else, if it's a single name, it might be a prefix, let LLM decide or use default

                current_file_goal += f"The filename should be '{filename_to_create}'."
                
                # Use a more targeted prompt for multi-file creation step
                # Ensure the LLM uses 'create_file' and not 'generate_large_file' for these individual, smaller files.
                multi_file_system_prompt = self._build_system_prompt(for_multi_file_step=True)
                user_prompt = self._build_user_prompt(current_file_goal, observations, for_multi_file_step=True)
                current_req, _ = self._get_llm_response(multi_file_system_prompt, user_prompt)

                # Force create_file tool for multi-file context with enhanced validation
                if current_req and current_req.action:
                    original_tool = current_req.action.tool_name
                    if original_tool != 'create_file':
                        # Override tool selection to force create_file
                        logger.info(f"Forcing tool change from {original_tool} to create_file for multi-file step")
                        current_req.action.tool_name = 'create_file'
                        
                        # Preserve any useful content from original parameters
                        original_content = current_req.action.parameters.get('content', '')
                        if not original_content or len(original_content.strip()) < 10:
                            # Generate meaningful default content based on file context
                            default_content = self._generate_default_file_content(ctx, file_num)
                            current_req.action.parameters['content'] = default_content
                        else:
                            # Keep original content if it seems substantial
                            current_req.action.parameters['content'] = original_content
                    
                    # Validate and set path parameter
                    if 'path' not in current_req.action.parameters or not current_req.action.parameters.get('path'):
                        logger.info(f"Setting default path {filename_to_create} for multi-file step")
                        current_req.action.parameters['path'] = filename_to_create
                    
                    # Validate content quality
                    content = current_req.action.parameters.get('content', '')
                    if len(content.strip()) < 10:  # Minimum content threshold
                        logger.warning(f"Content too short for file {file_num}, enhancing with default structure")
                        current_req.action.parameters['content'] = self._generate_default_file_content(ctx, file_num)
                    
                    # Add target line count for validation
                    if ctx['lines_per_file']:
                        current_req.action.parameters['target_line_count'] = ctx['lines_per_file']
                    
                    action = current_req.action
                else:
                    # If LLM failed to provide a valid response, create a robust default action
                    logger.warning(f"LLM failed to provide response for file {file_num}, creating default action")
                    parameters = {
                        'path': filename_to_create,
                        'content': self._generate_default_file_content(ctx, file_num)
                    }
                    # Add target line count for validation
                    if ctx['lines_per_file']:
                        parameters['target_line_count'] = ctx['lines_per_file']
                    
                    action = Action(
                        tool_name='create_file',
                        parameters=parameters
                    )
                    current_req = type('MockRequest', (), {'action': action})()
            else:
                # Standard planning if not in multi-file mode or multi-file part is done
                user_prompt = self._build_user_prompt(goal, observations)
                current_req, _ = self._get_llm_response(system_prompt, user_prompt)
                if current_req: # Add this check
                    action = current_req.action # ensure 'action' is set for the normalization block
                # This second 'else' for the same 'if' is redundant and causes indentation issues.
                # Removing it and ensuring the subsequent 'if not current_req' is at the correct level.
                # else:
                #     # Standard planning if not in multi-file mode or multi-file part is done
                #     user_prompt = self._build_user_prompt(goal, observations)
                #     current_req, _ = self._get_llm_response(system_prompt, user_prompt)

            if not current_req or not current_req.action.tool_name:
                break
            
            # Check if goal appears to be completed based on planned actions
            if self._is_goal_likely_completed(goal, plan, current_req.action, self._multi_file_request_context):
                plan.append(current_req.action)
                # If it was a multi-file context and this action completes it, clear context
                if self._multi_file_request_context:
                    # Check if this will be the last file after execution
                    remaining_after_this = self._multi_file_request_context['num_files_remaining']
                    if current_req.action.tool_name == 'create_file':
                        remaining_after_this -= 1
                    
                    if remaining_after_this <= 0:
                        if current_req.action.tool_name != 'finish': # if finish not already planned
                             plan.append(Action(tool_name='finish', parameters={'result': 'All requested files created.'}))
                        self._multi_file_request_context = None
                break
            
            # Apply parameter normalization (same logic as in _translate_nl_to_tool_call)
            if current_req and hasattr(current_req, 'action'): # Check if current_req and action exist
                action = current_req.action
                if action and action.tool_name in ['create_file', 'write_file', 'read_file', 'delete_file', 'generate_large_file', 'append_to_file']:
                    if 'path' in action.parameters and action.parameters['path']:
                        import os
                        original_path = str(action.parameters['path']) # Ensure it's a string
                        # Normalize path separators for Windows
                        original_path = original_path.replace('\\', '/')
                        
                        # Prevent writing outside designated areas
                        # Allow project root 'd:/کتابخانه پایتون/2-/' and 'D:/3/'
                        project_root_abs = os.path.abspath('d:/کتابخانه پایتون/2-').replace('\\', '/') + '/'
                        sandbox_dir_abs = os.path.abspath('D:/3').replace('\\', '/') + '/'

                        if os.path.isabs(original_path):
                            abs_path_normalized = os.path.abspath(original_path).replace('\\', '/')
                            if not abs_path_normalized.startswith(project_root_abs) and not abs_path_normalized.startswith(sandbox_dir_abs):
                                # If absolute and outside allowed areas, place it in D:/3/
                                basename = os.path.basename(original_path)
                                action.parameters['path'] = os.path.join(sandbox_dir_abs, basename).replace('\\', '/')
                            else:
                                action.parameters['path'] = abs_path_normalized # Already in allowed area
                        else:
                            # Relative path, join with project root
                            # Sanitize relative path to prevent directory traversal like ../
                            safe_relative_path = os.path.normpath(os.path.join('/', original_path.lstrip('/'))).lstrip('/')
                            action.parameters['path'] = os.path.join(project_root_abs, safe_relative_path).replace('\\', '/')

                    elif action.tool_name == 'generate_large_file' or (self._multi_file_request_context and action.tool_name == 'create_file'):
                        # For generate_large_file or create_file in multi-file context, ensure path parameter exists or generate one
                        if 'path' not in action.parameters or not action.parameters.get('path'):
                            content_desc = action.parameters.get('content_description', '')
                            if not content_desc and self._multi_file_request_context:
                                content_desc = self._multi_file_request_context.get('file_description_base', 'generated_file')
                            
                            import re
                            filename_match = re.search(r'\b(\w+)\.(py|js|txt|md|html|css|json)\b', content_desc, re.IGNORECASE)
                            filename = ''
                            if filename_match:
                                filename = filename_match.group(0)
                            else:
                                file_num_str = f"_{self._multi_file_request_context['files_created_count'] + 1}" if self._multi_file_request_context else ""
                                if 'python' in content_desc.lower() or '.py' in content_desc.lower():
                                    filename = f'generated_code{file_num_str}.py'
                                elif 'javascript' in content_desc.lower() or '.js' in content_desc.lower():
                                    filename = f'generated_script{file_num_str}.js'
                                else:
                                    filename = f'generated_file{file_num_str}.txt'
                            action.parameters['path'] = os.path.join(os.path.abspath('D:/3').replace('\\', '/'), filename).replace('\\', '/')
            else:
                # If current_req or current_req.action is None, we might have broken from the loop earlier.
                # This can happen if _get_llm_response returns None or a request without an action.
                # No action to normalize in this case.
                pass
            
            plan.append(action)
            observations.append(f"Observation: planned action {action.tool_name} with params {action.parameters}")

            if self._multi_file_request_context and action.tool_name == 'create_file':
                # Update counters for planning phase
                self._multi_file_request_context['num_files_remaining'] -= 1
                self._multi_file_request_context['files_created_count'] += 1
                
                files_remaining = self._multi_file_request_context['num_files_remaining']
                files_created = self._multi_file_request_context['files_created_count']
                logger.info(f"Multi-file progress: {files_created} files planned, {files_remaining} remaining")
                
                if files_remaining <= 0:
                    # All files planned, add a finish action if not already there
                    logger.info("All multi-file creation planned, adding finish action")
                    if not any(a.tool_name == 'finish' for a in plan):
                         plan.append(Action(tool_name='finish', parameters={'result': 'All requested files created.'}))
                    # Don't clear context here - it will be cleared after execution
                    break # Exit planning loop
            
            if action.tool_name == "finish":
                # If finish is planned, and we were in multi-file, clear context
                if self._multi_file_request_context:
                    self._multi_file_request_context = None
                break
            step_count += 1
        return plan

    def _generate_default_file_content(self, ctx: dict, file_num: int) -> str:
        """
        Generate meaningful default content for a file based on multi-file context.
        Creates substantial, functional code that meets the specified line count requirements.
        """
        description = ctx.get('file_description_base', 'Generated file')
        lines_per_file = ctx.get('lines_per_file')
        
        # Determine file type and generate appropriate content
        if '.py' in ctx.get('naming_pattern', '') or 'python' in description.lower():
            content = f'"""\n{description} - File {file_num}\n\nThis file is part of a multi-file project.\n"""\n\n'
            content += 'import os\nimport sys\nimport time\nimport json\nimport logging\nfrom typing import List, Dict, Any, Optional, Union\nfrom datetime import datetime\nfrom pathlib import Path\n\n'
            
            if lines_per_file and lines_per_file > 10:
                content += f'# TODO: Implement {description.lower()} functionality\n'
                content += f'# Target: approximately {lines_per_file} lines\n\n'
                
                # Generate substantial content to reach target line count
                class_name = description.replace(" ", "").replace("-", "").replace("_", "")
                if not class_name or not class_name[0].isupper():
                    class_name = f"Module{file_num}Handler"
                
                content += f'# Configure logging\nlogging.basicConfig(level=logging.INFO)\nlogger = logging.getLogger(__name__)\n\n'
                
                content += f'class {class_name}:\n'
                content += '    """\n'
                content += f'    Main handler class for {description.lower()}.\n'
                content += '    Provides comprehensive functionality for data processing and management.\n'
                content += '    Implements robust error handling and comprehensive logging.\n'
                content += '    Supports multiple data processing operations and transformations.\n'
                content += '    """\n\n'
                content += '    def __init__(self, config: Optional[Dict[str, Any]] = None):\n'
                content += '        """Initialize the handler with optional configuration."""\n'
                content += '        self.config = config or {}\n'
                content += '        self.data_store = {}\n'
                content += '        self.status = "initialized"\n'
                content += '        self.created_at = datetime.now()\n'
                content += '        self.operation_count = 0\n'
                content += '        self.error_count = 0\n'
                content += '        self.success_count = 0\n'
                content += '        logger.info(f"Initialized {self.__class__.__name__} with config: {self.config}")\n\n'
                
                # Calculate method count based on target lines more accurately
                # Current content lines (excluding empty lines)
                current_content_lines = len([line for line in content.split('\n') if line.strip()])
                
                # Reserve lines for utility methods and main function (approximately 40 lines)
                reserved_lines = 40
                available_lines = max(0, lines_per_file - current_content_lines - reserved_lines)
                
                # Each method averages 12 lines (simpler implementation)
                lines_per_method = 12
                method_count = max(1, min(10, available_lines // lines_per_method))  # 1-10 methods
                
                # Adjust for different target sizes
                if lines_per_file < 60:
                    # For small targets, reduce reserved lines and method size
                    reserved_lines = 25
                    available_lines = max(0, lines_per_file - current_content_lines - reserved_lines)
                    method_count = 1
                    lines_per_method = 8
                elif lines_per_file > 150:
                    # For larger files, use more methods
                    method_count = max(6, min(15, available_lines // lines_per_method))
                
                # Add simple methods
                for i in range(method_count):
                    method_name = f"process_operation_{i+1}"
                    content += f'    def {method_name}(self, data: Any) -> Dict[str, Any]:\n'
                    content += f'        """Process operation {i+1}."""\n'
                    content += '        try:\n'
                    content += '            self.operation_count += 1\n'
                    content += '            result = {\n'
                    content += '                "operation_id": self.operation_count,\n'
                    content += '                "data": data,\n'
                    content += '                "status": "success"\n'
                    content += '            }\n'
                    content += '            self.data_store[f"op_{self.operation_count}"] = result\n'
                    content += '            return result\n'
                    content += '        except Exception as e:\n'
                    content += '            return {"status": "error", "error": str(e)}\n\n'
                
                # Add simple utility methods
                content += '    def get_statistics(self) -> Dict[str, Any]:\n'
                content += '        """Get handler statistics."""\n'
                content += '        return {\n'
                content += '            "operation_count": self.operation_count,\n'
                content += '            "status": self.status\n'
                content += '        }\n\n'
                
                content += '    def reset(self) -> None:\n'
                content += '        """Reset handler state."""\n'
                content += '        self.data_store.clear()\n'
                content += '        self.operation_count = 0\n\n'
                
                content += 'def main():\n'
                content += '    """Main function."""\n'
                content += f'    handler = {class_name}()\n'
                content += '    result = handler.process_operation_1("test")\n'
                content += '    print(result)\n\n'
                content += 'if __name__ == "__main__":\n'
                content += '    main()\n'
            else:
                content += 'def main():\n    """Main function for this module."""\n    pass\n\n'
                content += 'if __name__ == "__main__":\n    main()\n'
                
        elif '.js' in ctx.get('naming_pattern', '') or 'javascript' in description.lower():
            content = f'/**\n * {description} - File {file_num}\n * This file is part of a multi-file project.\n */\n\n'
            if lines_per_file and lines_per_file > 10:
                content += f'// TODO: Implement {description.lower()} functionality\n'
                content += f'// Target: approximately {lines_per_file} lines\n\n'
                
                class_name = description.replace(" ", "").replace("-", "").replace("_", "")
                if not class_name:
                    class_name = f"Module{file_num}Handler"
                
                content += f'class {class_name} {{\n'
                content += '    /**\n'
                content += f'     * Main handler class for {description.lower()}.\n'
                content += '     * Provides comprehensive functionality for data processing.\n'
                content += '     */\n'
                content += '    constructor(config = {}) {\n'
                content += '        this.config = config;\n'
                content += '        this.data = {};\n'
                content += '        this.status = "initialized";\n'
                content += '        this.operationCount = 0;\n'
                content += '        this.errorCount = 0;\n'
                content += '        console.log(`Initialized ${this.constructor.name} with config:`, this.config);\n'
                content += '    }\n\n'
                
                # Calculate method count for JavaScript
                current_content_lines = len([line for line in content.split('\n') if line.strip()])
                reserved_lines = 10  # For closing class, main function, etc.
                available_lines = max(0, lines_per_file - current_content_lines - reserved_lines)
                lines_per_method = 12  # More conservative estimate
                method_count = max(1, min(6, available_lines // lines_per_method))
                
                # Adjust for different target sizes
                if lines_per_file < 30:
                    method_count = 1
                    lines_per_method = 8
                elif lines_per_file >= 80 and lines_per_file <= 120:
                    # For medium targets, be very conservative
                    method_count = max(2, min(4, available_lines // 15))
                elif lines_per_file > 150:
                    # For larger files, use more methods with smaller size
                    method_count = max(6, min(12, available_lines // 10))
                
                # Add simple methods
                for i in range(method_count):
                    content += f'    /**\n'
                    content += f'     * Process operation {i+1}.\n'
                    content += f'     */\n'
                    content += f'    processOperation{i+1}(inputData) {{\n'
                    content += '        try {\n'
                    content += '            this.operationCount++;\n'
                    content += '            return {\n'
                    content += '                operationId: this.operationCount,\n'
                    content += '                data: inputData,\n'
                    content += '                status: "success"\n'
                    content += '            };\n'
                    content += '        } catch (error) {\n'
                    content += '            return { status: "error", error: error.message };\n'
                    content += '        }\n'
                    content += '    }\n\n'
                
                content += '    getStatistics() {\n'
                content += '        return { operationCount: this.operationCount, errorCount: this.errorCount };\n'
                content += '    }\n'
                content += '}\n\n'
                
                content += 'function main() {\n'
                content += f'    const handler = new {class_name}();\n'
                content += '    const result = handler.processOperation1("test");\n'
                content += '    console.log(result);\n'
                content += '}\n\n'
                content += 'main();\n'
            else:
                content += 'function main() {\n    // Implementation goes here\n}\n\n'
                content += 'main();\n'
        else:
            # Generic content with proper structure
            content = f'# {description} - File {file_num}\n'
            content += f'# This file is part of a multi-file project.\n\n'
            if lines_per_file and lines_per_file > 10:
                content += f'# Target: approximately {lines_per_file} lines\n\n'
                
                # Generate structured content sections
                current_content_lines = len([line for line in content.split('\n') if line.strip()])
                remaining_lines = max(0, lines_per_file - current_content_lines - 5)  # Reserve 5 lines for footer
                
                # Create meaningful sections with proper content
                section_count = max(3, min(8, remaining_lines // 8))  # 8 lines per section
                
                for i in range(section_count):
                    content += f'# ========================================\n'
                    content += f'# Section {i+1}: {description} Component {i+1}\n'
                    content += f'# ========================================\n'
                    content += f'\n'
                    content += f'# Purpose: Implement specific functionality for {description.lower()}\n'
                    content += f'# This section handles component {i+1} operations and processing\n'
                    content += f'# TODO: Add implementation details for component {i+1}\n'
                    content += f'# TODO: Define data structures and algorithms\n'
                    content += f'# TODO: Implement error handling and validation\n'
                    content += f'# TODO: Add logging and monitoring capabilities\n'
                    content += f'\n'
                
                content += f'# ========================================\n'
                content += f'# Configuration and Setup\n'
                content += f'# ========================================\n'
                content += f'\n'
            else:
                content += f'# Basic implementation structure\n'
                content += f'# TODO: Add specific functionality\n\n'
            
            content += f'# TODO: Implement {description.lower()} functionality\n'
            content += f'# End of file - {description} - File {file_num}\n'
        
        return content

    def _analyze_goal_complexity(self, goal: str) -> int:
        """
        Analyze goal complexity to determine appropriate step limit.
        Returns estimated number of steps needed.
        """
        goal_lower = goal.lower()
        
        # Simple goals (1-3 steps)
        simple_patterns = [
            r'create (a|one) file',
            r'write (a|one) file',
            r'generate (a|one)',
            r'read file',
            r'delete file',
            r'show me',
            r'what is',
            r'help with'
        ]
        
        # Medium complexity goals (3-8 steps)
        medium_patterns = [
            r'create (\d+) files',
            r'build (a|an) app',
            r'make (a|an) website',
            r'develop',
            r'implement',
            r'refactor'
        ]
        
        # Complex goals (8-15 steps)
        complex_patterns = [
            r'complete project',
            r'full application',
            r'comprehensive',
            r'entire system',
            r'multiple.*and.*',
            r'with.*database.*and.*api'
        ]
        
        import re
        
        # Check for complex patterns first
        for pattern in complex_patterns:
            if re.search(pattern, goal_lower):
                return 12
        
        # Check for medium patterns
        for pattern in medium_patterns:
            if re.search(pattern, goal_lower):
                return 6
        
        # Check for simple patterns
        for pattern in simple_patterns:
            if re.search(pattern, goal_lower):
                return 3
        
        # Default to medium complexity if no pattern matches
        return 8
    
    def _is_goal_likely_completed(self, goal: str, current_plan: List[Action], next_action: Action, multi_file_ctx: Optional[Dict[str, Any]] = None) -> bool:
        """
        Determine if the goal is likely completed based on current plan and next action.
        """
        # If next action is 'finish', goal is completed
        if next_action.tool_name == 'finish':
            return True
        
        goal_lower = goal.lower()
        
        # For file creation goals
        if any(word in goal_lower for word in ['create', 'generate', 'write', 'make']):
            # Check if we've already created the requested files
            file_creation_actions = [a for a in current_plan if a.tool_name in ['create_file', 'write_file', 'generate_large_file']]
            
            # Simple file creation - if we have 1 file creation action, likely done
            if 'one file' in goal_lower or 'a file' in goal_lower:
                return len(file_creation_actions) >= 1
            
            # Multiple files - check if we've created the requested number
            if multi_file_ctx:
                # If in multi-file context, completion is when num_files_remaining is 0 (or <=0)
                # and the next action is 'finish' or we are about to add the last file creation action.
                if multi_file_ctx['num_files_remaining'] <= 1 and next_action.tool_name == 'create_file':
                    return True # This create_file action will be the last one
                if multi_file_ctx['num_files_remaining'] <= 0 and next_action.tool_name == 'finish':
                    return True
                return False # Still more files to create in multi-file context
            else:
                # Standard multi-file check if not using the new context (legacy or simple cases)
                import re
                number_match = re.search(r'(\d+)\s+files?', goal_lower)
                if number_match:
                    requested_files = int(number_match.group(1))
                    return len(file_creation_actions) >= requested_files
        
        # For reading/analysis goals
        if any(word in goal_lower for word in ['read', 'analyze', 'show', 'display']):
            read_actions = [a for a in current_plan if a.tool_name in ['read_file', 'list_files']]
            return len(read_actions) >= 1
        
        # Default: not completed yet
        return False

    def batch_execute_plan(self, plan: List[Action], parallel: bool = False) -> MindLinkResponse:
        """
        Execute a given plan of Actions sequentially.
        """
        last_resp: Optional[MindLinkResponse] = None
        successful_actions = 0
        failed_actions = 0
        error_messages = []
        
        for i, action in enumerate(plan):
            request = MindLinkRequest(action=action, reasoning=None, thought=None)
            resp = self._execute_tool(request)
            
            if resp.status.lower() == "error":
                failed_actions += 1
                error_messages.append(f"Action {i+1} ({action.tool_name}): {resp.observation}")
                # For file generation tools, continue with remaining actions
                if action.tool_name in ['generate_large_file', 'create_file', 'write_file']:
                    print(f"Warning: Action {i+1} failed, continuing with remaining actions...")
                    continue
                else:
                    # For other tools, stop execution
                    return resp
            else:
                successful_actions += 1
                last_resp = resp
        
        if last_resp is None:
            # No actions to execute: return a default success response
            return MindLinkResponse(observation="", status="success", finished=True)
        
        # If we had mixed results, provide a summary
        if failed_actions > 0 and successful_actions > 0:
            summary = f"Batch execution completed with {successful_actions} successful and {failed_actions} failed actions.\n"
            summary += "Errors:\n" + "\n".join(error_messages)
            return MindLinkResponse(observation=summary, status="partial_success", finished=True)
        
        return last_resp

    def run(self, goal: str) -> Tuple[bool, str, MindLinkResponse]:
        """
        Run the agent to achieve the given goal.
        """
        # Reset history for this run
        self.clear_history()
        log_event(create_user_input_log(self.session_id, goal, intent="agent_goal"))
        
        # Command comprehension phase
        processed_goal = self._preprocess_command(goal)
        if processed_goal != goal:
            logging.info(f"Command preprocessed: '{goal}' -> '{processed_goal}'")
        
        # Plan phase
        plan = self.plan_once(processed_goal)
        if plan:
            first = plan[0]
            if first.tool_name == 'echo':
                msg = first.parameters.get('message', '')
                resp = MindLinkResponse(observation=msg, status='success', finished=True)
                return True, msg, resp
            # Execute plan
            resp = self.batch_execute_plan(plan)
            return resp.status == 'success', resp.observation, resp
        # No plan: fallback to chat
        chat_resp = self._generate_with_failover("You are a helpful assistant.", processed_goal, self.history)
        resp = MindLinkResponse(observation=chat_resp, status='success', finished=True)
        return True, chat_resp, resp
    
    def _preprocess_command(self, command: str) -> str:
        """
        Preprocess user command using intelligent command comprehension.
        
        Args:
            command: The original user command
            
        Returns:
            Processed command or clarification request
        """
        if not self.enable_command_comprehension or not self.command_comprehension_engine:
            return command
        
        try:
            # Analyze the command
            analysis = self.command_comprehension_engine.analyze_command(command)
            
            # Log the analysis result
            logging.info(f"Command analysis - Intent: {analysis.intent}, Confidence: {analysis.confidence.value}")
            
            # If clarification is needed, return a clarification request
            if analysis.clarification_needed and analysis.confidence == ConfidenceLevel.LOW:
                clarification_question = self.command_comprehension_engine.generate_clarification_question(analysis)
                if clarification_question:
                    return f"I need clarification about your request: {clarification_question}"
            
            # If we have high or medium confidence, enhance the command with extracted information
            if analysis.confidence in [ConfidenceLevel.HIGH, ConfidenceLevel.MEDIUM]:
                enhanced_command = self._enhance_command_with_analysis(command, analysis)
                return enhanced_command
            
            # For low confidence without clarification needs, return original command
            return command
            
        except Exception as e:
            logging.error(f"Error in command preprocessing: {e}")
            # Return original command if preprocessing fails
            return command
    
    def _enhance_command_with_analysis(self, original_command: str, analysis) -> str:
        """
        Enhance the original command with extracted intent and entities.
        
        Args:
            original_command: The original user command
            analysis: CommandAnalysis result
            
        Returns:
            Enhanced command with structured information
        """
        try:
            # Create an enhanced prompt that includes the analysis
            enhanced_parts = [original_command]
            
            # Add intent information
            if analysis.intent and analysis.intent != "unknown":
                enhanced_parts.append(f"\n[Intent: {analysis.intent}]")
            
            # Add entity information
            if analysis.entities:
                entity_info = []
                for key, value in analysis.entities.items():
                    if isinstance(value, list):
                        entity_info.append(f"{key}: {', '.join(map(str, value))}")
                    else:
                        entity_info.append(f"{key}: {value}")
                
                if entity_info:
                    enhanced_parts.append(f"\n[Entities: {'; '.join(entity_info)}]")
            
            # Add confidence level for the LLM to consider
            enhanced_parts.append(f"\n[Analysis Confidence: {analysis.confidence.value}]")
            
            return "".join(enhanced_parts)
            
        except Exception as e:
            logging.error(f"Error enhancing command: {e}")
            return original_command
