from mindlink.agent import AgentOS
from mindlink.models.openrouter import OpenRouterModel

llm = OpenRouterModel()
agent = AgentOS(llm=llm, system_prompt_template='You are a helpful AI assistant.')

print("Testing fixed iterative planning...")
plan = agent.plan_once('Create 5 Python files with 300 lines of functional code each')

print(f'Generated {len(plan)} actions:')
for i, action in enumerate(plan):
    print(f'Action {i+1}: {action.tool_name}')
    if action.tool_name == 'generate_large_file':
        print(f'  Parameters: {list(action.parameters.keys())}')
        print(f'  Path: {action.parameters.get("path", "MISSING")}')
        print(f'  Content description: {action.parameters.get("content_description", "MISSING")[:100]}...')
    print()

generate_actions = [a for a in plan if a.tool_name == 'generate_large_file']
print(f'\nFound {len(generate_actions)} generate_large_file actions with path parameter')