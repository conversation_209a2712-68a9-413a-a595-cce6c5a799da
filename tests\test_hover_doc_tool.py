import pytest
from mindlink.tools.doc_tools import HoverDocTool


def test_hover_doc_function(tmp_path):
    # Create a sample file with a function and a docstring
    sample = tmp_path / "sample.py"
    sample.write_text(
        '''
def foo():
    """This is foo."""
    return 42
'''
    )
    tool = HoverDocTool()
    # Query at the function definition line
    res = tool.execute(path=str(sample), line=2, column=0)
    assert res['status'] == 'success'
    assert res['observation'] == 'This is foo.'


def test_hover_doc_no_docstring(tmp_path):
    # Create a sample file with a function without docstring
    sample = tmp_path / "sample2.py"
    sample.write_text(
        '''
def bar():
    pass
'''
    )
    tool = HoverDocTool()
    res = tool.execute(path=str(sample), line=2, column=0)
    assert res['status'] == 'success'
    assert 'No docstring available' in res['observation']


def test_hover_doc_no_symbol(tmp_path):
    # Create an empty file (no symbols)
    sample = tmp_path / "sample3.py"
    sample.write_text("# no code here")
    tool = HoverDocTool()
    res = tool.execute(path=str(sample), line=1, column=0)
    assert res['status'] == 'error'
    assert 'No symbol' in res['error'] 