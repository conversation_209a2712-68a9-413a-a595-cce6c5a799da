import unittest
from unittest.mock import Magic<PERSON><PERSON>, patch, call
import json
import os
import requests # Required for requests.Response

from mindlink.models.openrouter import OpenRouterModel

# Helper to create a mock stream response
def create_mock_stream_response(chunks_data):
    mock_response = MagicMock(spec=requests.Response)
    mock_response.status_code = 200
    
    def iter_lines():
        for chunk_text in chunks_data:
            # Simulate the data format: data: {"choices": [{"delta": {"content": "chunk_text"}}]}
            # Also handle the [DONE] message
            if chunk_text == "[DONE]":
                yield f"data: {chunk_text}\n\n".encode('utf-8')
            else:
                # Construct the JSON part for a content chunk
                delta_content = {"choices": [{"delta": {"content": chunk_text}}]}
                yield f"data: {json.dumps(delta_content)}\n\n".encode('utf-8')
        yield "".encode('utf-8') # End of stream

    mock_response.iter_lines = iter_lines
    mock_response.raise_for_status = MagicMock() # Ensure this doesn't raise
    return mock_response

# Helper to create a mock non-stream response
def create_mock_non_stream_response(content, tokens=5):
    mock_response = MagicMock(spec=requests.Response)
    mock_response.status_code = 200
    response_json = {
        "choices": [{"message": {"content": content}}],
        "usage": {"total_tokens": tokens}
    }
    mock_response.json = MagicMock(return_value=response_json)
    mock_response.raise_for_status = MagicMock()
    return mock_response

class TestOpenRouterModelStreaming(unittest.TestCase):

    def setUp(self):
        # Set a default API key for OpenRouterModel initialization
        os.environ["OPENROUTER_API_KEY"] = "sk-or-v1-testapikey"
        # Using a model known to be in OPENROUTER_MODELS for initialization
        self.model = OpenRouterModel(model_name="mistral-small-3.1") 
        # Reset API key if it's not meant to be globally set for other tests
        # For this test class, it's fine as it's scoped.

    @patch('requests.post')
    def test_generate_streaming(self, mock_post):
        chunks_content = ["This ", "is ", "a ", "streamed ", "response."]
        # The stream from OpenRouter ends with "data: [DONE]"
        stream_chunks_for_mock = chunks_content + ["[DONE]"] 
        
        mock_post.return_value = create_mock_stream_response(stream_chunks_for_mock)
        
        callback_mock = MagicMock()
        system_prompt = "System prompt for OpenRouter"
        user_prompt = "User prompt for OpenRouter stream"

        # Test with stream=True explicitly
        result = self.model.generate(system_prompt, user_prompt, stream=True, callback=callback_mock)

        # Verify requests.post was called correctly
        expected_payload = {
            "model": self.model.model_id,
            "messages": [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ],
            "stream": True,
            **self.model.params 
        }
        mock_post.assert_called_once_with(
            self.model.api_url,
            headers=self.model.headers,
            json=expected_payload,
            stream=True
        )

        # Verify callback was called for each content chunk
        expected_calls = [call(chunk_data) for chunk_data in chunks_content] # Don't include [DONE]
        self.assertEqual(callback_mock.call_args_list, expected_calls)

        # Verify the final concatenated result
        self.assertEqual(result, "".join(chunks_content))
        # Token usage is typically not available or None for streams in OpenRouterModel's current impl
        self.assertIsNone(self.model.last_tokens_used)


    @patch('requests.post')
    def test_generate_non_streaming(self, mock_post):
        expected_response_content = "This is a non-streaming OpenRouter response."
        mock_post.return_value = create_mock_non_stream_response(expected_response_content, tokens=10)

        system_prompt = "System prompt non-stream"
        user_prompt = "User prompt non-stream"

        result = self.model.generate(system_prompt, user_prompt, stream=False)

        # Verify requests.post was called correctly
        expected_payload = {
            "model": self.model.model_id,
            "messages": [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ],
            "stream": False,
            **self.model.params
        }
        mock_post.assert_called_once_with(
            self.model.api_url,
            headers=self.model.headers,
            json=expected_payload,
            stream=False 
        )

        self.assertEqual(result, expected_response_content)
        self.assertEqual(self.model.last_tokens_used, 10)

    def tearDown(self):
        if "OPENROUTER_API_KEY" in os.environ:
            del os.environ["OPENROUTER_API_KEY"]

if __name__ == '__main__':
    unittest.main()
