#!/usr/bin/env python3
"""
Debug script to understand why GenerateLargeFileTool stops early
"""

import os
import sys
from pathlib import Path

# Add the project root to Python path
sys.path.insert(0, str(Path(__file__).parent))

from mindlink.tools.file_tools import GenerateLargeFileTool

def debug_tool():
    """Debug the GenerateLargeFileTool to understand early stopping"""
    tool = GenerateLargeFileTool()
    
    print("=== Debugging GenerateLargeFileTool ===")
    print("Testing with smaller target to understand behavior...")
    
    # Test with smaller target first
    result = tool.execute(
        path="debug_small_file.py",
        content_description="Create a simple Python calculator with basic arithmetic operations, input validation, and a main menu loop. Include detailed comments and docstrings.",
        target_line_count=200,
        max_chunks=10,
        chunk_size_description="Generate about 50-80 lines of functional Python code with comments.",
        context_carryover_lines=15
    )
    
    print(f"\nSmall file result: {result}")
    
    if result.get('status') == 'success':
        lines_written = result.get('result', {}).get('lines_written')
        chunks_written = result.get('result', {}).get('chunks_written')
        target_met = result.get('result', {}).get('target_lines_met')
        
        print(f"\n📝 Lines written: {lines_written}")
        print(f"🔢 Chunks written: {chunks_written}")
        print(f"🎯 Target met: {target_met}")
        print(f"📈 Target vs Actual: 200 vs {lines_written} ({lines_written/200*100:.1f}% of target)")
        
        if lines_written >= 150:  # 75% of target
            print("✅ Small test PASSED - tool can generate reasonable amounts")
            
            # Now test with larger target
            print("\n=== Testing with larger target ===")
            result2 = tool.execute(
                path="debug_large_file.py",
                content_description="Create a comprehensive Python web application with Flask, database models, API endpoints, authentication, file handling, error handling, logging, configuration management, and comprehensive functionality. Include detailed comments, docstrings, and multiple classes and functions.",
                target_line_count=500,
                max_chunks=20,
                chunk_size_description="Generate about 100-150 lines of functional Python code with detailed comments and docstrings.",
                context_carryover_lines=20
            )
            
            print(f"\nLarge file result: {result2}")
            
            if result2.get('status') == 'success':
                lines_written2 = result2.get('result', {}).get('lines_written')
                chunks_written2 = result2.get('result', {}).get('chunks_written')
                target_met2 = result2.get('result', {}).get('target_lines_met')
                
                print(f"\n📝 Lines written: {lines_written2}")
                print(f"🔢 Chunks written: {chunks_written2}")
                print(f"🎯 Target met: {target_met2}")
                print(f"📈 Target vs Actual: 500 vs {lines_written2} ({lines_written2/500*100:.1f}% of target)")
                
                if lines_written2 >= 400:  # 80% of target
                    print("🎉 SUCCESS: Tool can generate large files!")
                else:
                    print("⚠️  Tool still struggles with larger targets")
            else:
                print(f"❌ Large file test failed: {result2.get('error')}")
        else:
            print("❌ Small test FAILED - tool has fundamental issues")
    else:
        print(f"❌ Error: {result.get('error', 'Unknown error')}")

if __name__ == "__main__":
    debug_tool()