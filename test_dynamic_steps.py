#!/usr/bin/env python3
"""
Test script to verify dynamic step determination in MindLink Agent.
This tests that the agent no longer always uses 15 steps but adapts based on goal complexity.
"""

import sys
import os

# Add the current directory to Python path to import mindlink
sys.path.insert(0, os.path.dirname(__file__))

from mindlink.agent import AgentOS
from mindlink.schemas.mindlink import Action

def test_goal_complexity_analysis():
    """Test the goal complexity analysis function."""
    print("Testing goal complexity analysis...")
    
    # Create a mock agent to test the complexity analysis
    class MockLLM:
        def generate(self, *args, **kwargs):
            return "Mock response"
    
    agent = AgentOS(MockLLM(), "Test prompt", max_steps=25)
    
    # Test simple goals
    simple_goals = [
        "create a file",
        "read file test.txt",
        "show me the contents",
        "what is this file"
    ]
    
    # Test medium complexity goals
    medium_goals = [
        "create 3 files",
        "build an app",
        "develop a website",
        "implement a feature"
    ]
    
    # Test complex goals
    complex_goals = [
        "complete project with database and API",
        "comprehensive application",
        "entire system with multiple components",
        "full application with authentication"
    ]
    
    print("\nSimple goals (expected: 3 steps):")
    for goal in simple_goals:
        complexity = agent._analyze_goal_complexity(goal)
        print(f"  '{goal}' -> {complexity} steps")
        assert complexity == 3, f"Expected 3 steps for simple goal, got {complexity}"
    
    print("\nMedium complexity goals (expected: 6 steps):")
    for goal in medium_goals:
        complexity = agent._analyze_goal_complexity(goal)
        print(f"  '{goal}' -> {complexity} steps")
        assert complexity == 6, f"Expected 6 steps for medium goal, got {complexity}"
    
    print("\nComplex goals (expected: 12 steps):")
    for goal in complex_goals:
        complexity = agent._analyze_goal_complexity(goal)
        print(f"  '{goal}' -> {complexity} steps")
        assert complexity == 12, f"Expected 12 steps for complex goal, got {complexity}"
    
    print("\n✅ Goal complexity analysis working correctly!")

def test_goal_completion_detection():
    """Test the goal completion detection function."""
    print("\nTesting goal completion detection...")
    
    class MockLLM:
        def generate(self, *args, **kwargs):
            return "Mock response"
    
    agent = AgentOS(MockLLM(), "Test prompt", max_steps=25)
    
    # Mock actions
    
    create_file_action = Action(tool_name="create_file", parameters={"path": "test.py"})
    finish_action = Action(tool_name="finish", parameters={})
    read_file_action = Action(tool_name="read_file", parameters={"path": "test.txt"})
    
    # Test simple file creation completion
    goal = "create a file"
    current_plan = [create_file_action]
    next_action = finish_action
    
    is_completed = agent._is_goal_likely_completed(goal, current_plan, next_action)
    print(f"Goal: '{goal}' with finish action -> Completed: {is_completed}")
    assert is_completed, "Should detect completion when finish action is next"
    
    # Test file creation goal with file already created
    goal = "create one file"
    current_plan = [create_file_action]
    next_action = Action(tool_name="echo", parameters={})
    
    is_completed = agent._is_goal_likely_completed(goal, current_plan, next_action)
    print(f"Goal: '{goal}' with 1 file created -> Completed: {is_completed}")
    assert is_completed, "Should detect completion when requested files are created"
    
    # Test reading goal completion
    goal = "read the file"
    current_plan = [read_file_action]
    next_action = Action(tool_name="echo", parameters={})
    
    is_completed = agent._is_goal_likely_completed(goal, current_plan, next_action)
    print(f"Goal: '{goal}' with read action -> Completed: {is_completed}")
    assert is_completed, "Should detect completion when file is read"
    
    print("\n✅ Goal completion detection working correctly!")

def test_dynamic_step_calculation():
    """Test that dynamic step calculation works within max_steps bounds."""
    print("\nTesting dynamic step calculation...")
    
    class MockLLM:
        def generate(self, *args, **kwargs):
            return "Mock response"
    
    agent = AgentOS(MockLLM(), "Test prompt", max_steps=25)
    
    # Test that simple goals get fewer steps
    simple_goal = "create a file"
    complexity = agent._analyze_goal_complexity(simple_goal)
    dynamic_max = min(agent.max_steps, complexity)
    
    print(f"Simple goal: '{simple_goal}'")
    print(f"  Complexity: {complexity} steps")
    print(f"  Dynamic max: {dynamic_max} steps")
    print(f"  Agent max_steps: {agent.max_steps}")
    
    assert dynamic_max == 3, f"Expected 3 steps for simple goal, got {dynamic_max}"
    assert dynamic_max < agent.max_steps, "Dynamic steps should be less than max_steps for simple goals"
    
    # Test that complex goals are bounded by max_steps
    complex_goal = "comprehensive application with database and API"
    complexity = agent._analyze_goal_complexity(complex_goal)
    dynamic_max = min(agent.max_steps, complexity)
    
    print(f"\nComplex goal: '{complex_goal}'")
    print(f"  Complexity: {complexity} steps")
    print(f"  Dynamic max: {dynamic_max} steps")
    print(f"  Agent max_steps: {agent.max_steps}")
    
    assert dynamic_max <= agent.max_steps, "Dynamic steps should not exceed max_steps"
    
    print("\n✅ Dynamic step calculation working correctly!")

if __name__ == "__main__":
    print("🧪 Testing Dynamic Step Determination")
    print("=" * 50)
    
    try:
        test_goal_complexity_analysis()
        test_goal_completion_detection()
        test_dynamic_step_calculation()
        
        print("\n" + "=" * 50)
        print("🎉 All tests passed! Dynamic step determination is working correctly.")
        print("\nThe agent will now:")
        print("  • Analyze goal complexity to determine appropriate step count")
        print("  • Use fewer steps for simple goals (3 steps)")
        print("  • Use moderate steps for medium goals (6 steps)")
        print("  • Use more steps for complex goals (12 steps)")
        print("  • Stop early when goals appear completed")
        print("  • Respect the max_steps upper bound (25 steps)")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)