import time
from mindlink import load_config
from mindlink.main import create_llm
from mindlink.agent import AgentOS
from mindlink.tools.batch_execute_tool import BatchExecuteTool
import sys, io


def main():
    # Load configuration and initialize agent
    config = load_config(None)
    llm = create_llm(config)
    agent = AgentOS(
        llm=llm,
        system_prompt_template=config['agent']['system_prompt_template'],
        max_steps=config['agent']['max_steps'],
        max_parsing_retries=config['agent']['max_parsing_retries'],
        retry_delay=config['agent']['retry_delay']
    )

    # Define the File Operations Test goal
    goal = (
        "Create a complex directory structure with the following:\n"
        "1. A root directory called 'project'\n"
        "2. Inside 'project', create subdirectories: 'src', 'tests', 'docs', and 'config'\n"
        "3. In 'src', create a file called 'main.py' with a simple Python program that prints 'Hello World'\n"
        "4. In 'tests', create a test file called 'test_main.py' that imports the main module and tests it\n"
        "5. In 'docs', create a README.md with documentation for the project\n"
        "6. In 'config', create a config.json file with some example configuration\n"
        "7. List all the files and directories you created\n"
        "8. Finally, read the content of each file to verify"
    )

    # Plan once
    print("Planning operations...")
    # Suppress LLM debug logs during planning
    _stdout = sys.stdout
    sys.stdout = io.StringIO()
    plan_requests = agent.plan(goal)
    sys.stdout = _stdout
    plan_actions = [req.action for req in plan_requests]

    # Execute the plan in one batch
    batch_tool = BatchExecuteTool()
    print(f"Executing batch of {len(plan_actions)} actions...")
    start = time.time()
    resp = batch_tool.execute(plan=plan_actions)
    end = time.time()

    # Report results
    print(f"Batch execution took {end - start:.2f} seconds")
    print(f"Status: {resp['status']}")
    if resp.get('error'):
        print(f"Errors: {resp['error']}")
    print("Observations:")
    print(resp['observation'])


if __name__ == '__main__':
    main() 