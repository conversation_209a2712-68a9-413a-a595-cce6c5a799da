"""
OpenRouter implementation of the LLM interface.
"""

import os
import json
import re
import traceback
import time
import requests
from typing import Dict, Any, List, Optional
import logging # Added
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

try:
    from .llm import LLMInterface
except ImportError:
    # Handle case when running directly
    from llm import LLMInterface

import logging
logging.basicConfig(level=logging.WARNING)

logger = logging.getLogger(__name__) # Added


# Only the free Mistral Small 3.1 model is available
OPENROUTER_MODELS = {
    "mistral-small-3.1": {
        'name': 'Mistral Small 3.1 (OpenRouter)',
        'api': 'OR',
        'model_id': 'mistralai/mistral-small-3.1-24b-instruct:free',  # Free tier model
        'icon': 'bot2-icon.png',
        'params': {"temperature": 0.1, "max_tokens": 8192, "top_p": 0.9},
        'headers': {"HTTP-Referer": "http://localhost", "X-Title": "MindLink Agent"},
        # Remove static api_key field
        'api_url': 'https://openrouter.ai/api/v1/chat/completions',
        'supports_streaming': True
    },
}



class OpenRouterModel(LLMInterface):
    """
    OpenRouter implementation of the LLM interface.
    """

    def __init__(self,
                 model_name: str = "mistral-small-3.1",
                 api_key: Optional[str] = None,
                 temperature: Optional[float] = None,
                 max_tokens: Optional[int] = None,
                 top_p: Optional[float] = None,
                 stream: bool = False, # Default stream behavior for the instance
                 callback: Optional[callable] = None): # Default callback for the instance
        """
        Initialize the OpenRouter model.

        Args:
            model_name: Name of the model to use (default: deepseek-r1)
            api_key: OpenRouter API key (defaults to OPENROUTER_API_KEY environment variable or model's default)
            temperature: Temperature for generation (overrides model default)
            max_tokens: Maximum tokens to generate (overrides model default)
            top_p: Top-p sampling parameter (overrides model default)
            stream: Whether to stream responses by default for this instance.
            callback: Default callback function for streaming for this instance.
        """
        if model_name not in OPENROUTER_MODELS:
            raise ValueError(f"Unknown model: {model_name}. Available models: {', '.join(OPENROUTER_MODELS.keys())}")

        # Get model configuration
        self.model_config = OPENROUTER_MODELS[model_name].copy()
        self._model_name_property = self.model_config.get('name', self.model_config.get('model_id', model_name))
        self.model_id = self.model_config.get('model_id', model_name)

        # Set API key
        self.api_key = api_key or os.environ.get("OPENROUTER_API_KEY")
        # Debug: log the API key (masked) and headers
        masked_key = self.api_key[:8] + "*" * (len(self.api_key) - 12) + self.api_key[-4:] if self.api_key and len(self.api_key) > 12 else str(self.api_key)
        logging.warning(f"[DEBUG] OpenRouterModel instantiated with API key: {masked_key}")

        # Set model parameters
        self.params = self.model_config['params'].copy()
        if temperature is not None:
            self.params["temperature"] = temperature
        if max_tokens is not None:
            self.params["max_tokens"] = max_tokens
        else:
            # Ensure a default value is set if not overridden
            self.params["max_tokens"] = 4096
        if top_p is not None:
            self.params["top_p"] = top_p

        # Set headers
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
            **self.model_config.get('headers', {})
        }
        logging.warning(f"[DEBUG] OpenRouterModel headers: {self.headers}")
        self.api_url = self.model_config['api_url']
        self.stream = stream
        self.callback = callback
        self.last_tokens_used: Optional[int] = 0
        self.last_latency_ms: Optional[float] = 0

    @property
    def model_name(self) -> str:
        """Returns the configured model name for logging."""
        return self._model_name_property

    def _handle_streaming_response(self, response: requests.Response, current_callback: Optional[callable]) -> str:
        """Handles streaming response from OpenRouter with chunked processing."""
        full_response_content = ""
        try:
            for line in response.iter_lines():
                if line:
                    decoded_line = line.decode('utf-8')
                    if decoded_line.startswith("data: "):
                        json_str = decoded_line[len("data: "):]
                        if json_str.strip() == "[DONE]":
                            break
                        try:
                            chunk = json.loads(json_str)
                            if chunk.get("choices") and chunk["choices"][0].get("delta"):
                                content_delta = chunk["choices"][0]["delta"].get("content", "")
                                full_response_content += content_delta
                                if current_callback:
                                    try:
                                        current_callback(content_delta)
                                    except Exception as cb_err:
                                        print(f"Warning: Callback error during streaming: {cb_err}")
                        except json.JSONDecodeError as e:
                            print(f"Error decoding stream chunk: {e}")
                            continue
            # Token usage for streams is often not available or needs separate handling
            self.last_tokens_used = None 
        except Exception as e:
            print(f"Error during streaming: {e}")
        return full_response_content

    def generate(self, 
                 system_prompt: str, 
                 user_prompt: str, 
                 history: Optional[List[Dict[str, str]]] = None,
                 stream: Optional[bool] = None,
                 callback: Optional[callable] = None) -> str:
        """
        Generate a response from the OpenRouter model.

        Args:
            system_prompt: The system prompt to set the context and behavior.
            user_prompt: The user prompt containing the current query.
            history: Optional conversation history.
            stream: Whether to stream the response. Overrides instance default.
            callback: Callback function for streaming. Overrides instance default.

        Returns:
            The model's response as a string.
        """
        messages = [{"role": "system", "content": system_prompt}]
        if history:
            messages.extend(history)
        messages.append({"role": "user", "content": user_prompt})

        should_stream = stream if stream is not None else self.stream
        current_callback = callback if callback is not None else self.callback
        
        request_data = {
            "model": self.model_id,
            "messages": messages,
            "stream": should_stream,
            **self.params
        }
        
        self.last_tokens_used = None
        self.last_latency_ms = None
        start_time = time.time()

        try:
            # Add timeout to prevent hanging - 60 seconds for regular requests, 120 for streaming
            timeout = 120 if should_stream else 60
            response = requests.post(self.api_url, headers=self.headers, json=request_data, stream=should_stream, timeout=timeout)
            self.last_latency_ms = (time.time() - start_time) * 1000 # Initial latency
            response.raise_for_status()

            if should_stream:
                content = self._handle_streaming_response(response, current_callback)
                # Latency for streaming is more complex; this is time to first byte + stream duration
                self.last_latency_ms = (time.time() - start_time) * 1000 
                return content
            else:
                # Log the raw response text before attempting to parse it as JSON
                raw_response_text = response.text
                text_len = len(raw_response_text)
                if text_len > 2000: # Only log snippets if very long
                    logger.debug(f"OpenRouter Raw LLM Response Text (len={text_len}):\nSTART:\n{raw_response_text[:1000]}\n...\nEND:\n{raw_response_text[-1000:]}")
                else:
                    logger.debug(f"OpenRouter Raw LLM Response Text (len={text_len}):\n{raw_response_text}")

                response_data = response.json()
                if 'usage' in response_data and response_data['usage'] is not None:
                    self.last_tokens_used = response_data['usage'].get("total_tokens")
                
                if response_data.get("choices") and response_data["choices"][0].get("message"):
                    return response_data["choices"][0]["message"].get("content", "")
                return ""
            
        except requests.exceptions.Timeout as e:
            if self.last_latency_ms is None: # If error before first byte
                self.last_latency_ms = (time.time() - start_time) * 1000
            error_msg = f"OpenRouter API request timed out after {timeout} seconds. This may be due to network issues, server overload, or the request being too complex. Consider reducing the content size or trying again later."
            print(error_msg)
            raise requests.exceptions.RequestException(error_msg) from e
        except requests.exceptions.RequestException as e:
            if self.last_latency_ms is None: # If error before first byte
                self.last_latency_ms = (time.time() - start_time) * 1000
            print(f"Error calling OpenRouter API: {e}")
            # Consider how to handle errors in streaming vs non-streaming contexts.
            # For now, re-raising.
            raise
        except json.JSONDecodeError as e: # Only relevant for non-streaming
            if self.last_latency_ms is None:
                self.last_latency_ms = (time.time() - start_time) * 1000
            response_text_preview = response.text[:500] if 'response' in locals() and hasattr(response, 'text') else "N/A"
            print(f"Error decoding JSON response from OpenRouter: {e}. Response text: {response_text_preview}")
            raise
