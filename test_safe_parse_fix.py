#!/usr/bin/env python3
"""
Test script to verify the parameter normalization fix for safe_parse_llm_response path.
"""

import sys
import os
sys.path.insert(0, os.path.abspath('.'))

from mindlink.agent import AgentOS
from mindlink.schemas.mindlink import Mind<PERSON>inkRequest, Action

def test_safe_parse_path():
    """Test the safe_parse_llm_response path with generate_large_file actions."""
    
    # Test case 1: generate_large_file action without path parameter
    print("\n=== Test Case 1: generate_large_file without path ===")
    
    # Create a translation request that would result in an echo action
    # with embedded generate_large_file JSON
    embedded_json = '''{
        "action": {
            "tool_name": "generate_large_file",
            "parameters": {
                "content_description": "A Python script for data processing",
                "lines": 300
            }
        },
        "reasoning": "Creating a large Python file",
        "thought": "This will be a comprehensive data processing script"
    }'''
    
    echo_action = Action(
        tool_name="echo",
        parameters={"message": embedded_json}
    )
    
    translation_req = MindLinkRequest(
        action=echo_action,
        reasoning="Fallback to echo",
        thought="Using echo as fallback"
    )
    
    # Simulate the plan_once logic for the echo case
    raw_msg = translation_req.action.parameters.get("message", "")
    from mindlink.utils.json_parser import safe_parse_llm_response
    parsed_req, _ = safe_parse_llm_response(raw_msg)
    
    if parsed_req and parsed_req.action.tool_name and parsed_req.action.tool_name != "echo":
        # Apply parameter normalization to the parsed action
        action = parsed_req.action
        print(f"Original action: {action.tool_name}")
        print(f"Original parameters: {action.parameters}")
        
        if action.tool_name in ['create_file', 'write_file', 'read_file', 'delete_file', 'generate_large_file']:
            if 'path' in action.parameters and action.parameters['path']:
                # Normalize path to absolute path under D:/3/
                import os
                original_path = action.parameters['path']
                if not os.path.isabs(original_path):
                    action.parameters['path'] = f"D:/3/{original_path}"
                elif not original_path.startswith("D:/3/"):
                    # If it's absolute but not under D:/3/, make it relative to D:/3/
                    basename = os.path.basename(original_path)
                    action.parameters['path'] = f"D:/3/{basename}"
            elif action.tool_name == 'generate_large_file':
                # For generate_large_file, ensure path parameter exists
                if 'path' not in action.parameters or not action.parameters['path']:
                    # Generate a default path based on content description
                    content_desc = action.parameters.get('content_description', 'generated_file')
                    # Extract a reasonable filename from content description
                    import re
                    filename_match = re.search(r'\b(\w+)\.(py|js|txt|md|html|css|json)\b', content_desc)
                    if filename_match:
                        filename = filename_match.group(0)
                    else:
                        # Generate filename based on content type
                        if 'python' in content_desc.lower() or 'py' in content_desc.lower():
                            filename = 'generated_code.py'
                        elif 'javascript' in content_desc.lower() or 'js' in content_desc.lower():
                            filename = 'generated_code.js'
                        elif 'html' in content_desc.lower():
                            filename = 'generated_page.html'
                        else:
                            filename = 'generated_file.txt'
                    action.parameters['path'] = f"D:/3/{filename}"
        
        print(f"Normalized action: {action.tool_name}")
        print(f"Normalized parameters: {action.parameters}")
        
        # Check if path was added
        if 'path' in action.parameters:
            print(f"✅ SUCCESS: Path parameter added: {action.parameters['path']}")
        else:
            print("❌ FAILED: Path parameter still missing")
    else:
        print("❌ FAILED: Could not parse embedded JSON or got echo action")

if __name__ == "__main__":
    test_safe_parse_path()