import unittest
from unittest.mock import patch, MagicMock, call
import os
from pathlib import Path
import logging

from mindlink.tools.file_tools import (
    GenerateLargeFileTool,
    CreateFileTool, # Used by GenerateLargeFileTool internally, but not directly called by test logic
    AppendToFileTool, # Used by GenerateLargeFileTool internally, but not directly called by test logic
    ReadFileTool,
    ensure_safe_path,
    SAFE_BASE_DIR # Assuming this might be used by ensure_safe_path if not mocked
)
# Assuming create_llm might be in mindlink.tools.file_tools if it's a common utility there,
# or adjust if it's elsewhere. The tool code shows 'from mindlink.main import create_llm'.
# So, we'll patch 'mindlink.tools.file_tools.create_llm' because that's where GenerateLargeFileTool looks for it.
# Also, prepare to patch OpenRouterModel as a fallback.
from mindlink.models.llm import LLMInterface # For type hinting mock LLM

# Disable most logging for cleaner test output, can be enabled for debugging
logging.disable(logging.CRITICAL)


class TestGenerateLargeFileTool(unittest.TestCase):
    def setUp(self):
        self.test_dir = Path("test_generated_files_temp")
        self.test_dir.mkdir(parents=True, exist_ok=True)
        
        # Override SAFE_BASE_DIR for testing purposes to use a local temp directory
        self.original_safe_base_dir = SAFE_BASE_DIR
        global SAFE_BASE_DIR
        SAFE_BASE_DIR = self.test_dir.resolve()

        self.test_file_name = "large_file_output.txt"
        self.test_file_path = str(self.test_dir / self.test_file_name) # Will be made safe by tool

        # Clean up before each test
        if os.path.exists(self.test_file_path):
            os.remove(self.test_file_path)

    def tearDown(self):
        if os.path.exists(self.test_file_path):
            try:
                os.remove(self.test_file_path)
            except Exception as e:
                print(f"Error in tearDown removing {self.test_file_path}: {e}")
        
        # Clean up the temp directory
        if self.test_dir.exists():
            for item in self.test_dir.iterdir():
                item.unlink()
            self.test_dir.rmdir()
            
        # Restore original SAFE_BASE_DIR
        global SAFE_BASE_DIR
        SAFE_BASE_DIR = self.original_safe_base_dir


    @patch('mindlink.tools.file_tools.create_llm') # Primary mock target
    @patch('mindlink.tools.file_tools.OpenRouterModel') # Fallback mock target
    def test_successful_generation_mocked_llm(self, MockOpenRouterModel, MockCreateLLM):
        # Arrange
        mock_llm_instance = MagicMock(spec=LLMInterface)
        MockCreateLLM.return_value = mock_llm_instance # create_llm() will return our mock
        # If create_llm fails or is None, GenerateLargeFileTool might try OpenRouterModel()
        MockOpenRouterModel.return_value = mock_llm_instance 

        content_description = "Create a Python script with a main function and two helper functions."
        mock_code_chunks = [
            "# Chunk 1: Main function definition\\nprint('Hello from main')\\n",
            "def helper_one():\\n    print('Helper one reporting')\\n",
            "def helper_two():\\n    print('Helper two reporting')\\n# End of script\\n"
        ]
        mock_llm_instance.generate.side_effect = mock_code_chunks

        tool = GenerateLargeFileTool() # Uses create_llm internally by default

        # Act
        result = tool.execute(
            path=self.test_file_name, # Tool will use ensure_safe_path
            content_description=content_description,
            max_chunks=3,
            context_carryover_lines=2 # Small carryover for easier assertion
        )

        # Assert
        self.assertEqual(result['status'], 'success', f"Tool execution failed: {result.get('observation')}")
        self.assertEqual(mock_llm_instance.generate.call_count, len(mock_code_chunks))
        
        # Context Carryover Check
        # Call 1 (chunk 0): No context expected beyond base prompt
        first_call_args = mock_llm_instance.generate.call_args_list[0]
        user_prompt_1 = first_call_args.kwargs.get('user_prompt', first_call_args.args[1] if len(first_call_args.args) > 1 else "")
        self.assertNotIn("The last", user_prompt_1, "First prompt should not have context carryover section.")

        # Call 2 (chunk 1): Expect context from chunk 0
        if len(mock_code_chunks) > 1:
            second_call_args = mock_llm_instance.generate.call_args_list[1]
            user_prompt_2 = second_call_args.kwargs.get('user_prompt', second_call_args.args[1] if len(second_call_args.args) > 1 else "")
            self.assertIn("The last", user_prompt_2, "Second prompt should have context carryover section.")
            # Ensure the context is from the end of chunk 1
            # chunk_0_lines = mock_code_chunks[0].splitlines()
            # expected_context_from_chunk0 = "\\n".join(chunk_0_lines[-2:]) # Last 2 lines
            # self.assertIn(expected_context_from_chunk0, user_prompt_2)
            # More robust check: check for some unique text from end of chunk 0
            self.assertIn("print('Hello from main')", user_prompt_2)


        # Verify file content
        self.assertTrue(os.path.exists(self.test_file_path))
        with open(self.test_file_path, 'r', encoding='utf-8') as f:
            file_content = f.read()
        
        expected_full_content = "".join(mock_code_chunks)
        self.assertEqual(file_content, expected_full_content)
        
        self.assertEqual(result.get('result', {}).get('chunks_written'), len(mock_code_chunks))
        self.assertEqual(result.get('result', {}).get('file_path'), self.test_file_path)


    @patch('mindlink.tools.file_tools.create_llm')
    @patch('mindlink.tools.file_tools.OpenRouterModel')
    def test_stops_at_max_chunks(self, MockOpenRouterModel, MockCreateLLM):
        mock_llm_instance = MagicMock(spec=LLMInterface)
        MockCreateLLM.return_value = mock_llm_instance
        MockOpenRouterModel.return_value = mock_llm_instance

        mock_code_chunks = ["chunk1\n", "chunk2\n", "chunk3\n", "chunk4\n", "chunk5\n"]
        mock_llm_instance.generate.side_effect = mock_code_chunks
        
        tool = GenerateLargeFileTool()
        max_chunks_to_test = 2
        result = tool.execute(
            path=self.test_file_name,
            content_description="Test max_chunks",
            max_chunks=max_chunks_to_test 
        )

        self.assertEqual(result['status'], 'success')
        self.assertEqual(mock_llm_instance.generate.call_count, max_chunks_to_test)
        
        with open(self.test_file_path, 'r', encoding='utf-8') as f:
            file_content = f.read()
        self.assertEqual(file_content, "".join(mock_code_chunks[:max_chunks_to_test]))
        self.assertEqual(result.get('result', {}).get('chunks_written'), max_chunks_to_test)

    @patch('mindlink.tools.file_tools.create_llm')
    @patch('mindlink.tools.file_tools.OpenRouterModel')
    def test_stops_at_target_lines(self, MockOpenRouterModel, MockCreateLLM):
        mock_llm_instance = MagicMock(spec=LLMInterface)
        MockCreateLLM.return_value = mock_llm_instance
        MockOpenRouterModel.return_value = mock_llm_instance

        mock_code_chunks = [
            "line1\nline2\n",  # 2 lines
            "line3\nline4\nline5\n", # 3 lines (total 5)
            "line6\nline7\n" # 2 lines (total 7)
        ]
        mock_llm_instance.generate.side_effect = mock_code_chunks
        
        tool = GenerateLargeFileTool()
        target_lines = 4
        result = tool.execute(
            path=self.test_file_name,
            content_description="Test target_line_count",
            target_line_count=target_lines,
            max_chunks=5 # Ensure max_chunks doesn't interfere
        )

        self.assertEqual(result['status'], 'success')
        # LLM should be called for chunk1 (2 lines) and chunk2 (3 lines, total 5 >= 4)
        self.assertEqual(mock_llm_instance.generate.call_count, 2) 
        
        with open(self.test_file_path, 'r', encoding='utf-8') as f:
            file_content = f.read()
        self.assertEqual(file_content, "".join(mock_code_chunks[:2]))
        self.assertEqual(result.get('result', {}).get('chunks_written'), 2)
        self.assertTrue(result.get('result', {}).get('lines_written') >= target_lines)
        self.assertTrue(result.get('result', {}).get('target_lines_met'))


    @patch('mindlink.tools.file_tools.create_llm')
    @patch('mindlink.tools.file_tools.OpenRouterModel')
    def test_stops_on_llm_empty_response(self, MockOpenRouterModel, MockCreateLLM):
        mock_llm_instance = MagicMock(spec=LLMInterface)
        MockCreateLLM.return_value = mock_llm_instance
        MockOpenRouterModel.return_value = mock_llm_instance

        mock_code_chunks = ["chunk1_code\n", "", "chunk3_code\n"] # Empty response for 2nd chunk
        mock_llm_instance.generate.side_effect = mock_code_chunks
        
        tool = GenerateLargeFileTool()
        result = tool.execute(
            path=self.test_file_name,
            content_description="Test empty LLM response",
            max_chunks=3
        )

        self.assertEqual(result['status'], 'success')
        # LLM called for chunk1, then for chunk2 (which is empty), then stops.
        self.assertEqual(mock_llm_instance.generate.call_count, 2) 
        
        with open(self.test_file_path, 'r', encoding='utf-8') as f:
            file_content = f.read()
        # Only chunk1 should be written, and it should have a newline.
        self.assertEqual(file_content, "chunk1_code\n") 
        self.assertEqual(result.get('result', {}).get('chunks_written'), 1) # Only non-empty chunks count towards this


    @patch('mindlink.tools.file_tools.create_llm')
    @patch('mindlink.tools.file_tools.OpenRouterModel')
    def test_handles_llm_exception(self, MockOpenRouterModel, MockCreateLLM):
        mock_llm_instance = MagicMock(spec=LLMInterface)
        MockCreateLLM.return_value = mock_llm_instance
        MockOpenRouterModel.return_value = mock_llm_instance

        # LLM raises exception on the second call
        mock_llm_instance.generate.side_effect = [
            "chunk1_code\n", 
            RuntimeError("LLM simulation error")
        ]
        
        tool = GenerateLargeFileTool()
        result = tool.execute(
            path=self.test_file_name,
            content_description="Test LLM exception",
            max_chunks=3
        )

        self.assertEqual(result['status'], 'error')
        self.assertIn("Error generating content for chunk 2", result['observation'])
        self.assertIn("LLM simulation error", result['error'])
        
        # LLM should have been called for chunk1 and then failed on chunk2
        self.assertEqual(mock_llm_instance.generate.call_count, 2)
        
        # File might contain chunk1 content
        self.assertTrue(os.path.exists(self.test_file_path))
        with open(self.test_file_path, 'r', encoding='utf-8') as f:
            file_content = f.read()
        self.assertEqual(file_content, "chunk1_code\n")

    @patch('mindlink.tools.file_tools.create_llm')
    @patch('mindlink.tools.file_tools.OpenRouterModel')
    def test_content_description_from_prefixed_file_path(self, MockOpenRouterModel, MockCreateLLM):
        mock_llm_instance = MagicMock(spec=LLMInterface)
        MockCreateLLM.return_value = mock_llm_instance
        MockOpenRouterModel.return_value = mock_llm_instance

        detailed_description_text = "This is a detailed description from a file.\\nIt has multiple lines.\\nIt specifies a complex Python script."
        desc_file_path = self.test_dir / "temp_desc_for_large_file_prefixed.txt"
        with open(desc_file_path, 'w', encoding='utf-8') as f:
            f.write(detailed_description_text)

        mock_code_chunk = "print('Content based on file description')\\n"
        mock_llm_instance.generate.return_value = mock_code_chunk

        tool = GenerateLargeFileTool()
        result = tool.execute(
            path=self.test_file_name,
            content_description="file://" + str(desc_file_path.resolve()), # Use resolved absolute path with prefix
            max_chunks=1
        )

        self.assertEqual(result['status'], 'success', f"Tool execution failed: {result.get('observation')}")
        self.assertTrue(mock_llm_instance.generate.called, "LLM generate method should have been called.")
        
        # Check that the LLM prompt contained the content from the description file
        call_args = mock_llm_instance.generate.call_args_list[0]
        user_prompt = call_args.kwargs.get('user_prompt', call_args.args[1] if len(call_args.args) > 1 else "")
        self.assertIn(detailed_description_text, user_prompt, "LLM prompt should contain the description from the file.")
        self.assertNotIn("file://", user_prompt, "LLM prompt should not contain the file:// prefix literally.")

        self.assertTrue(os.path.exists(self.test_file_path))
        with open(self.test_file_path, 'r', encoding='utf-8') as f:
            file_content = f.read()
        self.assertEqual(file_content, mock_code_chunk)

        if os.path.exists(desc_file_path):
            os.remove(desc_file_path)

    @patch('mindlink.tools.file_tools.create_llm')
    @patch('mindlink.tools.file_tools.OpenRouterModel')
    @patch('mindlink.tools.file_tools.logger') # Mock the logger for this specific test
    def test_content_description_from_direct_file_path_with_warning(self, mock_logger, MockOpenRouterModel, MockCreateLLM):
        mock_llm_instance = MagicMock(spec=LLMInterface)
        MockCreateLLM.return_value = mock_llm_instance
        MockOpenRouterModel.return_value = mock_llm_instance

        detailed_description_text = "Direct path description.\\nContent for the LLM."
        # Create description file within the SAFE_BASE_DIR (which is self.test_dir for tests)
        desc_file_name = "temp_desc_direct.txt"
        desc_file_path = self.test_dir / desc_file_name 
        with open(desc_file_path, 'w', encoding='utf-8') as f:
            f.write(detailed_description_text)

        mock_code_chunk = "print('Content from direct path description')\\n"
        mock_llm_instance.generate.return_value = mock_code_chunk

        tool = GenerateLargeFileTool()
        
        # Execute with the relative path string that matches the file created in SAFE_BASE_DIR
        result = tool.execute(
            path=self.test_file_name,
            content_description=desc_file_name, # Relative path, ensure_safe_path will resolve it under SAFE_BASE_DIR
            max_chunks=1
        )

        self.assertEqual(result['status'], 'success', f"Tool execution failed: {result.get('observation')}")
        self.assertTrue(mock_llm_instance.generate.called)
        
        call_args = mock_llm_instance.generate.call_args_list[0]
        user_prompt = call_args.kwargs.get('user_prompt', call_args.args[1] if len(call_args.args) > 1 else "")
        self.assertIn(detailed_description_text, user_prompt)
        
        # Check for the warning log
        warning_logged = False
        for log_call in mock_logger.warning.call_args_list:
            if f"content_description '{desc_file_name}' matches an existing file path" in log_call.args[0] and \
               "but 'file://' prefix was not used" in log_call.args[0]:
                warning_logged = True
                break
        self.assertTrue(warning_logged, "Warning for using direct path as content_description was not logged.")


        self.assertTrue(os.path.exists(self.test_file_path))
        with open(self.test_file_path, 'r', encoding='utf-8') as f:
            file_content = f.read()
        self.assertEqual(file_content, mock_code_chunk)

        if os.path.exists(desc_file_path):
            os.remove(desc_file_path)


if __name__ == '__main__':
    unittest.main()
