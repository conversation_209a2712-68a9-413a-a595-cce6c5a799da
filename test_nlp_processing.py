import time
import json
from typing import Dict, Any, <PERSON>, Tuple, Optional
from mindlink.agent import <PERSON><PERSON>
from mindlink.schemas.mindlink import Mind<PERSON>inkRe<PERSON>, Action
from mock_llm import <PERSON><PERSON><PERSON><PERSON>

def create_test_agent():
    """Create a test agent with a mock LLM."""
    # Create a mock LLM with test responses
    mock_llm = MockLLM(model_name="test-model")
    
    # Set up test responses for different tools using only available tools
    test_responses = [
        # Response for file creation
        '{"action": {"tool_name": "create_file", "parameters": {"path": "test.txt", "content": "Hello World"}}, "reasoning": "User wants to create a new file with specific content"}',
        # Response for listing files
        '{"action": {"tool_name": "list_files", "parameters": {"directory": "."}}, "reasoning": "User wants to list files in the current directory"}',
        # Response for searching in files (using search_in_files instead of search_files)
        '{"action": {"tool_name": "search_in_files", "parameters": {"query": "test", "file_pattern": "*.py"}}, "reasoning": "User wants to search for text in Python files"}',
        # Response for reading a file (using read_file instead of get_time)
        '{"action": {"tool_name": "read_file", "parameters": {"path": "requirements.txt"}}, "reasoning": "User wants to check the project dependencies"}',
        # Response for running code (using run_code instead of generate_code)
        '{"action": {"tool_name": "run_code", "parameters": {"code": "for i in range(1, 11): print(i)", "language": "python"}}, "reasoning": "User wants to run a Python script that prints numbers 1 to 10"}'
    ]
    
    mock_llm.set_responses(test_responses)
    
    # Create a simple system prompt template that matches what AgentOS expects
    system_prompt_template = """You are a helpful AI assistant. Your task is to understand user requests and convert them into appropriate tool calls.
    
    {tool_descriptions}
    
    Respond with a JSON object in this format:
    {
        "action": {
            "tool_name": "tool_name",
            "parameters": {
                "param1": "value1",
                "param2": "value2"
            }
        },
        "reasoning": "Your reasoning here"
    }"""
    
    # Create the agent
    agent = AgentOS(
        llm=mock_llm, 
        system_prompt_template=system_prompt_template,
        max_steps=5,
        max_parsing_retries=3,
        retry_delay=1.0
    )
    
    # Manually set up the translator_llm for testing
    agent.translator_llm = mock_llm
    
    return agent

def run_performance_test():
    """Run a series of tests to evaluate the NLP processing capabilities."""
    try:
        print("Creating test agent...")
        agent = create_test_agent()
        print("Agent created successfully")
        
        # Debug: Print available tools
        from mindlink.tools.base import tool_registry
        print("\nAvailable tools:", list(tool_registry.keys()))
        
    except Exception as e:
        print(f"Failed to create agent: {str(e)}")
        import traceback
        traceback.print_exc()
        return
    
    test_queries = [
        "Create a new file called test.txt with 'Hello World' in it",
        "List all files in the current directory",
        "Search for the word 'test' in all Python files",
        "Read the contents of requirements.txt",
        "Run a Python script that prints numbers 1 to 10"
    ]
    
    results = []
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n--- Test {i}: {query} ---")
        start_time = time.time()
        
        try:
            print(f"\n--- Processing query: {query} ---")
            print("Calling _translate_nl_to_tool_call...")
            try:
                tool_call = agent._translate_nl_to_tool_call(query)
                print(f"_translate_nl_to_tool_call returned: {tool_call}")
                
                # Debug: Check translator_llm
                translator_llm = getattr(agent, 'translator_llm', None)
                print(f"Translator LLM: {translator_llm}")
                if translator_llm:
                    print(f"Translator LLM responses remaining: {len(translator_llm.responses) if hasattr(translator_llm, 'responses') else 'N/A'}")
            except Exception as e:
                print(f"Error in _translate_nl_to_tool_call: {str(e)}")
                import traceback
                traceback.print_exc()
                tool_call = None
            end_time = time.time()
            
            # Evaluate the result
            if tool_call and hasattr(tool_call, 'action') and hasattr(tool_call.action, 'tool_name'):
                status = "SUCCESS"
                tool_used = tool_call.action.tool_name
                params = getattr(tool_call.action, 'parameters', {})
                reasoning = getattr(tool_call, 'reasoning', 'No reasoning provided')
            else:
                status = "FAILED: No tool call generated"
                tool_used = "N/A"
                params = {}
                reasoning = "N/A"
                
            # Store results
            result = {
                "query": query,
                "status": status,
                "tool_used": tool_used,
                "execution_time": end_time - start_time,
                "parameters": params,
                "reasoning": reasoning
            }
            results.append(result)
            
            # Print test results
            print(f"Status: {status}")
            print(f"Tool: {tool_used}")
            print(f"Parameters: {params}")
            print(f"Reasoning: {reasoning}")
            print(f"Execution time: {result['execution_time']:.4f} seconds")
            
        except Exception as e:
            end_time = time.time()
            error_msg = f"ERROR: {str(e)}"
            print(error_msg)
            import traceback
            traceback.print_exc()
            results.append({
                "query": query,
                "status": error_msg,
                "execution_time": end_time - start_time
            })
    
    # Print summary
    print("\n=== Test Summary ===")
    success_count = sum(1 for r in results if r['status'] == 'SUCCESS')
    print(f"Success rate: {success_count}/{len(test_queries)} ({(success_count/len(test_queries))*100:.1f}%)")
    
    avg_time = sum(r['execution_time'] for r in results) / len(results)
    print(f"Average execution time: {avg_time:.4f} seconds")
    
    # Print detailed results
    print("\nDetailed Results:")
    for i, result in enumerate(results, 1):
        print(f"\nTest {i}:")
        print(f"Query: {result['query']}")
        print(f"Status: {result['status']}")
        if result['status'] == 'SUCCESS':
            print(f"Tool: {result['tool_used']}")
            print(f"Parameters: {result['parameters']}")
            print(f"Reasoning: {result['reasoning']}")
        print(f"Time: {result['execution_time']:.4f} seconds")

if __name__ == "__main__":
    run_performance_test()
