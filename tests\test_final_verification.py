"""
Final verification test for MindLink Agent Core.

This test focuses on verifying that the circular import issue is resolved
and that the library is fully operational.
"""

import pytest
import os
import sys
import tempfile
import shutil
import json

# Force reload of all relevant modules to ensure we're testing the current state
for module_name in list(sys.modules.keys()):
    if module_name.startswith('mindlink.tools.'):
        del sys.modules[module_name]
if 'mindlink.tools' in sys.modules:
    del sys.modules['mindlink.tools']

# Import all tools to verify no circular imports
from mindlink.tools import (
    CreateFileTool,
    ReadFileTool,
    ListFilesTool,
    RunShellCommandTool,
    InsertASTNodeTool,
    SemanticSuggestTool,
    RunCodeTool,
    SnapshotTool,
    GenerateGraphTool,
    HoverDocTool,
    GenerateCallGraphTool
)
from mindlink.tools.file_tools import (
    TransactionManager,
    create_file,
    read_file,
    list_files,
    path_exists
)
from mindlink.tools.base import tool_registry


def test_circular_import_resolution():
    """Test that the circular import issue is resolved."""
    # If we got here, the imports succeeded
    assert CreateFileTool is not None
    assert ReadFileTool is not None
    assert ListFilesTool is not None
    assert RunShellCommandTool is not None
    assert InsertASTNodeTool is not None
    assert SemanticSuggestTool is not None
    assert RunCodeTool is not None
    assert SnapshotTool is not None
    assert GenerateGraphTool is not None
    assert HoverDocTool is not None
    assert GenerateCallGraphTool is not None
    
    # Check that the tools are registered
    assert "create_file" in tool_registry
    assert "read_file" in tool_registry
    assert "list_files" in tool_registry
    assert "generate_call_graph" in tool_registry
    
    print("All tools imported successfully and registered")


def test_file_operations():
    """Test basic file operations."""
    # Create a temporary directory
    temp_dir = tempfile.mkdtemp()
    try:
        # Create a file
        file_path = os.path.join(temp_dir, "test.txt")
        content = "Test content"
        create_file(file_path, content)
        
        # Verify the file was created
        assert path_exists(file_path)
        
        # Read the file
        read_content = read_file(file_path)
        assert read_content == content
        
        # List files
        files = list_files(temp_dir)
        assert "test.txt" in files
        
        print("Basic file operations work correctly")
    finally:
        # Clean up
        shutil.rmtree(temp_dir)


def test_transaction_manager():
    """Test the TransactionManager."""
    # Create a temporary directory
    temp_dir = tempfile.mkdtemp()
    try:
        # Create files with a transaction
        with TransactionManager():
            file1_path = os.path.join(temp_dir, "file1.txt")
            create_file(file1_path, "File 1 content")
            
            file2_path = os.path.join(temp_dir, "file2.txt")
            create_file(file2_path, "File 2 content")
        
        # Verify the files were created
        assert path_exists(file1_path)
        assert path_exists(file2_path)
        assert read_file(file1_path) == "File 1 content"
        assert read_file(file2_path) == "File 2 content"
        
        # Test transaction rollback
        try:
            with TransactionManager():
                file3_path = os.path.join(temp_dir, "file3.txt")
                create_file(file3_path, "File 3 content")
                
                # Raise an exception to trigger rollback
                raise ValueError("Test exception")
        except ValueError:
            # Expected exception
            pass
        
        # Verify file3 was not created (rolled back)
        assert not path_exists(file3_path)
        
        print("TransactionManager works correctly")
    finally:
        # Clean up
        shutil.rmtree(temp_dir)


def test_tool_classes():
    """Test the tool classes."""
    # Create a temporary directory
    temp_dir = tempfile.mkdtemp()
    try:
        # Create a file using CreateFileTool
        create_tool = CreateFileTool()
        file_path = os.path.join(temp_dir, "tool_test.txt")
        result = create_tool.execute(path=file_path, content="Created by tool")
        assert result["status"] == "success"
        
        # Read the file using ReadFileTool
        read_tool = ReadFileTool()
        result = read_tool.execute(path=file_path)
        assert result["status"] == "success"
        assert result["observation"] == "Created by tool"
        
        # List files using ListFilesTool
        list_tool = ListFilesTool()
        result = list_tool.execute(directory=temp_dir)
        assert result["status"] == "success"
        assert "tool_test.txt" in result["observation"]
        
        print("Tool classes work correctly")
    finally:
        # Clean up
        shutil.rmtree(temp_dir)


def test_cross_module_integration():
    """Test integration between different modules."""
    # Create a temporary directory
    temp_dir = tempfile.mkdtemp()
    try:
        # Create a Python file for analysis
        py_file_path = os.path.join(temp_dir, "module.py")
        py_content = """
def function_a():
    function_b()
    function_c()
    return True

def function_b():
    return "result"

def function_c():
    return 42
"""
        create_file(py_file_path, py_content)
        
        # Use GenerateCallGraphTool to analyze the file
        graph_tool = GenerateCallGraphTool()
        result = graph_tool.execute(path=py_file_path)
        assert result["status"] == "success"
        
        # Parse the call graph
        call_graph = json.loads(result["observation"])
        assert "function_a" in call_graph
        assert "function_b" in call_graph["function_a"]
        assert "function_c" in call_graph["function_a"]
        
        print("Cross-module integration works correctly")
    finally:
        # Clean up
        shutil.rmtree(temp_dir)


if __name__ == "__main__":
    pytest.main(["-xvs", __file__])
