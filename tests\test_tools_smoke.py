import os
import pytest
from mindlink.tools.semantic_tools import SemanticSuggestTool
from mindlink.tools.sandbox_tools import RunCodeTool, SnapshotTool
from mindlink.tools.knowledge_tools import GenerateGraphTool, HoverDocTool

@pytest.fixture(autouse=True)
def temp_cwd(tmp_path, monkeypatch):
    # Run all tests in isolated temp dir
    monkeypatch.chdir(tmp_path)
    return tmp_path

@pytest.fixture
def sample_py(tmp_path):
    # Create a sample Python file with a docstring
    p = tmp_path / "sample.py"
    p.write_text('def foo():\n    """bar doc"""\n    return 42\n')
    return str(p)


def test_semantic_suggest(sample_py):
    tool = SemanticSuggestTool()
    resp = tool.execute(file_path=sample_py, line=3, column=0)
    assert resp["status"] == "success"
    assert isinstance(resp["observation"], str)


def test_run_code():
    tool = RunCodeTool()
    resp = tool.execute(code="print('hello')", timeout=5)
    assert resp["status"] == "success"
    assert "hello" in resp["observation"]


def test_snapshot(temp_cwd):
    tool = SnapshotTool()
    resp = tool.execute(name="smoketest", history=[{"step":1}])
    assert resp["status"] == "success"
    snap_dir = temp_cwd / ".mindlink_snapshots" / "smoketest"
    assert os.path.isdir(snap_dir)
    assert os.path.isfile(os.path.join(snap_dir, "history.json"))
    # Check archive exists
    archive = os.path.join(snap_dir, "smoketest.zip")
    assert os.path.isfile(archive)


def test_generate_graph(sample_py):
    tool = GenerateGraphTool()
    resp = tool.execute(root=".")
    assert resp["status"] == "success"
    assert resp["observation"].strip().startswith("digraph")


def test_hover_doc(sample_py):
    tool = HoverDocTool()
    resp = tool.execute(file_path=sample_py, line=2, column=0)
    assert resp["status"] == "success"
    obs = resp["observation"]
    assert "foo" in obs and "bar doc" in obs
