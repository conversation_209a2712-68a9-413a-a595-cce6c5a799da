import unittest
import os
from pathlib import Path

from mindlink.tools.file_tools import CreateFileTool, AppendToFileTool, ReadFileTool, ensure_safe_path, SAFE_BASE_DIR

# 1. Prepare Content
FULL_TEST_CODE = """# Start of a large script
# This is a multi-line Python script for testing.

def function_one(param1, param2):
    # This is a comment inside function_one
    result = param1 + param2
    # Check a condition
    if result > 100:
        print(f"Result is large: {result}")
    elif result < 0:
        print(f"Result is negative: {result}")
    else:
        # Printing a string with "quotes" and newlines\\n
        print(f"Result is moderate: {result}, details: \\"Okay\\"")
    return result

class MyClass:
    # Docstring for MyClass
    '''This class demonstrates basic structure and methods.'''
    
    class_variable = "shared among instances"

    def __init__(self, name: str):
        # Constructor with type hint
        self.name: str = name
        self._internal_data: list = [] # A private-like list
        self.settings: dict = {"option1": True, "option2": "value"}

    def add_item(self, item: any) -> int:
        '''Appends an item with escaping: "quotes" and newlines\\nand tabs\\t.'''
        # Example of a long line that might need careful handling if it were part of a chunk boundary, though not an issue here.
        self._internal_data.append(item)
        print(f"Item '{item}' added to {self.name}'s data. Current length: {len(self._internal_data)}")
        return len(self._internal_data)

    def get_data(self) -> list:
        # Returns a copy of the internal data
        return list(self._internal_data)

# More functions and code...
def function_two(data_list: list):
    # Processes a list
    for i, item in enumerate(data_list):
        print(f"Processing item {i}: {item}")
        if isinstance(item, (int, float)) and item % 2 == 0:
            # A nested comment
            function_one(item, i) # Calling another function

# Example of a loop and list comprehension
squares = [x*x for x in range(10) if x % 2 == 0]
print(f"Even squares: {squares}")

# Multi-line string for testing
MULTI_LINE_STR = '''
This is a
multi-line string
used within the script.
It also has "quotes".
'''

# End of the script
print("Script finished successfully.")
"""

# Split into 3 chunks
# Find split points (e.g., at specific line breaks)
lines = FULL_TEST_CODE.splitlines(True) # Keep line endings
num_lines = len(lines)
split1_idx = num_lines // 3
split2_idx = (num_lines * 2) // 3

CHUNK_1 = "".join(lines[:split1_idx])
CHUNK_2 = "".join(lines[split1_idx:split2_idx])
CHUNK_3 = "".join(lines[split2_idx:])


class TestChunkedFileCreation(unittest.TestCase):

    def setUp(self):
        # 2. Define Target File Path
        # Ensure the path is safe and within a controlled directory if needed.
        # For this test, using a relative path. If SAFE_BASE_DIR is enforced by tools,
        # it might redirect. Using ensure_safe_path to be explicit about where it goes.
        self.target_file_name = "temp_large_generated_file.py"
        # self.target_file_path = str(Path(SAFE_BASE_DIR) / self.target_file_name)
        # For local testing without assuming D:/3, let's use a relative path managed by ensure_safe_path
        # If SAFE_BASE_DIR is "D:/3", this will become "D:/3/temp_large_generated_file.py"
        # If SAFE_BASE_DIR is something else (e.g. relative ./sandbox), it will be ./sandbox/temp...
        # For the worker, it's safer to assume a relative path that might be namespaced by SAFE_BASE_DIR
        self.target_file_path = ensure_safe_path(self.target_file_name) 
        
        # Ensure the file does not exist before the test
        if os.path.exists(self.target_file_path):
            os.remove(self.target_file_path)

    def tearDown(self):
        # 5. Cleanup
        if os.path.exists(self.target_file_path):
            try:
                os.remove(self.target_file_path)
            except Exception as e:
                print(f"Error cleaning up file {self.target_file_path}: {e}")

    def test_create_and_append_chunks(self):
        # 3. Execute File Operations
        create_tool = CreateFileTool()
        append_tool = AppendToFileTool()
        read_tool = ReadFileTool()

        # Create file with chunk1
        create_result = create_tool.execute(path=self.target_file_path, content=CHUNK_1)
        self.assertEqual(create_result['status'], 'success', f"CreateFileTool failed: {create_result.get('observation')}")
        self.assertTrue(os.path.exists(self.target_file_path), "File should be created after CreateFileTool.")
        
        # Verify content after first chunk
        with open(self.target_file_path, 'r', encoding='utf-8') as f:
            content_after_chunk1 = f.read()
        self.assertEqual(content_after_chunk1, CHUNK_1, "Content after CreateFileTool does not match chunk1.")

        # Append chunk2
        append_result_2 = append_tool.execute(path=self.target_file_path, content_chunk=CHUNK_2)
        self.assertEqual(append_result_2['status'], 'success', f"AppendToFileTool (chunk2) failed: {append_result_2.get('observation')}")
        
        # Verify content after second chunk
        with open(self.target_file_path, 'r', encoding='utf-8') as f:
            content_after_chunk2 = f.read()
        self.assertEqual(content_after_chunk2, CHUNK_1 + CHUNK_2, "Content after appending chunk2 is incorrect.")

        # Append chunk3
        append_result_3 = append_tool.execute(path=self.target_file_path, content_chunk=CHUNK_3)
        self.assertEqual(append_result_3['status'], 'success', f"AppendToFileTool (chunk3) failed: {append_result_3.get('observation')}")

        # 4. Verify Content
        read_result = read_tool.execute(path=self.target_file_path)
        self.assertEqual(read_result['status'], 'success', f"ReadFileTool failed: {read_result.get('observation')}")
        
        # Compare the full content
        # Normalizing line endings for comparison, as git might change them on checkout
        expected_content_normalized = FULL_TEST_CODE.replace('\\r\\n', '\\n')
        read_content_normalized = read_result['observation'].replace('\\r\\n', '\\n')
        
        self.assertEqual(read_content_normalized, expected_content_normalized, "Final content does not match original full code.")
        
        # Verify file size from ReadFileTool's observation if possible, or directly
        self.assertEqual(len(read_content_normalized), len(expected_content_normalized), "Length of final content mismatch.")

if __name__ == '__main__':
    unittest.main()
