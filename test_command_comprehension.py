#!/usr/bin/env python3
"""
Test script for the Command Comprehension Engine.

This script demonstrates the intelligent command analysis using Microsoft's phi-4-reasoning model
via OpenRouter API.
"""

import os
import sys
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add the project root to the path
project_root = Path(__file__).parent
sys.path.append(str(project_root))

try:
    from mindlink.command_comprehension import CommandComprehensionEngine, ConfidenceLevel
except ImportError as e:
    print(f"Failed to import command comprehension module: {e}")
    sys.exit(1)


def test_command_comprehension():
    """Test the command comprehension engine with various commands."""
    
    # Get API key
    api_key = os.getenv('OPENROUTER_API_KEY')
    if not api_key:
        print("Error: OPENROUTER_API_KEY environment variable not set.")
        print("Please set your OpenRouter API key in the .env file or as an environment variable.")
        return
    
    # Initialize the engine
    try:
        engine = CommandComprehensionEngine(api_key)
        print("✅ Command Comprehension Engine initialized successfully!")
        print(f"Using model: {engine.model_name}")
        print("-" * 60)
    except Exception as e:
        print(f"❌ Failed to initialize engine: {e}")
        return
    
    # Test commands
    test_commands = [
        "Create a Python file called hello.py with a simple hello world function",
        "Search for information about machine learning algorithms",
        "Analyze the data in sales_report.csv and generate a summary",
        "Run the command 'pip install numpy' in the terminal",
        "What is the weather like today?",
        "Delete the old backup files from last month",
        "Generate a web scraper for extracting product prices",
        "Help me understand how neural networks work",
        "Create a database schema for a blog application",
        "Fix the bug in my code",  # Ambiguous command
        "Do something"  # Very ambiguous command
    ]
    
    for i, command in enumerate(test_commands, 1):
        print(f"\n🔍 Test {i}: Analyzing command")
        print(f"Command: '{command}'")
        print("-" * 40)
        
        try:
            # Analyze the command
            analysis = engine.analyze_command(command)
            
            # Display results
            print(f"Intent: {analysis.intent}")
            print(f"Confidence: {analysis.confidence.value}")
            
            if analysis.entities:
                print("Entities:")
                for key, value in analysis.entities.items():
                    print(f"  - {key}: {value}")
            else:
                print("Entities: None detected")
            
            if analysis.clarification_needed:
                print("⚠️  Clarification needed!")
                if analysis.ambiguous_parts:
                    print("Ambiguous parts:")
                    for part in analysis.ambiguous_parts:
                        print(f"  - {part}")
                
                # Generate clarification question
                clarification = engine.generate_clarification_question(analysis)
                if clarification:
                    print(f"Suggested clarification: {clarification}")
            else:
                print("✅ Command understood clearly")
                
        except Exception as e:
            print(f"❌ Error analyzing command: {e}")
        
        print("-" * 40)


def test_integration_with_agent():
    """Test the integration with the MindLink Agent."""
    
    print("\n" + "=" * 60)
    print("🤖 Testing Integration with MindLink Agent")
    print("=" * 60)
    
    try:
        from mindlink.agent import AgentOS
        from mindlink.models.openrouter import OpenRouterModel
        from mindlink.config import DEFAULT_SYSTEM_PROMPT
        
        # Get API key
        api_key = os.getenv('OPENROUTER_API_KEY')
        if not api_key:
            print("❌ No API key available for agent testing")
            return
        
        # Initialize agent with command comprehension enabled
        llm = OpenRouterModel(api_key=api_key, model_name="deepseek-r1")
        agent = AgentOS(
            llm=llm,
            system_prompt_template=DEFAULT_SYSTEM_PROMPT,
            max_steps=5,
            enable_command_comprehension=True
        )
        
        print("✅ Agent initialized with command comprehension enabled")
        
        # Test command preprocessing
        test_goal = "Create a simple Python calculator that can add and subtract numbers"
        print(f"\nOriginal goal: '{test_goal}'")
        
        # Test the preprocessing method directly
        processed_goal = agent._preprocess_command(test_goal)
        print(f"Processed goal: '{processed_goal}'")
        
        if processed_goal != test_goal:
            print("✅ Command was enhanced with analysis information")
        else:
            print("ℹ️  Command was not modified (may indicate low confidence or no enhancement needed)")
            
    except ImportError as e:
        print(f"❌ Could not import agent components: {e}")
    except Exception as e:
        print(f"❌ Error testing agent integration: {e}")


if __name__ == "__main__":
    print("🧠 MindLink Command Comprehension Test")
    print("=" * 60)
    print("This script tests the intelligent command analysis system")
    print("using Microsoft's phi-4-reasoning model via OpenRouter API.")
    print("=" * 60)
    
    # Test the command comprehension engine
    test_command_comprehension()
    
    # Test integration with the agent
    test_integration_with_agent()
    
    print("\n" + "=" * 60)
    print("🎉 Testing completed!")
    print("=" * 60)