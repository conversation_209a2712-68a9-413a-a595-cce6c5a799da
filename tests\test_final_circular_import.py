"""
Final test for circular import resolution in MindLink Agent Core.

This test focuses specifically on verifying that the circular import issue
in file_tools.py has been fixed and the library can be imported correctly.
"""

import pytest
import sys
import os
import importlib


def test_direct_import_file_tools():
    """Test that file_tools.py can be imported directly without circular import errors."""
    try:
        # Force reload to ensure we're testing the current state
        if 'mindlink.tools.file_tools' in sys.modules:
            del sys.modules['mindlink.tools.file_tools']
        
        # Try to import the module
        import mindlink.tools.file_tools
        
        # If we get here, the import succeeded
        assert True
    except ImportError as e:
        # If we get an ImportError, the test fails
        assert False, f"ImportError when importing file_tools.py: {str(e)}"


def test_import_through_init():
    """Test that tools can be imported through the __init__.py file without circular import errors."""
    try:
        # Force reload to ensure we're testing the current state
        if 'mindlink.tools' in sys.modules:
            del sys.modules['mindlink.tools']
        
        # Try to import through __init__
        import mindlink.tools
        
        # Check that file tools are available
        assert hasattr(mindlink.tools, 'CreateFileTool')
        assert hasattr(mindlink.tools, 'ReadFileTool')
        assert hasattr(mindlink.tools, 'ListFilesTool')
    except ImportError as e:
        # If we get an ImportError, the test fails
        assert False, f"ImportError when importing through __init__: {str(e)}"


def test_import_all_tools():
    """Test that all tools can be imported without circular import errors."""
    try:
        # Force reload to ensure we're testing the current state
        for module_name in list(sys.modules.keys()):
            if module_name.startswith('mindlink.tools.'):
                del sys.modules[module_name]
        if 'mindlink.tools' in sys.modules:
            del sys.modules['mindlink.tools']
        
        # Try to import all tools
        from mindlink.tools import (
            CreateFileTool,
            ReadFileTool,
            ListFilesTool,
            RunShellCommandTool,
            InsertASTNodeTool,
            SemanticSuggestTool,
            RunCodeTool,
            SnapshotTool,
            GenerateGraphTool,
            HoverDocTool,
            GenerateCallGraphTool
        )
        
        # If we get here, the imports succeeded
        assert CreateFileTool is not None
        assert ReadFileTool is not None
        assert ListFilesTool is not None
        assert RunShellCommandTool is not None
        assert InsertASTNodeTool is not None
        assert SemanticSuggestTool is not None
        assert RunCodeTool is not None
        assert SnapshotTool is not None
        assert GenerateGraphTool is not None
        assert HoverDocTool is not None
        assert GenerateCallGraphTool is not None
    except ImportError as e:
        # If we get an ImportError, the test fails
        assert False, f"ImportError when importing all tools: {str(e)}"


def test_file_tools_functionality():
    """Test that file_tools.py functions work correctly."""
    try:
        # Force reload to ensure we're testing the current state
        if 'mindlink.tools.file_tools' in sys.modules:
            del sys.modules['mindlink.tools.file_tools']
        
        # Import the functions
        from mindlink.tools.file_tools import (
            TransactionManager,
            create_file,
            read_file,
            list_files,
            path_exists
        )
        
        # Create a temporary file
        import tempfile
        temp_dir = tempfile.mkdtemp()
        try:
            # Test create_file and read_file
            test_file = os.path.join(temp_dir, "test.txt")
            test_content = "Hello, world!"
            
            with TransactionManager():
                create_file(test_file, test_content)
                assert path_exists(test_file)
                content = read_file(test_file)
                assert content == test_content
            
            # Test list_files
            files = list_files(temp_dir)
            assert "test.txt" in files
            
        finally:
            # Clean up
            import shutil
            shutil.rmtree(temp_dir)
    except Exception as e:
        assert False, f"Error testing file_tools functionality: {str(e)}"


if __name__ == "__main__":
    pytest.main(["-xvs", __file__])
