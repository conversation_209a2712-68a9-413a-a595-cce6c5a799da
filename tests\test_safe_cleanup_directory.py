"""
Test for verifying the safe_cleanup_directory function's handling of permission errors.
"""
import os
import sys
import tempfile
import unittest
import shutil
import stat
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.DEBUG, 
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

# Add the parent directory to sys.path to import mindlink modules
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from mindlink.tools.file_tools import safe_cleanup_directory


class TestSafeCleanupDirectory(unittest.TestCase):
    """Test that safe_cleanup_directory correctly handles permissions."""
    
    def setUp(self):
        """Create a temporary directory for testing."""
        self.temp_dir = tempfile.mkdtemp()
        print(f"Created temp directory: {self.temp_dir}")
    
    def tearDown(self):
        """Clean up the temporary directory."""
        try:
            if os.path.exists(self.temp_dir):
                print(f"Cleaning up temp directory: {self.temp_dir}")
                shutil.rmtree(self.temp_dir)
        except Exception as e:
            print(f"Error during tearDown: {e}")
    
    def test_cleanup_with_permission_issues(self):
        """Test cleanup with permission issues."""
        # Create a nested directory structure
        nested_dir = os.path.join(self.temp_dir, "level1", "level2")
        os.makedirs(nested_dir)
        
        # Create some test files
        test_file1 = os.path.join(nested_dir, "test1.txt")
        test_file2 = os.path.join(nested_dir, "test2.txt")
        
        with open(test_file1, "w") as f:
            f.write("Test content 1")
        
        with open(test_file2, "w") as f:
            f.write("Test content 2")
        
        # Make a file read-only to simulate permission issues
        os.chmod(test_file1, stat.S_IREAD)
        
        # First verify the test files exist
        self.assertTrue(os.path.exists(test_file1), f"File {test_file1} should exist")
        self.assertTrue(os.path.exists(test_file2), f"File {test_file2} should exist")
        
        # Use safe_cleanup_directory to clean up the directory
        print(f"Calling safe_cleanup_directory on: {self.temp_dir}")
        result = safe_cleanup_directory(self.temp_dir, max_retries=2, recreate=True)
        
        # Check the result
        print(f"safe_cleanup_directory result: {result}")
        
        # The directory should still exist because recreate=True
        self.assertTrue(os.path.exists(self.temp_dir), 
                       f"Directory {self.temp_dir} should exist after cleanup with recreate=True")
        
        # The nested directory structure should be gone
        self.assertFalse(os.path.exists(nested_dir), 
                        f"Nested directory {nested_dir} should have been removed")
    
    def test_cleanup_without_recreate(self):
        """Test cleanup without recreation."""
        # Create a simple file
        test_file = os.path.join(self.temp_dir, "test.txt")
        with open(test_file, "w") as f:
            f.write("Test content")
        
        # Use safe_cleanup_directory with recreate=False
        print(f"Calling safe_cleanup_directory with recreate=False on: {self.temp_dir}")
        result = safe_cleanup_directory(self.temp_dir, recreate=False)
        
        # Check the result
        print(f"safe_cleanup_directory result: {result}")
        
        # The directory should not exist anymore
        self.assertFalse(os.path.exists(self.temp_dir), 
                        f"Directory {self.temp_dir} should not exist after cleanup with recreate=False")


if __name__ == "__main__":
    unittest.main() 