2025-05-04 14:40:51,257 [INFO] [SUCCESS] All imports successful
2025-05-04 14:40:51,260 [ERROR] No API keys found. Set OPENAI_API_KEY or OPENROUTER_API_KEY environment variables.
2025-05-04 14:41:54,932 [INFO] [SUCCESS] All imports successful
2025-05-04 14:41:54,936 [INFO] Using OpenRouter provider with default API key
2025-05-04 14:41:54,938 [INFO] Created temporary test directory: C:\Users\<USER>\AppData\Local\Temp\tmpyp11ah5m
2025-05-04 14:41:54,938 [INFO] Starting test: Complex Project Generation
2025-05-04 14:41:54,939 [INFO] Starting complex project generation with goal: 
    Create a complete Python project for a Web scraper with async capabilities, result storage, and...
2025-05-04 14:41:54,940 [ERROR] Error in complex project generation: run_agent() got an unexpected keyword argument 'max_steps'
2025-05-04 14:41:54,942 [INFO] Test Complex Project Generation failed
2025-05-04 14:41:54,942 [INFO] Starting test: Adversarial Handling
2025-05-04 14:41:54,943 [INFO] Starting adversarial handling test with prompt: Create a multi-threaded application with potential race conditions but implement proper locks
2025-05-04 14:41:54,943 [ERROR] Error in adversarial handling: run_agent() got an unexpected keyword argument 'max_steps'
2025-05-04 14:41:54,944 [INFO] Test Adversarial Handling failed
2025-05-04 14:41:54,944 [INFO] Starting test: Concurrent Operations
2025-05-04 14:41:54,945 [INFO] Starting concurrent operations test with 3 tasks
2025-05-04 14:41:54,947 [INFO] Concurrent operations completed with success rate: 0.00
2025-05-04 14:41:54,948 [INFO] Test Concurrent Operations failed
2025-05-04 14:41:54,948 [INFO] Generating performance report...
2025-05-04 14:42:46,142 [INFO] [SUCCESS] All imports successful
2025-05-04 14:42:46,144 [INFO] Using OpenRouter provider with default API key
2025-05-04 14:42:46,145 [INFO] Created temporary test directory: C:\Users\<USER>\AppData\Local\Temp\tmpl91lkomd
2025-05-04 14:42:46,145 [INFO] Starting test: Complex Project Generation
2025-05-04 14:42:46,146 [INFO] Starting complex project generation with goal: 
    Create a complete Python project for a Data analysis pipeline with visualization, caching, and ...
2025-05-04 14:49:08,318 [ERROR] Project generation failed: Maximum number of steps (15) reached without completing the task.
2025-05-04 14:49:08,319 [INFO] Test Complex Project Generation failed
2025-05-04 14:49:08,320 [INFO] Starting test: Adversarial Handling
2025-05-04 14:49:08,320 [INFO] Starting adversarial handling test with prompt: Generate valid Python code with extremely deep nested control structures
2025-05-04 14:51:33,974 [INFO] Generating performance report...
