[
{
  "event_id": "3f8813c0-2437-4793-9d51-3f9afe8e0a1b",
  "timestamp": "2025-06-07T13:40:54.406408",
  "session_id": "95d58aff-62c0-4d16-8251-2f0b9659a745",
  "event_type": "user_input",
  "user_input": {
    "text": "Create a Python file that contains 500 lines of functional code.",
    "intent": "agent_goal"
  }
},

{
  "event_id": "354ddbcd-4a07-4bbe-b98f-ee0f6d4c1ad7",
  "timestamp": "2025-06-07T13:40:56.133729",
  "session_id": "95d58aff-62c0-4d16-8251-2f0b9659a745",
  "event_type": "llm_query",
  "llm_query": {
    "model": "Mistral Small 3.1 (OpenRouter)",
    "prompt_length": 201,
    "temperature": null,
    "max_tokens": null,
    "has_system_prompt": false,
    "tools_available": 0
  }
},

{
  "event_id": "bd319ca4-5baf-48a9-8524-aa5312ec113f",
  "timestamp": "2025-06-07T13:40:57.704494",
  "session_id": "95d58aff-62c0-4d16-8251-2f0b9659a745",
  "event_type": "error_occurred",
  "error_details": {
    "component": "LLMInterface",
    "severity": "CRITICAL",
    "message": "Generation Error: All LLM providers failed. Last error: 429 Client Error: Too Many Requests for url: https://openrouter.ai/api/v1/chat/completions",
    "has_stack_trace": true
  }
},

{
  "event_id": "b58376ed-f52c-41be-b9a7-4be2674f9a10",
  "timestamp": "2025-06-07T13:42:17.683444",
  "session_id": "95d58aff-62c0-4d16-8251-2f0b9659a745",
  "event_type": "error_occurred",
  "error_details": {
    "component": "LLMInterface",
    "severity": "CRITICAL",
    "message": "Generation Error: All LLM providers failed. Last error: OpenRouter API request timed out after 60 seconds. This may be due to network issues, server overload, or the request being too complex. Consider reducing the content size or trying again later.",
    "has_stack_trace": true
  }
},

{
  "event_id": "027fa2b3-2afd-4626-be0d-f58a7442ec48",
  "timestamp": "2025-06-07T13:42:21.447371",
  "session_id": "95d58aff-62c0-4d16-8251-2f0b9659a745",
  "event_type": "error_occurred",
  "error_details": {
    "component": "LLMInterface",
    "severity": "CRITICAL",
    "message": "Generation Error: All LLM providers failed. Last error: 429 Client Error: Too Many Requests for url: https://openrouter.ai/api/v1/chat/completions",
    "has_stack_trace": true
  }
},

{
  "event_id": "afc7d4cb-3de4-4b64-a9d7-8edc26bb551e",
  "timestamp": "2025-06-07T13:42:27.441051",
  "session_id": "95d58aff-62c0-4d16-8251-2f0b9659a745",
  "event_type": "error_occurred",
  "error_details": {
    "component": "LLMInterface",
    "severity": "CRITICAL",
    "message": "Generation Error: All LLM providers failed. Last error: 429 Client Error: Too Many Requests for url: https://openrouter.ai/api/v1/chat/completions",
    "has_stack_trace": true
  }
},

{
  "event_id": "e27037fb-b7e9-447d-8721-fe6adf3f275e",
  "timestamp": "2025-06-07T13:42:37.721841",
  "session_id": "95d58aff-62c0-4d16-8251-2f0b9659a745",
  "event_type": "error_occurred",
  "error_details": {
    "component": "LLMInterface",
    "severity": "CRITICAL",
    "message": "Generation Error: All LLM providers failed. Last error: 429 Client Error: Too Many Requests for url: https://openrouter.ai/api/v1/chat/completions",
    "has_stack_trace": true
  }
},

{
  "event_id": "63da7e13-35d5-47b7-ae7a-a7a7c9d3797b",
  "timestamp": "2025-06-07T13:43:01.613506",
  "session_id": "95d58aff-62c0-4d16-8251-2f0b9659a745",
  "event_type": "error_occurred",
  "error_details": {
    "component": "LLMInterface",
    "severity": "CRITICAL",
    "message": "Generation Error: All LLM providers failed. Last error: 429 Client Error: Too Many Requests for url: https://openrouter.ai/api/v1/chat/completions",
    "has_stack_trace": true
  }
},

{
  "event_id": "342e4c15-2662-41d9-bedf-994804dc3310",
  "timestamp": "2025-06-07T13:43:01.614217",
  "session_id": "95d58aff-62c0-4d16-8251-2f0b9659a745",
  "event_type": "llm_response",
  "llm_response": {
    "model": "Mistral Small 3.1 (OpenRouter)",
    "response_length": 0,
    "prompt_tokens": null,
    "completion_tokens": null,
    "total_tokens": null,
    "finish_reason": null,
    "latency_ms": null
  },
  "metadata": {
    "error": "Final generation attempt failed: All LLM providers failed. Last error: 429 Client Error: Too Many Requests for url: https://openrouter.ai/api/v1/chat/completions"
  }
},

{
  "event_id": "8b1b95d3-1254-46bb-b162-293eb0adc140",
  "timestamp": "2025-06-07T13:43:01.616765",
  "session_id": "95d58aff-62c0-4d16-8251-2f0b9659a745",
  "event_type": "llm_query",
  "llm_query": {
    "model": "Mistral Small 3.1 (OpenRouter)",
    "prompt_length": 508,
    "temperature": null,
    "max_tokens": null,
    "has_system_prompt": false,
    "tools_available": 0
  }
},

{
  "event_id": "cc5b5550-ab39-40da-a089-230f7b883b29",
  "timestamp": "2025-06-07T13:43:03.871608",
  "session_id": "95d58aff-62c0-4d16-8251-2f0b9659a745",
  "event_type": "error_occurred",
  "error_details": {
    "component": "LLMInterface",
    "severity": "CRITICAL",
    "message": "Generation Error: All LLM providers failed. Last error: 429 Client Error: Too Many Requests for url: https://openrouter.ai/api/v1/chat/completions",
    "has_stack_trace": true
  }
},

{
  "event_id": "532a39a8-370a-4978-b494-f1dc5f2446a7",
  "timestamp": "2025-06-07T13:43:06.632128",
  "session_id": "95d58aff-62c0-4d16-8251-2f0b9659a745",
  "event_type": "error_occurred",
  "error_details": {
    "component": "LLMInterface",
    "severity": "CRITICAL",
    "message": "Generation Error: All LLM providers failed. Last error: 429 Client Error: Too Many Requests for url: https://openrouter.ai/api/v1/chat/completions",
    "has_stack_trace": true
  }
},

{
  "event_id": "e2f560db-da5f-4191-b7be-c50b0f454fa7",
  "timestamp": "2025-06-07T13:43:10.455730",
  "session_id": "95d58aff-62c0-4d16-8251-2f0b9659a745",
  "event_type": "error_occurred",
  "error_details": {
    "component": "LLMInterface",
    "severity": "CRITICAL",
    "message": "Generation Error: All LLM providers failed. Last error: 429 Client Error: Too Many Requests for url: https://openrouter.ai/api/v1/chat/completions",
    "has_stack_trace": true
  }
},

{
  "event_id": "c246f853-c99b-4450-98ad-c14ccd650a23",
  "timestamp": "2025-06-07T13:43:21.318330",
  "session_id": "95d58aff-62c0-4d16-8251-2f0b9659a745",
  "event_type": "error_occurred",
  "error_details": {
    "component": "LLMInterface",
    "severity": "CRITICAL",
    "message": "Generation Error: All LLM providers failed. Last error: 429 Client Error: Too Many Requests for url: https://openrouter.ai/api/v1/chat/completions",
    "has_stack_trace": true
  }
},

{
  "event_id": "96291335-138f-4b56-80d1-94be5dd52e1b",
  "timestamp": "2025-06-07T13:43:31.257566",
  "session_id": "95d58aff-62c0-4d16-8251-2f0b9659a745",
  "event_type": "error_occurred",
  "error_details": {
    "component": "LLMInterface",
    "severity": "CRITICAL",
    "message": "Generation Error: All LLM providers failed. Last error: 429 Client Error: Too Many Requests for url: https://openrouter.ai/api/v1/chat/completions",
    "has_stack_trace": true
  }
},

{
  "event_id": "6b67c3ed-0f2d-47a2-920f-45272b5daba2",
  "timestamp": "2025-06-07T13:43:50.422120",
  "session_id": "95d58aff-62c0-4d16-8251-2f0b9659a745",
  "event_type": "error_occurred",
  "error_details": {
    "component": "LLMInterface",
    "severity": "CRITICAL",
    "message": "Generation Error: All LLM providers failed. Last error: 429 Client Error: Too Many Requests for url: https://openrouter.ai/api/v1/chat/completions",
    "has_stack_trace": true
  }
},

{
  "event_id": "de71ef5a-3aae-45ca-9627-68521ed7d445",
  "timestamp": "2025-06-07T13:43:50.423180",
  "session_id": "95d58aff-62c0-4d16-8251-2f0b9659a745",
  "event_type": "llm_response",
  "llm_response": {
    "model": "Mistral Small 3.1 (OpenRouter)",
    "response_length": 0,
    "prompt_tokens": null,
    "completion_tokens": null,
    "total_tokens": null,
    "finish_reason": null,
    "latency_ms": null
  },
  "metadata": {
    "error": "Final generation attempt failed: All LLM providers failed. Last error: 429 Client Error: Too Many Requests for url: https://openrouter.ai/api/v1/chat/completions"
  }
},

{
  "event_id": "53b51a7c-66e0-4eec-b974-440f7bf011db",
  "timestamp": "2025-06-07T13:43:50.424346",
  "session_id": "95d58aff-62c0-4d16-8251-2f0b9659a745",
  "event_type": "llm_query",
  "llm_query": {
    "model": "Mistral Small 3.1 (OpenRouter)",
    "prompt_length": 816,
    "temperature": null,
    "max_tokens": null,
    "has_system_prompt": false,
    "tools_available": 0
  }
},

{
  "event_id": "19fe5094-8a0d-4249-bb86-d0d4af659248",
  "timestamp": "2025-06-07T13:43:52.177295",
  "session_id": "95d58aff-62c0-4d16-8251-2f0b9659a745",
  "event_type": "error_occurred",
  "error_details": {
    "component": "LLMInterface",
    "severity": "CRITICAL",
    "message": "Generation Error: All LLM providers failed. Last error: 429 Client Error: Too Many Requests for url: https://openrouter.ai/api/v1/chat/completions",
    "has_stack_trace": true
  }
},

{
  "event_id": "42a9d3ee-f759-476e-83c0-1cff3d257ede",
  "timestamp": "2025-06-07T13:43:54.932943",
  "session_id": "95d58aff-62c0-4d16-8251-2f0b9659a745",
  "event_type": "error_occurred",
  "error_details": {
    "component": "LLMInterface",
    "severity": "CRITICAL",
    "message": "Generation Error: All LLM providers failed. Last error: 429 Client Error: Too Many Requests for url: https://openrouter.ai/api/v1/chat/completions",
    "has_stack_trace": true
  }
},

{
  "event_id": "a8a58f41-c44b-469f-b390-beeb3e86c4b2",
  "timestamp": "2025-06-07T13:44:00.971660",
  "session_id": "95d58aff-62c0-4d16-8251-2f0b9659a745",
  "event_type": "error_occurred",
  "error_details": {
    "component": "LLMInterface",
    "severity": "CRITICAL",
    "message": "Generation Error: All LLM providers failed. Last error: 429 Client Error: Too Many Requests for url: https://openrouter.ai/api/v1/chat/completions",
    "has_stack_trace": true
  }
},

{
  "event_id": "812925e5-42d6-4a6e-983c-1c547cf04601",
  "timestamp": "2025-06-07T13:44:08.341639",
  "session_id": "95d58aff-62c0-4d16-8251-2f0b9659a745",
  "event_type": "error_occurred",
  "error_details": {
    "component": "LLMInterface",
    "severity": "CRITICAL",
    "message": "Generation Error: All LLM providers failed. Last error: 429 Client Error: Too Many Requests for url: https://openrouter.ai/api/v1/chat/completions",
    "has_stack_trace": true
  }
},

{
  "event_id": "6e0b1ee5-9ebd-40e1-87a6-3cb483d8c7ec",
  "timestamp": "2025-06-07T13:44:18.003016",
  "session_id": "95d58aff-62c0-4d16-8251-2f0b9659a745",
  "event_type": "error_occurred",
  "error_details": {
    "component": "LLMInterface",
    "severity": "CRITICAL",
    "message": "Generation Error: All LLM providers failed. Last error: 429 Client Error: Too Many Requests for url: https://openrouter.ai/api/v1/chat/completions",
    "has_stack_trace": true
  }
},

{
  "event_id": "236ace50-3a01-41f8-abf7-770272735c41",
  "timestamp": "2025-06-07T13:44:36.477009",
  "session_id": "95d58aff-62c0-4d16-8251-2f0b9659a745",
  "event_type": "error_occurred",
  "error_details": {
    "component": "LLMInterface",
    "severity": "CRITICAL",
    "message": "Generation Error: All LLM providers failed. Last error: 429 Client Error: Too Many Requests for url: https://openrouter.ai/api/v1/chat/completions",
    "has_stack_trace": true
  }
},

{
  "event_id": "2c7ea461-1b71-4c4f-9f5d-c4221c6d47cb",
  "timestamp": "2025-06-07T13:44:36.477529",
  "session_id": "95d58aff-62c0-4d16-8251-2f0b9659a745",
  "event_type": "llm_response",
  "llm_response": {
    "model": "Mistral Small 3.1 (OpenRouter)",
    "response_length": 0,
    "prompt_tokens": null,
    "completion_tokens": null,
    "total_tokens": null,
    "finish_reason": null,
    "latency_ms": null
  },
  "metadata": {
    "error": "Final generation attempt failed: All LLM providers failed. Last error: 429 Client Error: Too Many Requests for url: https://openrouter.ai/api/v1/chat/completions"
  }
},

{
  "event_id": "93a3b9cb-e63d-42b2-baa7-3e98566b88f3",
  "timestamp": "2025-06-07T13:44:36.478044",
  "session_id": "95d58aff-62c0-4d16-8251-2f0b9659a745",
  "event_type": "llm_query",
  "llm_query": {
    "model": "Mistral Small 3.1 (OpenRouter)",
    "prompt_length": 1124,
    "temperature": null,
    "max_tokens": null,
    "has_system_prompt": false,
    "tools_available": 0
  }
},

{
  "event_id": "959d9758-bf7a-40d8-b2e7-f54708852cdc",
  "timestamp": "2025-06-07T13:44:38.111665",
  "session_id": "95d58aff-62c0-4d16-8251-2f0b9659a745",
  "event_type": "error_occurred",
  "error_details": {
    "component": "LLMInterface",
    "severity": "CRITICAL",
    "message": "Generation Error: All LLM providers failed. Last error: 429 Client Error: Too Many Requests for url: https://openrouter.ai/api/v1/chat/completions",
    "has_stack_trace": true
  }
},

{
  "event_id": "f49a5b43-e03a-446f-8180-8ac369714483",
  "timestamp": "2025-06-07T13:44:40.821591",
  "session_id": "95d58aff-62c0-4d16-8251-2f0b9659a745",
  "event_type": "error_occurred",
  "error_details": {
    "component": "LLMInterface",
    "severity": "CRITICAL",
    "message": "Generation Error: All LLM providers failed. Last error: 429 Client Error: Too Many Requests for url: https://openrouter.ai/api/v1/chat/completions",
    "has_stack_trace": true
  }
},

{
  "event_id": "212448d5-e716-4b84-9d6f-e66c6e25f6c0",
  "timestamp": "2025-06-07T13:44:44.670614",
  "session_id": "95d58aff-62c0-4d16-8251-2f0b9659a745",
  "event_type": "error_occurred",
  "error_details": {
    "component": "LLMInterface",
    "severity": "CRITICAL",
    "message": "Generation Error: All LLM providers failed. Last error: 429 Client Error: Too Many Requests for url: https://openrouter.ai/api/v1/chat/completions",
    "has_stack_trace": true
  }
},

{
  "event_id": "00a7f2ea-dd28-4431-86a7-d785fc3112fd",
  "timestamp": "2025-06-07T13:44:50.591739",
  "session_id": "95d58aff-62c0-4d16-8251-2f0b9659a745",
  "event_type": "error_occurred",
  "error_details": {
    "component": "LLMInterface",
    "severity": "CRITICAL",
    "message": "Generation Error: All LLM providers failed. Last error: 429 Client Error: Too Many Requests for url: https://openrouter.ai/api/v1/chat/completions",
    "has_stack_trace": true
  }
},

{
  "event_id": "a0e7d063-0b00-4a10-95c4-7c7bb20a46c0",
  "timestamp": "2025-06-07T13:45:00.458734",
  "session_id": "95d58aff-62c0-4d16-8251-2f0b9659a745",
  "event_type": "error_occurred",
  "error_details": {
    "component": "LLMInterface",
    "severity": "CRITICAL",
    "message": "Generation Error: All LLM providers failed. Last error: 429 Client Error: Too Many Requests for url: https://openrouter.ai/api/v1/chat/completions",
    "has_stack_trace": true
  }
},

{
  "event_id": "a6deab2a-2804-4f8e-a568-75e76b6ee957",
  "timestamp": "2025-06-07T13:45:20.779230",
  "session_id": "95d58aff-62c0-4d16-8251-2f0b9659a745",
  "event_type": "error_occurred",
  "error_details": {
    "component": "LLMInterface",
    "severity": "CRITICAL",
    "message": "Generation Error: All LLM providers failed. Last error: 429 Client Error: Too Many Requests for url: https://openrouter.ai/api/v1/chat/completions",
    "has_stack_trace": true
  }
},

{
  "event_id": "cd9882ea-bc90-4b14-a64e-a8315f054cb8",
  "timestamp": "2025-06-07T13:45:20.779230",
  "session_id": "95d58aff-62c0-4d16-8251-2f0b9659a745",
  "event_type": "llm_response",
  "llm_response": {
    "model": "Mistral Small 3.1 (OpenRouter)",
    "response_length": 0,
    "prompt_tokens": null,
    "completion_tokens": null,
    "total_tokens": null,
    "finish_reason": null,
    "latency_ms": null
  },
  "metadata": {
    "error": "Final generation attempt failed: All LLM providers failed. Last error: 429 Client Error: Too Many Requests for url: https://openrouter.ai/api/v1/chat/completions"
  }
},

{
  "event_id": "babbe44e-fdf1-4244-b980-511c59a21ee0",
  "timestamp": "2025-06-07T13:45:20.780556",
  "session_id": "95d58aff-62c0-4d16-8251-2f0b9659a745",
  "event_type": "llm_query",
  "llm_query": {
    "model": "Mistral Small 3.1 (OpenRouter)",
    "prompt_length": 1432,
    "temperature": null,
    "max_tokens": null,
    "has_system_prompt": false,
    "tools_available": 0
  }
},

{
  "event_id": "4884f162-f559-46c5-8d30-0487b4b10cd4",
  "timestamp": "2025-06-07T13:45:22.435333",
  "session_id": "95d58aff-62c0-4d16-8251-2f0b9659a745",
  "event_type": "error_occurred",
  "error_details": {
    "component": "LLMInterface",
    "severity": "CRITICAL",
    "message": "Generation Error: All LLM providers failed. Last error: 429 Client Error: Too Many Requests for url: https://openrouter.ai/api/v1/chat/completions",
    "has_stack_trace": true
  }
},

{
  "event_id": "6b263bd5-f1b8-486a-af34-350d989f388b",
  "timestamp": "2025-06-07T13:45:25.210184",
  "session_id": "95d58aff-62c0-4d16-8251-2f0b9659a745",
  "event_type": "error_occurred",
  "error_details": {
    "component": "LLMInterface",
    "severity": "CRITICAL",
    "message": "Generation Error: All LLM providers failed. Last error: 429 Client Error: Too Many Requests for url: https://openrouter.ai/api/v1/chat/completions",
    "has_stack_trace": true
  }
},

{
  "event_id": "459aa637-d600-4583-9f64-465c83a664b0",
  "timestamp": "2025-06-07T13:45:28.963652",
  "session_id": "95d58aff-62c0-4d16-8251-2f0b9659a745",
  "event_type": "error_occurred",
  "error_details": {
    "component": "LLMInterface",
    "severity": "CRITICAL",
    "message": "Generation Error: All LLM providers failed. Last error: 429 Client Error: Too Many Requests for url: https://openrouter.ai/api/v1/chat/completions",
    "has_stack_trace": true
  }
},

{
  "event_id": "a08b6666-57ae-4f86-8850-7b671edc39fb",
  "timestamp": "2025-06-07T13:45:37.655655",
  "session_id": "95d58aff-62c0-4d16-8251-2f0b9659a745",
  "event_type": "error_occurred",
  "error_details": {
    "component": "LLMInterface",
    "severity": "CRITICAL",
    "message": "Generation Error: All LLM providers failed. Last error: 429 Client Error: Too Many Requests for url: https://openrouter.ai/api/v1/chat/completions",
    "has_stack_trace": true
  }
},

{
  "event_id": "7d17b0e2-fee2-4c4c-86c1-3cdf944cc2a4",
  "timestamp": "2025-06-07T13:45:47.801455",
  "session_id": "95d58aff-62c0-4d16-8251-2f0b9659a745",
  "event_type": "error_occurred",
  "error_details": {
    "component": "LLMInterface",
    "severity": "CRITICAL",
    "message": "Generation Error: All LLM providers failed. Last error: 429 Client Error: Too Many Requests for url: https://openrouter.ai/api/v1/chat/completions",
    "has_stack_trace": true
  }
},

{
  "event_id": "2000f9a5-e0f9-47b0-bcd8-9ae25b2f96fc",
  "timestamp": "2025-06-07T13:46:06.271652",
  "session_id": "95d58aff-62c0-4d16-8251-2f0b9659a745",
  "event_type": "error_occurred",
  "error_details": {
    "component": "LLMInterface",
    "severity": "CRITICAL",
    "message": "Generation Error: All LLM providers failed. Last error: 429 Client Error: Too Many Requests for url: https://openrouter.ai/api/v1/chat/completions",
    "has_stack_trace": true
  }
},

{
  "event_id": "34256b9a-fd3f-4072-ae6b-3461fc0daa36",
  "timestamp": "2025-06-07T13:46:06.271982",
  "session_id": "95d58aff-62c0-4d16-8251-2f0b9659a745",
  "event_type": "llm_response",
  "llm_response": {
    "model": "Mistral Small 3.1 (OpenRouter)",
    "response_length": 0,
    "prompt_tokens": null,
    "completion_tokens": null,
    "total_tokens": null,
    "finish_reason": null,
    "latency_ms": null
  },
  "metadata": {
    "error": "Final generation attempt failed: All LLM providers failed. Last error: 429 Client Error: Too Many Requests for url: https://openrouter.ai/api/v1/chat/completions"
  }
},

{
  "event_id": "ecef8dbb-b43d-40d1-952e-41ff5c764464",
  "timestamp": "2025-06-07T13:46:06.272054",
  "session_id": "95d58aff-62c0-4d16-8251-2f0b9659a745",
  "event_type": "llm_query",
  "llm_query": {
    "model": "Mistral Small 3.1 (OpenRouter)",
    "prompt_length": 1740,
    "temperature": null,
    "max_tokens": null,
    "has_system_prompt": false,
    "tools_available": 0
  }
},

{
  "event_id": "0932060f-e55a-438f-903e-aabad0ace178",
  "timestamp": "2025-06-07T13:46:08.061638",
  "session_id": "95d58aff-62c0-4d16-8251-2f0b9659a745",
  "event_type": "error_occurred",
  "error_details": {
    "component": "LLMInterface",
    "severity": "CRITICAL",
    "message": "Generation Error: All LLM providers failed. Last error: 429 Client Error: Too Many Requests for url: https://openrouter.ai/api/v1/chat/completions",
    "has_stack_trace": true
  }
},

{
  "event_id": "694476cb-7e8b-4fdf-a1e4-42e42e164dbb",
  "timestamp": "2025-06-07T13:46:10.828987",
  "session_id": "95d58aff-62c0-4d16-8251-2f0b9659a745",
  "event_type": "error_occurred",
  "error_details": {
    "component": "LLMInterface",
    "severity": "CRITICAL",
    "message": "Generation Error: All LLM providers failed. Last error: 429 Client Error: Too Many Requests for url: https://openrouter.ai/api/v1/chat/completions",
    "has_stack_trace": true
  }
},

{
  "event_id": "77dafe9a-0cb1-42e0-9257-613f1c6b4394",
  "timestamp": "2025-06-07T13:46:15.642601",
  "session_id": "95d58aff-62c0-4d16-8251-2f0b9659a745",
  "event_type": "error_occurred",
  "error_details": {
    "component": "LLMInterface",
    "severity": "CRITICAL",
    "message": "Generation Error: All LLM providers failed. Last error: 429 Client Error: Too Many Requests for url: https://openrouter.ai/api/v1/chat/completions",
    "has_stack_trace": true
  }
},

{
  "event_id": "a20441c2-aab5-4cac-bf56-3502296ddd6b",
  "timestamp": "2025-06-07T13:46:21.736854",
  "session_id": "95d58aff-62c0-4d16-8251-2f0b9659a745",
  "event_type": "error_occurred",
  "error_details": {
    "component": "LLMInterface",
    "severity": "CRITICAL",
    "message": "Generation Error: All LLM providers failed. Last error: 429 Client Error: Too Many Requests for url: https://openrouter.ai/api/v1/chat/completions",
    "has_stack_trace": true
  }
},

{
  "event_id": "bb999331-85a8-4bfd-aa11-68ee2e72d747",
  "timestamp": "2025-06-07T13:46:31.919155",
  "session_id": "95d58aff-62c0-4d16-8251-2f0b9659a745",
  "event_type": "error_occurred",
  "error_details": {
    "component": "LLMInterface",
    "severity": "CRITICAL",
    "message": "Generation Error: All LLM providers failed. Last error: 429 Client Error: Too Many Requests for url: https://openrouter.ai/api/v1/chat/completions",
    "has_stack_trace": true
  }
},

{
  "event_id": "693fbf19-c401-4f2b-b2e8-bb7a31154f2d",
  "timestamp": "2025-06-07T13:46:52.226734",
  "session_id": "95d58aff-62c0-4d16-8251-2f0b9659a745",
  "event_type": "error_occurred",
  "error_details": {
    "component": "LLMInterface",
    "severity": "CRITICAL",
    "message": "Generation Error: All LLM providers failed. Last error: 429 Client Error: Too Many Requests for url: https://openrouter.ai/api/v1/chat/completions",
    "has_stack_trace": true
  }
},

{
  "event_id": "217aaa68-e763-4691-a9f4-184cb8c7f9d4",
  "timestamp": "2025-06-07T13:46:52.227237",
  "session_id": "95d58aff-62c0-4d16-8251-2f0b9659a745",
  "event_type": "llm_response",
  "llm_response": {
    "model": "Mistral Small 3.1 (OpenRouter)",
    "response_length": 0,
    "prompt_tokens": null,
    "completion_tokens": null,
    "total_tokens": null,
    "finish_reason": null,
    "latency_ms": null
  },
  "metadata": {
    "error": "Final generation attempt failed: All LLM providers failed. Last error: 429 Client Error: Too Many Requests for url: https://openrouter.ai/api/v1/chat/completions"
  }
},

{
  "event_id": "25d6f0f9-3d7c-4cdd-881f-48ea05dc627e",
  "timestamp": "2025-06-07T13:46:52.227820",
  "session_id": "95d58aff-62c0-4d16-8251-2f0b9659a745",
  "event_type": "llm_query",
  "llm_query": {
    "model": "Mistral Small 3.1 (OpenRouter)",
    "prompt_length": 2048,
    "temperature": null,
    "max_tokens": null,
    "has_system_prompt": false,
    "tools_available": 0
  }
},

{
  "event_id": "9bb5ef9e-8b0d-4a0e-bdc0-9c91f1048431",
  "timestamp": "2025-06-07T13:46:53.864121",
  "session_id": "95d58aff-62c0-4d16-8251-2f0b9659a745",
  "event_type": "error_occurred",
  "error_details": {
    "component": "LLMInterface",
    "severity": "CRITICAL",
    "message": "Generation Error: All LLM providers failed. Last error: 429 Client Error: Too Many Requests for url: https://openrouter.ai/api/v1/chat/completions",
    "has_stack_trace": true
  }
},

{
  "event_id": "801d3bbd-958c-4334-9ab7-1e1d24640ef2",
  "timestamp": "2025-06-07T13:46:56.588508",
  "session_id": "95d58aff-62c0-4d16-8251-2f0b9659a745",
  "event_type": "error_occurred",
  "error_details": {
    "component": "LLMInterface",
    "severity": "CRITICAL",
    "message": "Generation Error: All LLM providers failed. Last error: 429 Client Error: Too Many Requests for url: https://openrouter.ai/api/v1/chat/completions",
    "has_stack_trace": true
  }
},

{
  "event_id": "64fbbd6e-f864-4cd5-9209-626e2bce30a9",
  "timestamp": "2025-06-07T13:47:00.357744",
  "session_id": "95d58aff-62c0-4d16-8251-2f0b9659a745",
  "event_type": "error_occurred",
  "error_details": {
    "component": "LLMInterface",
    "severity": "CRITICAL",
    "message": "Generation Error: All LLM providers failed. Last error: 429 Client Error: Too Many Requests for url: https://openrouter.ai/api/v1/chat/completions",
    "has_stack_trace": true
  }
},

{
  "event_id": "f11ed427-1aa2-4b56-9f35-5067611291d9",
  "timestamp": "2025-06-07T13:47:06.211283",
  "session_id": "95d58aff-62c0-4d16-8251-2f0b9659a745",
  "event_type": "error_occurred",
  "error_details": {
    "component": "LLMInterface",
    "severity": "CRITICAL",
    "message": "Generation Error: All LLM providers failed. Last error: 429 Client Error: Too Many Requests for url: https://openrouter.ai/api/v1/chat/completions",
    "has_stack_trace": true
  }
},

{
  "event_id": "ca2f2014-2635-4a59-8f36-79127bc2d2d6",
  "timestamp": "2025-06-07T13:47:16.489255",
  "session_id": "95d58aff-62c0-4d16-8251-2f0b9659a745",
  "event_type": "error_occurred",
  "error_details": {
    "component": "LLMInterface",
    "severity": "CRITICAL",
    "message": "Generation Error: All LLM providers failed. Last error: 429 Client Error: Too Many Requests for url: https://openrouter.ai/api/v1/chat/completions",
    "has_stack_trace": true
  }
},

{
  "event_id": "42175a64-60e8-49d0-a990-9ab45b18f69f",
  "timestamp": "2025-06-07T13:47:34.797532",
  "session_id": "95d58aff-62c0-4d16-8251-2f0b9659a745",
  "event_type": "error_occurred",
  "error_details": {
    "component": "LLMInterface",
    "severity": "CRITICAL",
    "message": "Generation Error: All LLM providers failed. Last error: 429 Client Error: Too Many Requests for url: https://openrouter.ai/api/v1/chat/completions",
    "has_stack_trace": true
  }
},

{
  "event_id": "9767a7ee-452d-4123-87ec-8177c83b784a",
  "timestamp": "2025-06-07T13:47:34.797532",
  "session_id": "95d58aff-62c0-4d16-8251-2f0b9659a745",
  "event_type": "llm_response",
  "llm_response": {
    "model": "Mistral Small 3.1 (OpenRouter)",
    "response_length": 0,
    "prompt_tokens": null,
    "completion_tokens": null,
    "total_tokens": null,
    "finish_reason": null,
    "latency_ms": null
  },
  "metadata": {
    "error": "Final generation attempt failed: All LLM providers failed. Last error: 429 Client Error: Too Many Requests for url: https://openrouter.ai/api/v1/chat/completions"
  }
},

{
  "event_id": "e42ebce0-5ebe-4a3e-8953-527f2fb23a57",
  "timestamp": "2025-06-07T13:47:34.798081",
  "session_id": "95d58aff-62c0-4d16-8251-2f0b9659a745",
  "event_type": "llm_query",
  "llm_query": {
    "model": "Mistral Small 3.1 (OpenRouter)",
    "prompt_length": 2356,
    "temperature": null,
    "max_tokens": null,
    "has_system_prompt": false,
    "tools_available": 0
  }
},

{
  "event_id": "703a345f-4e48-4b66-b775-1492cff313b3",
  "timestamp": "2025-06-07T13:47:41.901516",
  "session_id": "95d58aff-62c0-4d16-8251-2f0b9659a745",
  "event_type": "error_occurred",
  "error_details": {
    "component": "LLMInterface",
    "severity": "CRITICAL",
    "message": "Generation Error: All LLM providers failed. Last error: 429 Client Error: Too Many Requests for url: https://openrouter.ai/api/v1/chat/completions",
    "has_stack_trace": true
  }
},

{
  "event_id": "1f837e08-3f4f-4bb2-aa72-6e38e0d3ac9d",
  "timestamp": "2025-06-07T13:47:44.597150",
  "session_id": "95d58aff-62c0-4d16-8251-2f0b9659a745",
  "event_type": "error_occurred",
  "error_details": {
    "component": "LLMInterface",
    "severity": "CRITICAL",
    "message": "Generation Error: All LLM providers failed. Last error: 429 Client Error: Too Many Requests for url: https://openrouter.ai/api/v1/chat/completions",
    "has_stack_trace": true
  }
},

{
  "event_id": "9784261f-6908-489e-a157-e09c908a90b0",
  "timestamp": "2025-06-07T13:47:48.386076",
  "session_id": "95d58aff-62c0-4d16-8251-2f0b9659a745",
  "event_type": "error_occurred",
  "error_details": {
    "component": "LLMInterface",
    "severity": "CRITICAL",
    "message": "Generation Error: All LLM providers failed. Last error: 429 Client Error: Too Many Requests for url: https://openrouter.ai/api/v1/chat/completions",
    "has_stack_trace": true
  }
},

{
  "event_id": "514d349f-52f3-4aaa-a946-e521210d9b34",
  "timestamp": "2025-06-07T13:47:54.281637",
  "session_id": "95d58aff-62c0-4d16-8251-2f0b9659a745",
  "event_type": "error_occurred",
  "error_details": {
    "component": "LLMInterface",
    "severity": "CRITICAL",
    "message": "Generation Error: All LLM providers failed. Last error: 429 Client Error: Too Many Requests for url: https://openrouter.ai/api/v1/chat/completions",
    "has_stack_trace": true
  }
},

{
  "event_id": "ee0563a7-ae28-451e-853a-a1878899089b",
  "timestamp": "2025-06-07T13:48:04.342391",
  "session_id": "95d58aff-62c0-4d16-8251-2f0b9659a745",
  "event_type": "error_occurred",
  "error_details": {
    "component": "LLMInterface",
    "severity": "CRITICAL",
    "message": "Generation Error: All LLM providers failed. Last error: 429 Client Error: Too Many Requests for url: https://openrouter.ai/api/v1/chat/completions",
    "has_stack_trace": true
  }
},

{
  "event_id": "cc310a92-e480-4e8e-a944-e02daa886395",
  "timestamp": "2025-06-07T13:48:22.309471",
  "session_id": "95d58aff-62c0-4d16-8251-2f0b9659a745",
  "event_type": "error_occurred",
  "error_details": {
    "component": "LLMInterface",
    "severity": "CRITICAL",
    "message": "Generation Error: All LLM providers failed. Last error: 429 Client Error: Too Many Requests for url: https://openrouter.ai/api/v1/chat/completions",
    "has_stack_trace": true
  }
},

{
  "event_id": "7fd4f671-92dd-49f2-8d5b-9a419430382f",
  "timestamp": "2025-06-07T13:48:22.309977",
  "session_id": "95d58aff-62c0-4d16-8251-2f0b9659a745",
  "event_type": "llm_response",
  "llm_response": {
    "model": "Mistral Small 3.1 (OpenRouter)",
    "response_length": 0,
    "prompt_tokens": null,
    "completion_tokens": null,
    "total_tokens": null,
    "finish_reason": null,
    "latency_ms": null
  },
  "metadata": {
    "error": "Final generation attempt failed: All LLM providers failed. Last error: 429 Client Error: Too Many Requests for url: https://openrouter.ai/api/v1/chat/completions"
  }
}