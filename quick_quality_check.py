import os
# Quick quality check: compare outputs from run() vs plan_and_execute()
os.environ["OPENROUTER_API_KEY"] = "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"

from agent_capability_benchmark import initialize_agent

agent = initialize_agent()
if not agent:
    raise RuntimeError("Agent initialization failed")

prompts = [
    "What is the capital of France? Provide just the name of the city.",
    "Create a file called 'sample.txt' with content 'Hello, Quality!'"
]

for prompt in prompts:
    print(f"Prompt: {prompt}")
    # run()
    success1, result1, history1 = agent.run(prompt)
    print(f"run() -> success={success1}, result={result1.strip()}")
    # plan_and_execute()
    success2, result2, actions2 = agent.plan_and_execute(prompt, parallel=True)
    print(f"plan_and_execute() -> success={success2}, result={result2.strip()}")
    print("Actions:", actions2)
    print("-") 