import pytest
from mindlink.agent import Agent<PERSON>
from mindlink.models.llm import LLMInterface

class Dummy<PERSON>M(LLMInterface):
    def __init__(self):
        self.calls = 0
        self.last_token_usage = {}

    def generate(self, system_prompt, user_prompt):
        self.calls += 1
        # First two calls return the same action
        if self.calls <= 2:
            return '{"action": {"tool_name": "run_shell_command", "parameters": {"command": "echo 1"}}, "reasoning": "duplicate step", "thought": "testing skip"}'
        # Third call finishes
        return '{"action": {"tool_name": "finish", "parameters": {"result": "done"}}, "reasoning": "finish step", "thought": "completed"}'


def test_skip_duplicates():
    llm = DummyLLM()
    agent = AgentOS(llm=llm, system_prompt_template="", max_steps=5)
    success, observation, history = agent.run("dummy goal")
    # Expect only one run_shell_command history entry plus the finish
    tool_names = [entry['request'].action.tool_name for entry in history]
    assert tool_names.count("run_shell_command") == 1
    assert tool_names[-1] == "finish"
    assert success is True
