"""
Agent Capability Benchmark for MindLink

This script evaluates the MindLink agent's capabilities across key dimensions
with a standardized 0-100 scoring system suitable for comparing with
commercial coding agents.
"""

import os
import sys
import time
import json
import tempfile
import shutil
import random
import string
import logging
from pathlib import Path
from typing import Dict, List, Any, Tuple, Optional
from dataclasses import dataclass, field
import statistics

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('agent_benchmark.log')
    ]
)
logger = logging.getLogger(__name__)

# Add the current directory to the path so we can import mindlink
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Try to import MindLink
try:
    from mindlink.agent import AgentOS
    from mindlink.models.openai import OpenAIModel
    from mindlink.models.openrouter import OpenRouterModel
    from mindlink.config import DEFAULT_SYSTEM_PROMPT
    
    IMPORTS_SUCCESSFUL = True
    logger.info("[SUCCESS] MindLink agent imported successfully")
except ImportError as e:
    IMPORTS_SUCCESSFUL = False
    logger.error(f"[ERROR] Failed to import MindLink: {e}")
    sys.exit(1)

@dataclass
class BenchmarkScore:
    """Container for capability benchmark scores."""
    # Core capabilities
    llm_integration: float = 0.0
    command_decomposition: float = 0.0
    tool_execution: float = 0.0
    multi_step_planning: float = 0.0
    
    # File operations
    file_creation: float = 0.0
    file_reading: float = 0.0
    file_editing: float = 0.0
    
    # Code operations
    code_analysis: float = 0.0
    code_generation: float = 0.0
    
    # Project management
    project_structure: float = 0.0
    error_handling: float = 0.0
    
    # Time metrics (seconds)
    execution_times: Dict[str, float] = field(default_factory=dict)
    
    def get_average_score(self) -> float:
        """Calculate the overall average score across all capabilities."""
        scores = [
            self.llm_integration,
            self.command_decomposition,
            self.tool_execution,
            self.multi_step_planning,
            self.file_creation,
            self.file_reading, 
            self.file_editing,
            self.code_analysis,
            self.code_generation,
            self.project_structure,
            self.error_handling
        ]
        return statistics.mean(scores)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert scores to a dictionary format."""
        return {
            "core_capabilities": {
                "llm_integration": self.llm_integration,
                "command_decomposition": self.command_decomposition,
                "tool_execution": self.tool_execution,
                "multi_step_planning": self.multi_step_planning,
            },
            "file_operations": {
                "file_creation": self.file_creation,
                "file_reading": self.file_reading,
                "file_editing": self.file_editing,
            },
            "code_operations": {
                "code_analysis": self.code_analysis,
                "code_generation": self.code_generation,
            },
            "project_management": {
                "project_structure": self.project_structure,
                "error_handling": self.error_handling,
            },
            "execution_times": self.execution_times,
            "overall_score": self.get_average_score()
        }

def initialize_agent():
    """Initialize the MindLink agent with available API keys."""
    # Try OpenAI first, then OpenRouter
    api_key = os.getenv("OPENAI_API_KEY")
    if api_key:
        logger.info("[INFO] Using OpenAI for agent")
        llm = OpenAIModel(api_key=api_key)
    else:
        api_key = os.getenv("OPENROUTER_API_KEY")
        if api_key:
            logger.info("[INFO] Using OpenRouter for agent")
            llm = OpenRouterModel(api_key=api_key)
        else:
            logger.error("[ERROR] No API keys found for any LLM provider")
            return None
    
    agent = AgentOS(
        llm=llm,
        system_prompt_template=DEFAULT_SYSTEM_PROMPT,
        max_steps=50
    )
    
    return agent

def test_llm_integration(agent, scores: BenchmarkScore) -> None:
    """Test the agent's LLM integration capabilities."""
    logger.info("[TEST] Testing LLM integration")
    
    # Simple fact-based query to test basic LLM functionality
    prompt = "What is the capital of France? Provide just the name of the city."
    
    start_time = time.time()
    success, result, history = agent.run(prompt)
    execution_time = time.time() - start_time
    
    # Score based on:
    # 1. Whether the LLM responded
    # 2. Response correctness (contains "Paris")
    # 3. Response time (under 5 seconds is optimal)
    
    score = 0.0
    if success:
        score += 50.0  # Base score for success
        
        if "paris" in result.lower():
            score += 30.0  # Correctness
        
        # Time factor (max 20 points)
        time_score = min(20.0, 20.0 * (5.0 / max(1.0, execution_time)))
        score += time_score
    
    scores.llm_integration = score
    scores.execution_times["llm_integration"] = execution_time
    
    logger.info(f"LLM integration score: {score:.1f}/100")
    logger.info(f"Execution time: {execution_time:.2f} seconds")

def test_command_decomposition(agent, scores: BenchmarkScore) -> None:
    """Test the agent's ability to break down complex commands."""
    logger.info("[TEST] Testing command decomposition")
    
    # Complex multi-step command
    prompt = """
    Create a Python calculator application with the following features:
    1. A main.py file with a command-line interface
    2. Support for basic operations (add, subtract, multiply, divide)
    3. A history feature that remembers previous calculations
    4. Error handling for invalid inputs
    5. A help menu that explains how to use the calculator
    """
    
    start_time = time.time()
    success, result, history = agent.run(prompt)
    execution_time = time.time() - start_time
    
    # Score based on:
    # 1. Whether the command was successfully decomposed
    # 2. Number of logical steps taken to complete the task
    # 3. Proper sequencing of steps
    
    score = 0.0
    if success:
        score += 40.0  # Base score for success
        
        # Number of steps (optimal is 5-15 steps for this task)
        steps = len(history)
        if 5 <= steps <= 15:
            score += 30.0
        elif steps < 5:
            score += 15.0  # Too few steps
        else:
            score += max(0.0, 30.0 - (steps - 15) * 2)  # Deduction for too many steps
        
        # Check for logical sequencing by examining created files
        files_created = []
        for entry in history:
            if hasattr(entry, 'request') and hasattr(entry['request'], 'action'):
                action = entry['request'].action
                if hasattr(action, 'tool_name') and action.tool_name == 'create_file':
                    if hasattr(action, 'params') and 'path' in action.params:
                        files_created.append(action.params['path'])
        
        if 'main.py' in files_created:
            score += 15.0  # Created main entry point
        
        # Check result for mentions of completed features
        feature_checks = [
            "calculator" in result.lower(),
            "command" in result.lower() or "cli" in result.lower() or "interface" in result.lower(),
            "operation" in result.lower() or "add" in result.lower() or "subtract" in result.lower(),
            "history" in result.lower(),
            "error" in result.lower() or "exception" in result.lower() or "handling" in result.lower(),
            "help" in result.lower() or "usage" in result.lower() or "menu" in result.lower()
        ]
        
        score += 15.0 * (sum(feature_checks) / len(feature_checks))
    
    scores.command_decomposition = score
    scores.execution_times["command_decomposition"] = execution_time
    
    logger.info(f"Command decomposition score: {score:.1f}/100")
    logger.info(f"Execution time: {execution_time:.2f} seconds")

def test_tool_execution(agent, scores: BenchmarkScore) -> None:
    """Test the agent's ability to execute various tools."""
    logger.info("[TEST] Testing tool execution capabilities")
    
    # Multi-tool task that requires file operations and command execution
    prompt = """
    Perform the following tasks:
    1. Create a Python file called 'hello.py' that prints "Hello, World!"
    2. Run this Python script and capture its output
    3. Create a new file called 'output.txt' containing the output from the script
    4. Read the content of 'output.txt' and confirm it matches what was expected
    """
    
    start_time = time.time()
    success, result, history = agent.run(prompt)
    execution_time = time.time() - start_time
    
    # Score based on:
    # 1. Whether all tools were executed correctly
    # 2. Proper sequencing of tool usage
    # 3. Appropriate error handling
    
    score = 0.0
    if success:
        score += 30.0  # Base score for success
        
        # Check for correct tool usage
        create_file_used = False
        run_command_used = False
        read_file_used = False
        
        for entry in history:
            if hasattr(entry, 'request') and hasattr(entry['request'], 'action'):
                action = entry['request'].action
                if hasattr(action, 'tool_name'):
                    if action.tool_name == 'create_file':
                        create_file_used = True
                    elif action.tool_name == 'run_shell_command':
                        run_command_used = True
                    elif action.tool_name == 'read_file':
                        read_file_used = True
        
        # Score for tool variety (30 points)
        tools_used = sum([create_file_used, run_command_used, read_file_used])
        score += 10.0 * tools_used
        
        # Check for existence of required files (40 points)
        file_points = 0.0
        if os.path.exists('hello.py'):
            file_points += 20.0
        if os.path.exists('output.txt'):
            file_points += 20.0
            
            # Check content (bonus)
            try:
                with open('output.txt', 'r') as f:
                    content = f.read()
                    if "hello, world" in content.lower():
                        score += 10.0  # Bonus for correct content
            except:
                pass
                
        score += file_points
    
    scores.tool_execution = score
    scores.execution_times["tool_execution"] = execution_time
    
    logger.info(f"Tool execution score: {score:.1f}/100")
    logger.info(f"Execution time: {execution_time:.2f} seconds")

def test_multi_step_planning(agent, scores: BenchmarkScore) -> None:
    """Test the agent's ability to plan and execute multi-step tasks."""
    logger.info("[TEST] Testing multi-step planning capabilities")
    
    # Complex task requiring planning
    prompt = """
    Create a simple web scraper application that:
    1. Has a main script that takes a URL as an input
    2. Fetches the content from the URL
    3. Extracts all links from the page
    4. Saves the links to a CSV file
    5. Has proper error handling for network issues
    6. Includes a README explaining how to use the tool
    
    Use the requests and BeautifulSoup libraries for this task.
    """
    
    start_time = time.time()
    success, result, history = agent.run(prompt)
    execution_time = time.time() - start_time
    
    # Score based on:
    # 1. Overall success of the planning task
    # 2. Logical organization of steps
    # 3. Completeness of implementation
    
    score = 0.0
    if success:
        score += 30.0  # Base score for success
        
        # Check for required files
        files_created = []
        for entry in history:
            if hasattr(entry, 'request') and hasattr(entry['request'], 'action'):
                action = entry['request'].action
                if hasattr(action, 'tool_name') and action.tool_name == 'create_file':
                    if hasattr(action, 'params') and 'path' in action.params:
                        files_created.append(action.params['path'])
        
        # Check for main script (20 points)
        has_main = any(f.endswith('.py') for f in files_created)
        if has_main:
            score += 20.0
        
        # Check for README (10 points)
        has_readme = any(f.lower() == 'readme.md' for f in files_created)
        if has_readme:
            score += 10.0
        
        # Check implementation completeness (40 points)
        completeness_checks = []
        for file_path in files_created:
            if file_path.endswith('.py'):
                try:
                    with open(file_path, 'r') as f:
                        content = f.read().lower()
                        completeness_checks.extend([
                            'import requests' in content,
                            'beautifulsoup' in content or 'bs4' in content,
                            'csv' in content,
                            'try' in content and 'except' in content,  # Error handling
                            'url' in content and ('input' in content or 'arg' in content),
                            'write' in content or 'save' in content
                        ])
                except:
                    pass
        
        if completeness_checks:
            score += 40.0 * (sum(completeness_checks) / len(completeness_checks))
    
    scores.multi_step_planning = score
    scores.execution_times["multi_step_planning"] = execution_time
    
    logger.info(f"Multi-step planning score: {score:.1f}/100")
    logger.info(f"Execution time: {execution_time:.2f} seconds")

def test_file_operations(agent, scores: BenchmarkScore) -> None:
    """Test the agent's file operation capabilities."""
    logger.info("[TEST] Testing file operations")
    
    # Test file creation
    logger.info("[TEST] Testing file creation")
    
    creation_prompt = """
    Create a file called 'test_data.json' with the following content:
    {
        "name": "Test User",
        "age": 30,
        "email": "<EMAIL>",
        "skills": ["Python", "JavaScript", "SQL"]
    }
    """
    
    start_time = time.time()
    creation_success, creation_result, creation_history = agent.run(creation_prompt)
    creation_time = time.time() - start_time
    
    # Score file creation (0-100)
    creation_score = 0.0
    if creation_success and os.path.exists('test_data.json'):
        creation_score += 60.0  # Base score for creating the file
        
        # Check content correctness
        try:
            with open('test_data.json', 'r') as f:
                content = f.read()
                try:
                    data = json.loads(content)
                    if data.get('name') == 'Test User' and data.get('age') == 30:
                        creation_score += 20.0  # Correct basic content
                    
                    if isinstance(data.get('skills'), list) and len(data.get('skills')) == 3:
                        creation_score += 20.0  # Correct complex content
                except json.JSONDecodeError:
                    # File exists but has invalid JSON
                    creation_score += 10.0  # Partial credit for creating a file with content
        except:
            pass
    
    scores.file_creation = creation_score
    scores.execution_times["file_creation"] = creation_time
    
    logger.info(f"File creation score: {creation_score:.1f}/100")
    
    # Test file reading
    logger.info("[TEST] Testing file reading")
    
    reading_prompt = """
    Read the content of 'test_data.json' and tell me:
    1. The name of the user
    2. Their age
    3. How many skills they have
    """
    
    start_time = time.time()
    reading_success, reading_result, reading_history = agent.run(reading_prompt)
    reading_time = time.time() - start_time
    
    # Score file reading (0-100)
    reading_score = 0.0
    if reading_success:
        reading_score += 40.0  # Base score for success
        
        # Check if the response contains the correct information
        result_lower = reading_result.lower()
        if 'test user' in result_lower:
            reading_score += 20.0  # Correct name
        
        if '30' in result_lower:
            reading_score += 20.0  # Correct age
        
        if '3' in result_lower and 'skills' in result_lower:
            reading_score += 20.0  # Correct skills count
    
    scores.file_reading = reading_score
    scores.execution_times["file_reading"] = reading_time
    
    logger.info(f"File reading score: {reading_score:.1f}/100")
    
    # Test file editing
    logger.info("[TEST] Testing file editing")
    
    editing_prompt = """
    Modify the 'test_data.json' file to:
    1. Change the age to 31
    2. Add a new field called 'location' with the value 'New York'
    3. Add a new skill 'Docker' to the skills array
    """
    
    start_time = time.time()
    editing_success, editing_result, editing_history = agent.run(editing_prompt)
    editing_time = time.time() - start_time
    
    # Score file editing (0-100)
    editing_score = 0.0
    if editing_success and os.path.exists('test_data.json'):
        editing_score += 40.0  # Base score for success
        
        # Check if edits were correctly applied
        try:
            with open('test_data.json', 'r') as f:
                content = f.read()
                try:
                    data = json.loads(content)
                    if data.get('age') == 31:
                        editing_score += 20.0  # Correctly modified age
                    
                    if data.get('location') == 'New York':
                        editing_score += 20.0  # Correctly added new field
                    
                    if 'Docker' in data.get('skills', []):
                        editing_score += 20.0  # Correctly modified array
                except json.JSONDecodeError:
                    pass
        except:
            pass
    
    scores.file_editing = editing_score
    scores.execution_times["file_editing"] = editing_time
    
    logger.info(f"File editing score: {editing_score:.1f}/100")

def test_code_capabilities(agent, scores: BenchmarkScore) -> None:
    """Test the agent's code analysis and generation capabilities."""
    logger.info("[TEST] Testing code analysis and generation")
    
    # First, create a file with buggy code for analysis
    buggy_code = """
def calculate_average(numbers):
    total = 0
    for num in numbers:
        total += num
    # Bug: Doesn't handle empty list case
    return total / len(numbers)

def find_max(numbers):
    # Bug: Doesn't initialize with first value
    max_value = 0
    for num in numbers:
        if num > max_value:
            max_value = num
    return max_value

# Bug: Incorrect function name
def find_minium(numbers):
    if not numbers:
        return None
    min_value = numbers[0]
    for num in numbers:
        if num < min_value:
            min_value = num
    return min_value
    """
    
    with open('buggy_code.py', 'w') as f:
        f.write(buggy_code)
    
    # Test code analysis
    logger.info("[TEST] Testing code analysis")
    
    analysis_prompt = """
    Analyze the Python file 'buggy_code.py' and identify all bugs and issues.
    For each issue:
    1. Describe what the bug is
    2. Explain why it's problematic
    3. Suggest a fix
    """
    
    start_time = time.time()
    analysis_success, analysis_result, analysis_history = agent.run(analysis_prompt)
    analysis_time = time.time() - start_time
    
    # Score code analysis (0-100)
    analysis_score = 0.0
    if analysis_success:
        analysis_score += 40.0  # Base score for success
        
        # Check if all three bugs were identified
        result_lower = analysis_result.lower()
        bugs_identified = 0
        
        if 'empty list' in result_lower or 'zero division' in result_lower:
            bugs_identified += 1
        
        if 'max_value = 0' in result_lower or 'initialize max' in result_lower:
            bugs_identified += 1
        
        if 'minium' in result_lower or 'function name' in result_lower or 'typo' in result_lower:
            bugs_identified += 1
        
        analysis_score += 20.0 * (bugs_identified / 3)  # Points for bug identification
        
        # Check if fixes were suggested
        if 'try' in result_lower and 'except' in result_lower or 'if not numbers' in result_lower:
            analysis_score += 20.0  # Solution for empty list
        
        if 'max_value = numbers[0]' in result_lower:
            analysis_score += 10.0  # Solution for max initialization
        
        if 'minimum' in result_lower or 'rename' in result_lower:
            analysis_score += 10.0  # Solution for typo
    
    scores.code_analysis = analysis_score
    scores.execution_times["code_analysis"] = analysis_time
    
    logger.info(f"Code analysis score: {analysis_score:.1f}/100")
    
    # Test code generation
    logger.info("[TEST] Testing code generation")
    
    generation_prompt = """
    Create a Python class called 'DataAnalyzer' that:
    1. Takes a list of numerical data in the constructor
    2. Has methods to calculate mean, median, and mode
    3. Has a method to plot a histogram of the data (using matplotlib)
    4. Includes proper error handling and input validation
    5. Has well-documented docstrings following PEP 257
    """
    
    start_time = time.time()
    generation_success, generation_result, generation_history = agent.run(generation_prompt)
    generation_time = time.time() - start_time
    
    # Score code generation (0-100)
    generation_score = 0.0
    if generation_success:
        generation_score += 30.0  # Base score for success
        
        # Look for created file
        code_file = None
        for entry in generation_history:
            if hasattr(entry, 'request') and hasattr(entry['request'], 'action'):
                action = entry['request'].action
                if hasattr(action, 'tool_name') and action.tool_name == 'create_file':
                    if hasattr(action, 'params') and 'path' in action.params:
                        path = action.params['path']
                        if path.endswith('.py'):
                            code_file = path
                            break
        
        if code_file and os.path.exists(code_file):
            # Check code quality and features
            try:
                with open(code_file, 'r') as f:
                    content = f.read().lower()
                    
                    # Class structure (10 points)
                    if 'class dataanalyzer' in content:
                        generation_score += 10.0
                    
                    # Required methods (30 points)
                    methods_checks = [
                        'def __init__' in content,
                        'def mean' in content or 'def calculate_mean' in content,
                        'def median' in content or 'def calculate_median' in content,
                        'def mode' in content or 'def calculate_mode' in content,
                        'def plot' in content or 'def histogram' in content
                    ]
                    generation_score += 6.0 * sum(methods_checks)
                    
                    # Error handling (10 points)
                    if ('try' in content and 'except' in content) or ('if not' in content):
                        generation_score += 10.0
                    
                    # Docstrings (10 points)
                    if '"""' in content or "'''" in content:
                        generation_score += 10.0
                    
                    # Imports (10 points)
                    if 'import matplotlib' in content or 'from matplotlib' in content:
                        generation_score += 10.0
            except:
                pass
    
    scores.code_generation = generation_score
    scores.execution_times["code_generation"] = generation_time
    
    logger.info(f"Code generation score: {generation_score:.1f}/100")

def test_project_structure(agent, scores: BenchmarkScore) -> None:
    """Test the agent's ability to create proper project structures."""
    logger.info("[TEST] Testing project structure creation")
    
    # Create a temporary directory for this test
    with tempfile.TemporaryDirectory() as tmp_dir:
        os.chdir(tmp_dir)
        
        prompt = """
        Create a properly structured Python package called 'dataprocessor' with:
        1. Standard package structure (src, tests, docs)
        2. A setup.py file for installation
        3. A README.md with installation and usage instructions
        4. A basic implementation with a main module
        5. A test file demonstrating the functionality
        6. A requirements.txt file listing dependencies
        
        The package should provide basic data processing functions like loading CSV files,
        filtering data, and calculating statistics.
        """
        
        start_time = time.time()
        success, result, history = agent.run(prompt)
        execution_time = time.time() - start_time
        
        # Score project structure (0-100)
        score = 0.0
        if success:
            score += 30.0  # Base score for success
            
            # Check directory structure (30 points)
            dir_checks = [
                os.path.exists('dataprocessor'),
                os.path.exists(os.path.join('dataprocessor', 'src')) or 
                    os.path.exists(os.path.join('dataprocessor', 'dataprocessor')),
                os.path.exists(os.path.join('dataprocessor', 'tests')),
                os.path.exists(os.path.join('dataprocessor', 'docs')) or
                    os.path.exists(os.path.join('dataprocessor', 'README.md'))
            ]
            score += 7.5 * sum(dir_checks)
            
            # Check for required files (40 points)
            file_checks = [
                os.path.exists(os.path.join('dataprocessor', 'setup.py')),
                os.path.exists(os.path.join('dataprocessor', 'README.md')),
                os.path.exists(os.path.join('dataprocessor', 'requirements.txt')),
                any(f.endswith('.py') for f in os.listdir(os.path.join('dataprocessor', 'src'))) 
                    if os.path.exists(os.path.join('dataprocessor', 'src')) else
                any(f.endswith('.py') for f in os.listdir(os.path.join('dataprocessor', 'dataprocessor')))
                    if os.path.exists(os.path.join('dataprocessor', 'dataprocessor')) else False,
                any(f.endswith('.py') for f in os.listdir(os.path.join('dataprocessor', 'tests')))
                    if os.path.exists(os.path.join('dataprocessor', 'tests')) else False
            ]
            score += 8.0 * sum(file_checks)
    
    scores.project_structure = score
    scores.execution_times["project_structure"] = execution_time
    
    logger.info(f"Project structure score: {score:.1f}/100")
    logger.info(f"Execution time: {execution_time:.2f} seconds")
    
    # Make sure we return to the original directory
    os.chdir(os.path.dirname(os.path.abspath(__file__)))

def test_error_handling(agent, scores: BenchmarkScore) -> None:
    """Test the agent's error handling capabilities."""
    logger.info("[TEST] Testing error handling capabilities")
    
    # Deliberate error cases to test robustness
    prompts = [
        # Missing file
        "Read the content of 'non_existent_file.txt' and summarize it.",
        
        # Invalid syntax
        "Fix the following Python code:\n```\nif True print('Hello')\n```",
        
        # Contradictory request
        "Create a file named 'test.txt' with 'Hello' content, but don't create any files."
    ]
    
    total_score = 0.0
    total_time = 0.0
    
    for i, prompt in enumerate(prompts):
        logger.info(f"[TEST] Testing error case {i+1}")
        
        start_time = time.time()
        success, result, history = agent.run(prompt)
        execution_time = time.time() - start_time
        total_time += execution_time
        
        # Score based on how well the agent handled the error
        # Rather than success/failure
        case_score = 0.0
        
        # Did it recognize there was an issue?
        if 'error' in result.lower() or 'cannot' in result.lower() or 'not found' in result.lower() or 'doesn\'t exist' in result.lower():
            case_score += 40.0  # Recognized the problem
        
        # Did it provide a helpful explanation?
        if 'because' in result.lower() or 'reason' in result.lower():
            case_score += 30.0  # Explained the issue
        
        # Did it suggest an alternative or workaround?
        if 'instead' in result.lower() or 'could' in result.lower() or 'suggest' in result.lower() or 'alternative' in result.lower():
            case_score += 30.0  # Offered a solution
            
        logger.info(f"Error case {i+1} score: {case_score:.1f}/100")
        total_score += case_score
    
    # Average the scores
    final_score = total_score / len(prompts)
    scores.error_handling = final_score
    scores.execution_times["error_handling"] = total_time
    
    logger.info(f"Error handling score: {final_score:.1f}/100")
    logger.info(f"Total execution time: {total_time:.2f} seconds")

def generate_report(scores: BenchmarkScore, output_dir: str) -> None:
    """Generate benchmark reports in both JSON and Markdown formats."""
    # Ensure the output directory exists
    os.makedirs(output_dir, exist_ok=True)
    
    # Generate JSON report
    json_path = os.path.join(output_dir, 'agent_benchmark_results.json')
    with open(json_path, 'w') as f:
        json.dump(scores.to_dict(), f, indent=2)
    
    # Generate Markdown report
    md_path = os.path.join(output_dir, 'agent_benchmark_results.md')
    with open(md_path, 'w') as f:
        f.write("# MindLink Agent Capability Benchmark Results\n\n")
        f.write(f"**Test Date:** {time.strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        f.write("## Overall Score\n\n")
        f.write(f"**{scores.get_average_score():.1f}/100**\n\n")
        
        f.write("## Core Capabilities\n\n")
        f.write(f"- **LLM Integration:** {scores.llm_integration:.1f}/100\n")
        f.write(f"- **Command Decomposition:** {scores.command_decomposition:.1f}/100\n")
        f.write(f"- **Tool Execution:** {scores.tool_execution:.1f}/100\n")
        f.write(f"- **Multi-step Planning:** {scores.multi_step_planning:.1f}/100\n\n")
        
        f.write("## File Operations\n\n")
        f.write(f"- **File Creation:** {scores.file_creation:.1f}/100\n")
        f.write(f"- **File Reading:** {scores.file_reading:.1f}/100\n")
        f.write(f"- **File Editing:** {scores.file_editing:.1f}/100\n\n")
        
        f.write("## Code Operations\n\n")
        f.write(f"- **Code Analysis:** {scores.code_analysis:.1f}/100\n")
        f.write(f"- **Code Generation:** {scores.code_generation:.1f}/100\n\n")
        
        f.write("## Project Management\n\n")
        f.write(f"- **Project Structure:** {scores.project_structure:.1f}/100\n")
        f.write(f"- **Error Handling:** {scores.error_handling:.1f}/100\n\n")
        
        f.write("## Performance Metrics\n\n")
        f.write("| Test | Execution Time (s) |\n")
        f.write("|------|-------------------|\n")
        for test, time_taken in scores.execution_times.items():
            f.write(f"| {test.replace('_', ' ').title()} | {time_taken:.2f} |\n")
        
        f.write("\n## Benchmark Details\n\n")
        f.write("This benchmark evaluates the MindLink agent's capabilities on a standardized 0-100 scale, ")
        f.write("which can be used to compare with commercial coding agents. Higher scores indicate better performance.\n\n")
        
        f.write("Each capability is tested with specific tasks designed to measure both correctness and efficiency. ")
        f.write("Scores reflect the agent's ability to understand requirements, execute tools correctly, and produce high-quality results.")
        
    logger.info(f"Benchmark reports generated in {output_dir}")
    logger.info(f"Overall benchmark score: {scores.get_average_score():.1f}/100")

def run_agent_benchmark():
    """Run the complete agent capability benchmark suite."""
    print("\n=== MindLink Agent Capability Benchmark ===\n")
    
    # Create output directory
    output_dir = "agent_benchmark_results"
    os.makedirs(output_dir, exist_ok=True)
    
    # Initialize scores
    scores = BenchmarkScore()
    
    # Initialize agent
    agent = initialize_agent()
    if not agent:
        logger.error("[ERROR] Failed to initialize agent")
        return False
    
    try:
        # Core capabilities
        test_llm_integration(agent, scores)
        test_command_decomposition(agent, scores)
        test_tool_execution(agent, scores)
        test_multi_step_planning(agent, scores)
        
        # File operations
        test_file_operations(agent, scores)
        
        # Code capabilities
        test_code_capabilities(agent, scores)
        
        # Project management
        test_project_structure(agent, scores)
        test_error_handling(agent, scores)
        
        # Generate reports
        generate_report(scores, output_dir)
        
        return True
    except Exception as e:
        logger.error(f"Error during benchmark: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    if run_agent_benchmark():
        sys.exit(0)
    else:
        sys.exit(1) 