#!/usr/bin/env python3
"""
Test script to verify the failover mechanism works correctly.
"""

import os
import sys
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'mindlink'))

from mindlink.agent import AgentOS
from mindlink.config import DEFAULT_SYSTEM_PROMPT
from mindlink.models.openai import OpenAIModel
from mindlink.models.openrouter import OpenRouterModel

def test_failover():
    """Test the failover mechanism with different LLM configurations."""
    
    # Test with OpenAI as primary
    print("Testing with OpenAI as primary LLM...")
    try:
        openai_llm = OpenAIModel(
            api_key=os.getenv('OPENAI_API_KEY', 'test-key'),
            model='gpt-4-turbo',
            temperature=0.1,
            max_tokens=8192
        )
        
        agent = AgentOS(
            llm=openai_llm,
            system_prompt_template=DEFAULT_SYSTEM_PROMPT,
            enable_failover=True
        )
        
        print(f"Primary LLM: {agent.llm.__class__.__name__}")
        print(f"Failover LLMs available: {len(agent.failover_llms)}")
        
        for i, failover_llm in enumerate(agent.failover_llms):
            print(f"  Failover {i+1}: {failover_llm.__class__.__name__}")
        
        # Test a simple generation
        response = agent._generate_with_failover(
            "You are a helpful assistant.",
            "Say hello in one word.",
            []
        )
        print(f"Response: {response}")
        
    except Exception as e:
        print(f"Error with OpenAI primary: {e}")
    
    print("\n" + "="*50 + "\n")
    
    # Test with OpenRouter as primary
    print("Testing with OpenRouter as primary LLM...")
    try:
        openrouter_llm = OpenRouterModel(
            model_name='anthropic/claude-3.5-sonnet',
            api_key=os.getenv('OPENROUTER_API_KEY', 'test-key'),
            temperature=0.1,
            max_tokens=8192
        )
        
        agent = AgentOS(
            llm=openrouter_llm,
            system_prompt_template=DEFAULT_SYSTEM_PROMPT,
            enable_failover=True
        )
        
        print(f"Primary LLM: {agent.llm.__class__.__name__}")
        print(f"Failover LLMs available: {len(agent.failover_llms)}")
        
        for i, failover_llm in enumerate(agent.failover_llms):
            print(f"  Failover {i+1}: {failover_llm.__class__.__name__}")
        
        # Test a simple generation
        response = agent._generate_with_failover(
            "You are a helpful assistant.",
            "Say hello in one word.",
            []
        )
        print(f"Response: {response}")
        
    except Exception as e:
        print(f"Error with OpenRouter primary: {e}")
    
    print("\n" + "="*50 + "\n")
    
    # Test failover disabled
    print("Testing with failover disabled...")
    try:
        agent = AgentOS(
            llm=openai_llm,
            system_prompt_template=DEFAULT_SYSTEM_PROMPT,
            enable_failover=False
        )
        
        print(f"Primary LLM: {agent.llm.__class__.__name__}")
        print(f"Failover LLMs available: {len(agent.failover_llms)}")
        
    except Exception as e:
        print(f"Error with failover disabled: {e}")

if __name__ == "__main__":
    test_failover()