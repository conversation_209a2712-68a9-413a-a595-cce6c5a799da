"""
Ultimate Circular Import Verification Test for MindLink Agent Core.

This test is specifically designed to verify that the circular import issue
has been completely resolved and that the library can handle complex import
scenarios, including:

1. Dynamic module loading/unloading in various orders
2. Importing specific symbols from modules
3. Reloading modules multiple times
4. Accessing both HoverDocTool implementations
5. Stress testing with rapid module loading/unloading
6. Verifying tool functionality after complex import patterns
7. Testing with custom modules that import from multiple library modules
"""

import pytest
import sys
import importlib
import random
import gc
import os
import tempfile
import shutil
from types import ModuleType
from typing import Dict, List, Set, Any, Tuple


def test_comprehensive_import_patterns():
    """
    Test comprehensive import patterns to verify circular import resolution.
    
    This test tries various import patterns that would trigger circular imports
    if they existed, including importing modules in different orders, reloading
    modules, and importing specific symbols.
    """
    print("\n=== Testing Comprehensive Import Patterns ===")
    
    # Track loaded modules before test
    initial_modules = set(sys.modules.keys())
    
    # List of module paths to manipulate
    module_paths = [
        'mindlink.tools',
        'mindlink.tools.base',
        'mindlink.tools.file_tools',
        'mindlink.tools.knowledge_tools',
        'mindlink.tools.doc_tools',
        'mindlink.tools.graph_tools',
    ]
    
    # Remove modules if they're already loaded
    for module_path in module_paths:
        if module_path in sys.modules:
            del sys.modules[module_path]
    
    # Function to import a module and return it
    def import_module(module_path):
        try:
            return importlib.import_module(module_path)
        except ImportError as e:
            return f"ImportError: {str(e)}"
    
    # Test 1: Import modules in all possible permutations
    print("Test 1: Import modules in all possible permutations")
    
    # Generate a subset of permutations to keep test runtime reasonable
    import itertools
    permutations = list(itertools.permutations(module_paths))
    # Select a random subset of 10 permutations
    selected_permutations = random.sample(permutations, min(10, len(permutations)))
    
    for perm_idx, perm in enumerate(selected_permutations):
        print(f"  Permutation {perm_idx + 1}/{len(selected_permutations)}")
        
        # Clear modules
        for module_path in module_paths:
            if module_path in sys.modules:
                del sys.modules[module_path]
        
        # Import in the current permutation order
        results = {}
        for module_path in perm:
            results[module_path] = import_module(module_path)
        
        # Verify all imports succeeded
        for module_path, result in results.items():
            assert not isinstance(result, str), f"Failed to import {module_path}: {result}"
            assert isinstance(result, ModuleType), f"Expected module, got {type(result)}"
    
    # Test 2: Import specific symbols directly
    print("Test 2: Import specific symbols directly")
    
    # Clear modules
    for module_path in module_paths:
        if module_path in sys.modules:
            del sys.modules[module_path]
    
    # Import specific symbols that might be involved in circular imports
    from mindlink.tools.base import Tool, tool_registry
    from mindlink.tools.file_tools import TransactionManager, create_file, read_file
    from mindlink.tools.file_tools import CreateFileTool, ReadFileTool
    from mindlink.tools import HoverDocTool, KnowledgeHoverDocTool, DocHoverDocTool
    
    # Verify imports succeeded
    assert Tool is not None
    assert tool_registry is not None
    assert TransactionManager is not None
    assert create_file is not None
    assert read_file is not None
    assert CreateFileTool is not None
    assert ReadFileTool is not None
    assert HoverDocTool is not None
    assert KnowledgeHoverDocTool is not None
    assert DocHoverDocTool is not None
    
    # Test 3: Reload modules multiple times
    print("Test 3: Reload modules multiple times")
    
    modules = {}
    for module_path in module_paths:
        modules[module_path] = import_module(module_path)
    
    # Reload each module multiple times in different orders
    for _ in range(5):
        reload_order = list(modules.keys())
        random.shuffle(reload_order)
        for module_path in reload_order:
            modules[module_path] = importlib.reload(modules[module_path])
            assert isinstance(modules[module_path], ModuleType)
    
    # Test 4: Verify both HoverDocTool implementations are accessible
    print("Test 4: Verify both HoverDocTool implementations are accessible")
    
    # Clear modules
    for module_path in module_paths:
        if module_path in sys.modules:
            del sys.modules[module_path]
    
    # Import both HoverDocTool implementations
    from mindlink.tools import HoverDocTool, KnowledgeHoverDocTool, DocHoverDocTool
    
    # Verify they are different classes
    assert HoverDocTool is not None
    assert KnowledgeHoverDocTool is not None
    assert DocHoverDocTool is not None
    assert HoverDocTool.__name__ == "HoverDocTool"
    assert KnowledgeHoverDocTool.__name__ == "HoverDocTool"
    assert DocHoverDocTool.__name__ == "HoverDocTool"
    assert HoverDocTool is DocHoverDocTool  # Default should be DocHoverDocTool
    assert KnowledgeHoverDocTool is not DocHoverDocTool  # Should be different classes
    
    # Test 5: Stress test with rapid module loading/unloading
    print("Test 5: Stress test with rapid module loading/unloading")
    
    for _ in range(10):
        # Clear modules
        for module_path in module_paths:
            if module_path in sys.modules:
                del sys.modules[module_path]
        
        # Force garbage collection
        gc.collect()
        
        # Import a random module
        random_module = random.choice(module_paths)
        module = import_module(random_module)
        assert isinstance(module, ModuleType)
        
        # Import a specific symbol from a random module
        if random_module == 'mindlink.tools':
            from mindlink.tools import HoverDocTool
            assert HoverDocTool is not None
        elif random_module == 'mindlink.tools.base':
            from mindlink.tools.base import Tool
            assert Tool is not None
        elif random_module == 'mindlink.tools.file_tools':
            from mindlink.tools.file_tools import CreateFileTool
            assert CreateFileTool is not None
        elif random_module == 'mindlink.tools.knowledge_tools':
            from mindlink.tools.knowledge_tools import HoverDocTool
            assert HoverDocTool is not None
        elif random_module == 'mindlink.tools.doc_tools':
            from mindlink.tools.doc_tools import HoverDocTool
            assert HoverDocTool is not None
        elif random_module == 'mindlink.tools.graph_tools':
            from mindlink.tools.graph_tools import GenerateCallGraphTool
            assert GenerateCallGraphTool is not None
    
    print("All comprehensive import pattern tests passed!")
    
    # Clean up - restore initial module state
    current_modules = set(sys.modules.keys())
    new_modules = current_modules - initial_modules
    for module_name in new_modules:
        if module_name.startswith('mindlink.'):
            if module_name in sys.modules:
                del sys.modules[module_name]


def test_custom_module_with_complex_imports():
    """
    Test creating a custom module that imports from multiple library modules.
    
    This test verifies that a custom module can import from multiple library
    modules without triggering circular imports.
    """
    print("\n=== Testing Custom Module with Complex Imports ===")
    
    # Create a temporary directory
    temp_dir = tempfile.mkdtemp()
    try:
        # Create a custom module
        custom_module_dir = os.path.join(temp_dir, "custom_module")
        os.makedirs(custom_module_dir, exist_ok=True)
        
        # Create __init__.py
        with open(os.path.join(custom_module_dir, "__init__.py"), "w") as f:
            f.write("# Custom module package\n")
        
        # Create a module that imports from multiple library modules
        with open(os.path.join(custom_module_dir, "complex_imports.py"), "w") as f:
            f.write("""
# Module that imports from multiple library modules

# Import from base
from mindlink.tools.base import Tool, tool_registry, register_tool

# Import from file_tools
from mindlink.tools.file_tools import (
    TransactionManager,
    create_file,
    read_file,
    path_exists,
    CreateFileTool,
    ReadFileTool,
    ListFilesTool
)

# Import from knowledge_tools and doc_tools
from mindlink.tools import HoverDocTool, KnowledgeHoverDocTool, DocHoverDocTool

# Import from graph_tools
from mindlink.tools.graph_tools import GenerateCallGraphTool

# Define a custom tool that uses components from multiple modules
@register_tool
class ComplexTool(Tool):
    \"\"\"Tool that uses components from multiple modules.\"\"\"
    name = "complex_tool"
    description = "A tool that uses components from multiple modules."
    
    class Parameters:
        path: str
        content: str = None
    
    def execute(self, **parameters):
        path = parameters.get("path")
        content = parameters.get("content")
        
        try:
            # Use TransactionManager from file_tools
            with TransactionManager():
                # Use create_file from file_tools
                create_file(path, content)
            
            # Use path_exists from file_tools
            if path_exists(path):
                # Use read_file from file_tools
                result = read_file(path)
                return {
                    "observation": result,
                    "status": "success"
                }
            else:
                return {
                    "observation": "File not created",
                    "status": "error",
                    "error": "File not created"
                }
        except Exception as e:
            return {
                "observation": f"Error: {str(e)}",
                "status": "error",
                "error": str(e)
            }

# Function that uses both HoverDocTool implementations
def use_hover_tools():
    # Create instances of both HoverDocTool implementations
    default_tool = HoverDocTool()
    knowledge_tool = KnowledgeHoverDocTool()
    doc_tool = DocHoverDocTool()
    
    # Verify they are different instances
    assert default_tool.__class__ is doc_tool.__class__
    assert knowledge_tool.__class__ is not doc_tool.__class__
    
    return {
        "default": default_tool.__class__.__name__,
        "knowledge": knowledge_tool.__class__.__name__,
        "doc": doc_tool.__class__.__name__
    }
""")
        
        # Add the temp directory to sys.path
        sys.path.insert(0, temp_dir)
        
        try:
            # Import the custom module
            import custom_module.complex_imports
            
            # Verify the module imported successfully
            assert hasattr(custom_module.complex_imports, "ComplexTool")
            assert hasattr(custom_module.complex_imports, "use_hover_tools")
            
            # Verify the tool was registered
            from mindlink.tools.base import tool_registry
            assert "complex_tool" in tool_registry
            
            # Use the function that uses both HoverDocTool implementations
            result = custom_module.complex_imports.use_hover_tools()
            assert result["default"] == "HoverDocTool"
            assert result["knowledge"] == "HoverDocTool"
            assert result["doc"] == "HoverDocTool"
            
            # Create a temporary file to test the tool
            test_file = os.path.join(temp_dir, "test_file.txt")
            test_content = "Test content"
            
            # Use the complex tool
            complex_tool = tool_registry["complex_tool"]()
            result = complex_tool.execute(path=test_file, content=test_content)
            
            # Verify the tool worked
            assert result["status"] == "success"
            assert result["observation"] == test_content
            
            print("Custom module with complex imports test passed!")
        
        finally:
            # Clean up sys.path
            if temp_dir in sys.path:
                sys.path.remove(temp_dir)
            
            # Clean up imported modules
            if "custom_module.complex_imports" in sys.modules:
                del sys.modules["custom_module.complex_imports"]
            if "custom_module" in sys.modules:
                del sys.modules["custom_module"]
    
    finally:
        # Clean up
        shutil.rmtree(temp_dir)


def test_tool_functionality_after_complex_imports():
    """
    Test tool functionality after complex import patterns.
    
    This test verifies that tools still work correctly after complex
    import patterns that would trigger circular imports if they existed.
    """
    print("\n=== Testing Tool Functionality After Complex Imports ===")
    
    # Create a temporary directory
    temp_dir = tempfile.mkdtemp()
    try:
        # Clear all mindlink modules
        for module_name in list(sys.modules.keys()):
            if module_name.startswith('mindlink.'):
                del sys.modules[module_name]
        
        # Force garbage collection
        gc.collect()
        
        # Import modules in a specific order that would trigger circular imports
        import mindlink.tools.base
        import mindlink.tools.file_tools
        import mindlink.tools.doc_tools
        import mindlink.tools.knowledge_tools
        import mindlink.tools
        
        # Reload modules in reverse order
        importlib.reload(mindlink.tools)
        importlib.reload(mindlink.tools.knowledge_tools)
        importlib.reload(mindlink.tools.doc_tools)
        importlib.reload(mindlink.tools.file_tools)
        importlib.reload(mindlink.tools.base)
        
        # Import specific tools
        from mindlink.tools import (
            CreateFileTool,
            ReadFileTool,
            ListFilesTool,
            HoverDocTool,
            KnowledgeHoverDocTool,
            DocHoverDocTool
        )
        
        # Create and use tools
        create_tool = CreateFileTool()
        read_tool = ReadFileTool()
        list_tool = ListFilesTool()
        hover_tool = HoverDocTool()
        knowledge_hover_tool = KnowledgeHoverDocTool()
        doc_hover_tool = DocHoverDocTool()
        
        # Test file operations
        test_file = os.path.join(temp_dir, "test_file.txt")
        test_content = "Test content after complex imports"
        
        # Create a file
        result = create_tool.execute(path=test_file, content=test_content)
        assert result["status"] == "success"
        
        # Read the file
        result = read_tool.execute(path=test_file)
        assert result["status"] == "success"
        assert result["observation"] == test_content
        
        # List files
        result = list_tool.execute(directory=temp_dir)
        assert result["status"] == "success"
        assert "test_file.txt" in result["observation"]
        
        print("Tool functionality after complex imports test passed!")
    
    finally:
        # Clean up
        shutil.rmtree(temp_dir)


if __name__ == "__main__":
    # Run all tests
    test_comprehensive_import_patterns()
    test_custom_module_with_complex_imports()
    test_tool_functionality_after_complex_imports()
    print("\n=== All Ultimate Circular Import Verification Tests Passed! ===")
    print("The library is 100% ready for all new tests and commands.")
