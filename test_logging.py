import asyncio
import uuid
from typing import Optional, List, Dict, Any # Add Optional, List, Dict, Any here
from mindlink.agent import Agent<PERSON>
from mindlink.models.llm import LLMInterface
from mindlink.schemas.mindlink import MindLinkRequest, MindLinkResponse, Action
from mindlink.logging_utils import log_event, create_user_input_log, EventType
from mindlink.tools.base import Tool, ToolParameters, register_tool, tool_registry

# Mock LLM for testing
class MockLLM(LLMInterface):
    def __init__(self, model_name: str = "mock-model"):
        self.model_name = model_name

    async def query(self, system_prompt: str, user_prompt: str) -> str:
        # Simulate LLM returning a specific action
        action_json = {
            "action": {
                "tool_name": "mock_tool",
                "parameters": {"param1": "value1"}
            },
            "reasoning": "LL<PERSON> decided to use mock_tool.",
            "thought": "Thinking about using mock_tool."
        }
        return json.dumps(action_json)

    async def generate(self, system_prompt: str, user_prompt: str, tools: Optional[List[Dict[str, Any]]] = None, **kwargs) -> str:
        # Simple mock implementation for generate
        # This can be expanded if more complex generation logic is needed for tests
        return await self.query(system_prompt, user_prompt) # Reuse query logic for simplicity

    def get_model_name(self) -> str:
        return self.model_name

# Mock Tool for testing
@register_tool
class MockTool(Tool):
    name = "mock_tool"
    description = "A mock tool for testing purposes."

    class Parameters(ToolParameters):
        param1: str

    parameters_model = Parameters

    def execute(self, **parameters) -> dict:
        return {"observation": f"MockTool executed with {parameters}", "status": "success"}

async def main():
    print("Starting logging test script...")
    session_id = str(uuid.uuid4())
    print(f"Test Session ID: {session_id}")

    # 1. Log User Input (simulated)
    user_input_event = create_user_input_log(session_id, "Test user input for logging", "test_intent")
    log_event(user_input_event)
    print(f"Logged: {user_input_event.event_type}") # Changed from user_input_event.event_type.value

    # Setup AgentOS with MockLLM
    mock_llm = MockLLM()
    # A very basic system prompt template
    system_prompt = "You are a helpful assistant. Available tools: {tool_descriptions}"
    agent = AgentOS(llm=mock_llm, system_prompt_template=system_prompt)
    agent.session_id = session_id # Manually set session_id for this test

    goal = "Test the logging functionality by running a mock tool."

    try:
        print(f"Running agent with goal: {goal}")
        # The agent.run() method itself will trigger other logs (LLM_QUERY, LLM_RESPONSE, TOOL_CALL_START, TOOL_CALL_END, AGENT_GOAL_COMPLETED)
        final_response = await agent.run(goal)
        print(f"Agent finished. Final response: {final_response.observation}")
    except Exception as e:
        print(f"An error occurred during agent run: {e}")
        # Log error if agent.run fails
        from mindlink.logging_utils import create_error_occurred_log
        error_log = create_error_occurred_log(session_id, "AgentRun", str(e), "CRITICAL", traceback.format_exc())
        log_event(error_log)
        print(f"Logged: {error_log.event_type}") # Changed from error_log.event_type.value

    print("Logging test script finished.")
    print(f"Check the 'logs' directory in the project root (d:\\کتابخانه پایتون\\2-\\logs) for new log files.")

if __name__ == "__main__":
    # Ensure tool_registry is populated before agent runs
    # This would typically happen at module import time if tools are in a package structure
    # For this script, explicitly instantiate to register if not already.
    if "mock_tool" not in tool_registry:
        MockTool() # Instantiating it should call @register_tool
    
    # Need to import json for the mock LLM
    import json 
    import traceback
    asyncio.run(main())