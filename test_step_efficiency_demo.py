#!/usr/bin/env python3
"""
Demonstration script showing the improvement from fixed 15-step execution
to intelligent dynamic step determination in MindLink Agent.
"""

import sys
import os
sys.path.insert(0, os.path.dirname(__file__))

from mindlink.agent import AgentOS
from mindlink.schemas.mindlink import Action

def demonstrate_step_efficiency():
    """Demonstrate the efficiency improvements with dynamic step determination."""
    
    print("🚀 MindLink Agent Step Efficiency Demonstration")
    print("=" * 60)
    
    class MockLLM:
        def generate(self, *args, **kwargs):
            return "Mock response"
    
    # Create agent with new dynamic step logic
    agent = AgentOS(MockLLM(), "Test prompt", max_steps=25)
    
    # Test scenarios with different complexity levels
    test_scenarios = [
        {
            "goal": "create a simple Python file",
            "expected_old_steps": 15,  # Old behavior: always 15 steps
            "category": "Simple Task"
        },
        {
            "goal": "read file and show contents", 
            "expected_old_steps": 15,  # Old behavior: always 15 steps
            "category": "Simple Task"
        },
        {
            "goal": "create 3 Python files with different functions",
            "expected_old_steps": 15,  # Old behavior: always 15 steps
            "category": "Medium Task"
        },
        {
            "goal": "build a web application with Flask",
            "expected_old_steps": 15,  # Old behavior: always 15 steps
            "category": "Medium Task"
        },
        {
            "goal": "comprehensive project with database and API endpoints",
            "expected_old_steps": 15,  # Old behavior: always 15 steps
            "category": "Complex Task"
        },
        {
            "goal": "complete system with multiple components and authentication",
            "expected_old_steps": 15,  # Old behavior: always 15 steps
            "category": "Complex Task"
        }
    ]
    
    total_old_steps = 0
    total_new_steps = 0
    
    print("\n📊 Step Allocation Comparison:")
    print("-" * 60)
    print(f"{'Goal':<45} {'Old':<5} {'New':<5} {'Savings':<8}")
    print("-" * 60)
    
    for scenario in test_scenarios:
        goal = scenario["goal"]
        old_steps = scenario["expected_old_steps"]
        
        # Calculate new dynamic steps
        complexity = agent._analyze_goal_complexity(goal)
        new_steps = min(agent.max_steps, complexity)
        
        savings = old_steps - new_steps
        savings_pct = (savings / old_steps) * 100
        
        total_old_steps += old_steps
        total_new_steps += new_steps
        
        # Truncate goal for display
        display_goal = goal[:42] + "..." if len(goal) > 45 else goal
        
        print(f"{display_goal:<45} {old_steps:<5} {new_steps:<5} {savings} ({savings_pct:.0f}%)")
    
    print("-" * 60)
    print(f"{'TOTAL':<45} {total_old_steps:<5} {total_new_steps:<5} {total_old_steps - total_new_steps} ({((total_old_steps - total_new_steps) / total_old_steps) * 100:.0f}%)")
    
    print("\n🎯 Key Improvements:")
    print("=" * 40)
    print(f"• Total step reduction: {total_old_steps - total_new_steps} steps ({((total_old_steps - total_new_steps) / total_old_steps) * 100:.0f}% savings)")
    print(f"• Simple tasks now use {agent._analyze_goal_complexity('create a file')} steps instead of 15")
    print(f"• Medium tasks now use {agent._analyze_goal_complexity('build an app')} steps instead of 15")
    print(f"• Complex tasks use up to {agent._analyze_goal_complexity('comprehensive project')} steps (vs fixed 15)")
    print(f"• Upper bound increased to {agent.max_steps} for truly complex tasks")
    
    print("\n🧠 Intelligent Features:")
    print("=" * 40)
    print("• Goal complexity analysis determines optimal step count")
    print("• Early completion detection stops unnecessary planning")
    print("• Pattern matching identifies task types automatically")
    print("• Adaptive execution based on actual requirements")
    
    print("\n⚡ Performance Benefits:")
    print("=" * 40)
    print("• Faster execution for simple tasks")
    print("• Reduced computational overhead")
    print("• Better resource utilization")
    print("• More responsive user experience")
    print("• Eliminates unnecessary planning iterations")
    
    return total_old_steps, total_new_steps

def test_early_completion_detection():
    """Test the early completion detection feature."""
    
    print("\n🎯 Early Completion Detection Test")
    print("=" * 40)
    
    class MockLLM:
        def generate(self, *args, **kwargs):
            return "Mock response"
    
    agent = AgentOS(MockLLM(), "Test prompt", max_steps=25)
    
    # Simulate completion scenarios
    completion_tests = [
        {
            "goal": "create one file",
            "actions": [Action(tool_name="create_file", parameters={"path": "test.py"})],
            "next_action": Action(tool_name="echo", parameters={}),
            "should_complete": True
        },
        {
            "goal": "read the configuration file",
            "actions": [Action(tool_name="read_file", parameters={"path": "config.txt"})],
            "next_action": Action(tool_name="echo", parameters={}),
            "should_complete": True
        },
        {
            "goal": "create 2 files",
            "actions": [Action(tool_name="create_file", parameters={"path": "file1.py"})],
            "next_action": Action(tool_name="create_file", parameters={"path": "file2.py"}),
            "should_complete": False  # Only 1 of 2 files created
        }
    ]
    
    for i, test in enumerate(completion_tests, 1):
        is_completed = agent._is_goal_likely_completed(
            test["goal"], 
            test["actions"], 
            test["next_action"]
        )
        
        status = "✅ COMPLETE" if is_completed else "⏳ CONTINUE"
        expected = "✅ COMPLETE" if test["should_complete"] else "⏳ CONTINUE"
        
        print(f"Test {i}: {test['goal']}")
        print(f"  Actions completed: {len(test['actions'])}")
        print(f"  Detection result: {status}")
        print(f"  Expected result:  {expected}")
        
        if is_completed == test["should_complete"]:
            print(f"  ✅ CORRECT")
        else:
            print(f"  ❌ INCORRECT")
        print()

if __name__ == "__main__":
    try:
        old_total, new_total = demonstrate_step_efficiency()
        test_early_completion_detection()
        
        print("\n" + "=" * 60)
        print("🎉 DYNAMIC STEP DETERMINATION SUCCESSFULLY IMPLEMENTED!")
        print("=" * 60)
        print("\nThe agent now intelligently determines the optimal number of steps")
        print("based on goal complexity instead of always using 15 steps.")
        print("\nThis results in:")
        print(f"• {((old_total - new_total) / old_total) * 100:.0f}% reduction in unnecessary planning steps")
        print("• Faster execution for simple tasks")
        print("• Better resource utilization")
        print("• More responsive user experience")
        
    except Exception as e:
        print(f"\n❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)