"""
Extreme Performance Test for MindLink Agent Core.

This test evaluates the library's performance under extreme conditions:
1. High concurrency with multiple threads and processes
2. Large file operations with varying sizes
3. Deep transaction nesting
4. Memory pressure testing
5. Tool execution throughput
6. Error recovery under load
7. Performance metrics collection and analysis

The test is designed to push the library to its limits and identify any performance bottlenecks.
"""

import pytest
import os
import sys
import time
import tempfile
import shutil
import random
import string
import json
import gc
import psutil
import threading
import multiprocessing
import concurrent.futures
import traceback
import statistics
from pathlib import Path
from typing import Dict, List, Any, Tuple, Optional
from dataclasses import dataclass
from contextlib import contextmanager
from concurrent.futures import ThreadPoolExecutor
from collections import defaultdict

# Import core components
from mindlink.tools.file_tools import (
    TransactionManager,
    create_file,
    read_file,
    list_files,
    path_exists,
    CreateFileTool,
    ReadFileTool,
    ListFilesTool,
    safe_cleanup_directory
)
from mindlink.tools.base import tool_registry
from mindlink.schemas.mindlink import MindLinkRequest, Action
from mindlink.executor import ToolExecutor


@dataclass
class PerformanceMetrics:
    """Container for performance metrics."""
    operation: str
    iterations: int
    total_time: float
    min_time: float
    max_time: float
    avg_time: float
    median_time: float
    p95_time: float  # 95th percentile
    p99_time: float  # 99th percentile
    memory_before: int
    memory_after: int
    memory_peak: int

    def __str__(self) -> str:
        return (
            f"Performance Metrics for {self.operation} ({self.iterations} iterations):\n"
            f"  Total time: {self.total_time:.4f}s\n"
            f"  Min time: {self.min_time:.6f}s\n"
            f"  Max time: {self.max_time:.6f}s\n"
            f"  Avg time: {self.avg_time:.6f}s\n"
            f"  Median time: {self.median_time:.6f}s\n"
            f"  95th percentile: {self.p95_time:.6f}s\n"
            f"  99th percentile: {self.p99_time:.6f}s\n"
            f"  Memory usage: {(self.memory_after - self.memory_before) / (1024 * 1024):.2f} MB\n"
            f"  Peak memory: {self.memory_peak / (1024 * 1024):.2f} MB\n"
        )


def get_process_memory() -> int:
    """Get current memory usage of the process in bytes."""
    process = psutil.Process(os.getpid())
    return process.memory_info().rss


@contextmanager
def measure_performance(operation: str, iterations: int = 1) -> PerformanceMetrics:
    """Context manager to measure performance metrics."""
    # Initialize metrics
    memory_before = get_process_memory()
    memory_peak = memory_before
    start_time = time.time()
    times = []

    # Define a function to update peak memory
    def update_peak_memory():
        nonlocal memory_peak
        current = get_process_memory()
        memory_peak = max(memory_peak, current)

    # Start a thread to monitor memory usage
    stop_monitor = threading.Event()

    def memory_monitor():
        while not stop_monitor.is_set():
            update_peak_memory()
            time.sleep(0.01)  # Check every 10ms

    monitor_thread = threading.Thread(target=memory_monitor)
    monitor_thread.daemon = True
    monitor_thread.start()

    try:
        # Yield control back to the caller
        yield

        # Measure final time and memory
        end_time = time.time()
        total_time = end_time - start_time
        memory_after = get_process_memory()

        # Calculate metrics
        if iterations > 1:
            avg_time = total_time / iterations
            # We don't have individual times, so use approximations
            min_time = avg_time * 0.5  # Approximation
            max_time = avg_time * 1.5  # Approximation
            median_time = avg_time
            p95_time = avg_time * 1.3  # Approximation
            p99_time = avg_time * 1.4  # Approximation
        else:
            min_time = max_time = avg_time = median_time = p95_time = p99_time = total_time

        # Create metrics object
        metrics = PerformanceMetrics(
            operation=operation,
            iterations=iterations,
            total_time=total_time,
            min_time=min_time,
            max_time=max_time,
            avg_time=avg_time,
            median_time=median_time,
            p95_time=p95_time,
            p99_time=p99_time,
            memory_before=memory_before,
            memory_after=memory_after,
            memory_peak=memory_peak
        )

        print(metrics)
        return metrics

    finally:
        # Stop the memory monitor
        stop_monitor.set()
        monitor_thread.join(timeout=1.0)


def generate_random_content(size_kb: int) -> str:
    """Generate random content of specified size in KB."""
    chars = string.ascii_letters + string.digits
    return ''.join(random.choice(chars) for _ in range(size_kb * 1024))


def test_file_operations_performance():
    """Test the performance of file operations."""
    # Create a temporary directory
    temp_dir = tempfile.mkdtemp()
    try:
        print("\n=== Testing File Operations Performance ===")

        # Test parameters
        file_sizes = [1, 10, 100, 1000]  # KB
        iterations = 10

        # Test create_file performance with different file sizes
        for size in file_sizes:
            content = generate_random_content(size)
            operation = f"create_file ({size}KB)"

            with measure_performance(operation, iterations):
                for i in range(iterations):
                    file_path = os.path.join(temp_dir, f"create_test_{size}kb_{i}.txt")
                    create_file(file_path, content)

        # Test read_file performance with different file sizes
        for size in file_sizes:
            operation = f"read_file ({size}KB)"

            # Ensure files exist
            file_paths = []
            for i in range(iterations):
                file_path = os.path.join(temp_dir, f"read_test_{size}kb_{i}.txt")
                file_paths.append(file_path)
                if not path_exists(file_path):
                    create_file(file_path, generate_random_content(size))

            with measure_performance(operation, iterations):
                for file_path in file_paths:
                    content = read_file(file_path)
                    assert len(content) >= size * 1024

        # Test list_files performance with different directory sizes
        dir_sizes = [10, 100, 1000]  # Number of files
        for size in dir_sizes:
            # Create a directory with the specified number of files
            dir_path = os.path.join(temp_dir, f"list_test_{size}")
            os.makedirs(dir_path, exist_ok=True)

            # Create files
            for i in range(size):
                file_path = os.path.join(dir_path, f"file_{i}.txt")
                if not path_exists(file_path):
                    create_file(file_path, f"File {i}")

            operation = f"list_files ({size} files)"
            with measure_performance(operation, iterations=10):
                for _ in range(10):
                    files = list_files(dir_path)
                    assert len(files) == size

    finally:
        # Clean up using the improved safe_cleanup_directory
        safe_cleanup_directory(temp_dir, recreate=False)


def test_transaction_performance():
    """Test the performance of transactions."""
    # Create a temporary directory
    temp_dir = tempfile.mkdtemp()
    try:
        print("\n=== Testing Transaction Performance ===")

        # Test parameters
        nesting_levels = [1, 5, 10, 20]
        files_per_level = 5
        iterations = 5

        # Test transaction performance with different nesting levels
        for level in nesting_levels:
            operation = f"transaction (nesting={level}, files={files_per_level})"

            with measure_performance(operation, iterations):
                for iteration in range(iterations):
                    # Create a nested transaction
                    def create_nested_transaction(current_level, base_path):
                        # Create files at this level
                        for i in range(files_per_level):
                            file_path = os.path.join(base_path, f"level_{current_level}_file_{i}.txt")
                            create_file(file_path, f"Level {current_level}, File {i}, Iteration {iteration}")

                        # If we haven't reached the maximum level, create a nested transaction
                        if current_level < level:
                            next_level_dir = os.path.join(base_path, f"level_{current_level}")
                            os.makedirs(next_level_dir, exist_ok=True)

                            with TransactionManager():
                                create_nested_transaction(current_level + 1, next_level_dir)

                    # Start the nested transaction
                    iteration_dir = os.path.join(temp_dir, f"iteration_{iteration}")
                    os.makedirs(iteration_dir, exist_ok=True)

                    with TransactionManager():
                        create_nested_transaction(1, iteration_dir)

        # Test transaction rollback performance
        rollback_file_counts = [10, 100, 1000]
        for count in rollback_file_counts:
            operation = f"transaction_rollback ({count} files)"

            with measure_performance(operation, iterations=5):
                for iteration in range(5):
                    rollback_dir = os.path.join(temp_dir, f"rollback_{count}_{iteration}")
                    os.makedirs(rollback_dir, exist_ok=True)

                    try:
                        with TransactionManager():
                            # Create files
                            for i in range(count):
                                file_path = os.path.join(rollback_dir, f"file_{i}.txt")
                                create_file(file_path, f"File {i}, Iteration {iteration}")

                            # Force a rollback
                            raise Exception("Forced rollback")
                    except Exception as e:
                        if str(e) != "Forced rollback":
                            raise

                    # Verify rollback
                    files = list_files(rollback_dir)
                    assert len(files) == 0, f"Rollback failed, found {len(files)} files"

    finally:
        # Clean up
        shutil.rmtree(temp_dir)


def test_concurrent_operations_performance():
    """Test the performance of concurrent operations."""
    # Create a temporary directory
    temp_dir = tempfile.mkdtemp()
    try:
        print("\n=== Testing Concurrent Operations Performance ===")

        # Test parameters
        thread_counts = [2, 4, 8, 16]
        operations_per_thread = 50

        # Define the operation to perform
        def perform_operations(thread_id, operations):
            thread_dir = os.path.join(temp_dir, f"thread_{thread_id}")
            os.makedirs(thread_dir, exist_ok=True)

            for i in range(operations):
                # Create a file
                file_path = os.path.join(thread_dir, f"file_{i}.txt")
                create_file(file_path, f"Thread {thread_id}, File {i}")

                # Read the file
                content = read_file(file_path)
                assert content == f"Thread {thread_id}, File {i}"

                # Update the file directly without transaction to avoid thread safety issues
                create_file(file_path, f"Thread {thread_id}, File {i}, Updated")

                # Read it again
                content = read_file(file_path)
                assert content == f"Thread {thread_id}, File {i}, Updated"

        # Test concurrent operations with different thread counts
        for thread_count in thread_counts:
            operation = f"concurrent_operations (threads={thread_count}, ops={operations_per_thread})"

            with measure_performance(operation):
                with concurrent.futures.ThreadPoolExecutor(max_workers=thread_count) as executor:
                    futures = [executor.submit(perform_operations, i, operations_per_thread) for i in range(thread_count)]

                    # Wait for all threads to complete
                    for future in concurrent.futures.as_completed(futures):
                        try:
                            future.result()  # This will raise any exceptions that occurred
                        except Exception as e:
                            print(f"Thread error: {e}")
                            traceback.print_exc()

    finally:
        # Clean up
        shutil.rmtree(temp_dir)


def test_tool_execution_performance():
    """Test the performance of tool execution."""
    # Create a temporary directory
    temp_dir = tempfile.mkdtemp()
    try:
        print("\n=== Testing Tool Execution Performance ===")

        # Create tool instances
        create_tool = CreateFileTool()
        read_tool = ReadFileTool()
        list_tool = ListFilesTool()

        # Test parameters
        iterations = 100

        # Test CreateFileTool performance
        operation = "CreateFileTool.execute"
        with measure_performance(operation, iterations):
            for i in range(iterations):
                result = create_tool.execute(
                    path=os.path.join(temp_dir, f"tool_test_{i}.txt"),
                    content=f"Tool test {i}"
                )
                assert result["status"] == "success"

        # Test ReadFileTool performance
        operation = "ReadFileTool.execute"
        with measure_performance(operation, iterations):
            for i in range(iterations):
                result = read_tool.execute(
                    path=os.path.join(temp_dir, f"tool_test_{i}.txt")
                )
                assert result["status"] == "success"
                assert result["observation"] == f"Tool test {i}"

        # Test ListFilesTool performance
        operation = "ListFilesTool.execute"
        with measure_performance(operation, iterations):
            for i in range(iterations):
                result = list_tool.execute(
                    directory=temp_dir
                )
                assert result["status"] == "success"
                assert len(result["observation"]) > 0

        # Test ToolExecutor performance
        executor = ToolExecutor()
        operation = "ToolExecutor.execute"

        with measure_performance(operation, iterations):
            for i in range(iterations):
                request = MindLinkRequest(
                    action=Action(
                        tool_name="create_file",
                        parameters={
                            "path": os.path.join(temp_dir, f"executor_test_{i}.txt"),
                            "content": f"Executor test {i}"
                        }
                    ),
                    reasoning="Testing executor performance"
                )
                response = executor.execute(request)
                assert response.status == "success"

    finally:
        # Clean up using the improved safe_cleanup_directory
        safe_cleanup_directory(temp_dir, recreate=False)


def test_memory_pressure_performance():
    """Test performance under memory pressure."""
    # Create a temporary directory
    temp_dir = tempfile.mkdtemp()
    try:
        print("\n=== Testing Performance Under Memory Pressure ===")

        # Allocate memory to create pressure (smaller sizes for testing)
        memory_sizes = [10, 20, 50]  # MB
        iterations = 20

        for size_mb in memory_sizes:
            # Allocate memory
            print(f"Allocating {size_mb}MB of memory...")
            memory_hog = [bytearray(1024 * 1024) for _ in range(size_mb)]

            # Force garbage collection to stabilize memory
            gc.collect()

            # Test file operations under memory pressure
            operation = f"file_operations_under_pressure ({size_mb}MB)"
            with measure_performance(operation, iterations):
                for i in range(iterations):
                    # Create a file
                    file_path = os.path.join(temp_dir, f"pressure_test_{size_mb}mb_{i}.txt")
                    create_file(file_path, f"Memory pressure test {i}")

                    # Read the file
                    content = read_file(file_path)
                    assert content == f"Memory pressure test {i}"

                    # Use a transaction
                    with TransactionManager():
                        # Update the file
                        create_file(file_path, f"Memory pressure test {i}, Updated")

                        # Read it again
                        content = read_file(file_path)
                        assert content == f"Memory pressure test {i}, Updated"

            # Release memory
            del memory_hog
            gc.collect()

    finally:
        # Clean up
        shutil.rmtree(temp_dir)


def test_error_recovery_performance():
    """Test the performance of error recovery."""
    # Create a temporary directory
    temp_dir = tempfile.mkdtemp()
    try:
        print("\n=== Testing Error Recovery Performance ===")

        # Test parameters
        error_rates = [0.1, 0.3, 0.5, 0.7, 0.9]  # Probability of error
        iterations = 100

        for error_rate in error_rates:
            operation = f"error_recovery (rate={error_rate})"
            success_count = 0
            error_count = 0

            with measure_performance(operation, iterations):
                for i in range(iterations):
                    try:
                        with TransactionManager():
                            # Create a file
                            file_path = os.path.join(temp_dir, f"error_test_{error_rate}_{i}.txt")
                            create_file(file_path, f"Error test {i}")

                            # Randomly decide whether to cause an error
                            if random.random() < error_rate:
                                # Cause an error
                                raise Exception(f"Forced error {i}")

                            # If we get here, no error occurred
                            success_count += 1
                    except Exception as e:
                        if "Forced error" not in str(e):
                            raise
                        error_count += 1

                        # Verify the file doesn't exist (rollback worked)
                        file_path = os.path.join(temp_dir, f"error_test_{error_rate}_{i}.txt")
                        assert not path_exists(file_path), f"Rollback failed for {file_path}"

            # Verify error counts
            expected_errors = int(iterations * error_rate)
            error_margin = iterations * 0.1  # Allow 10% margin
            assert abs(error_count - expected_errors) <= error_margin, \
                f"Expected ~{expected_errors} errors, got {error_count}"
            assert success_count + error_count == iterations, \
                f"Expected {iterations} total operations, got {success_count + error_count}"

    finally:
        # Clean up
        shutil.rmtree(temp_dir)


# Module-level helper for multiprocessing
def _multiprocess_worker(args):
    process_id, temp_dir, operations_per_process = args
    # Each process needs its own directory
    process_dir = os.path.join(temp_dir, f"process_{process_id}")
    os.makedirs(process_dir, exist_ok=True)
    # Perform operations
    for i in range(operations_per_process):
        file_path = os.path.join(process_dir, f"file_{i}.txt")
        with TransactionManager():
            create_file(file_path, f"Process {process_id}, File {i}")
        content = read_file(file_path)
        assert content == f"Process {process_id}, File {i}"
    return process_id


def test_multiprocess_performance():
    """Test performance across multiple processes."""
    # This test requires that the library can be used across processes
    # Create a temporary directory
    temp_dir = tempfile.mkdtemp()
    try:
        print("\n=== Testing Multi-Process Performance ===")

        # Test parameters
        process_counts = [2, 4, 8]
        operations_per_process = 50

        # Test multi-process operations
        for process_count in process_counts:
            # Prepare arguments for this process count
            args = [(pid, temp_dir, operations_per_process) for pid in range(process_count)]

            operation = f"multiprocess_operations (processes={process_count}, ops={operations_per_process})"

            with measure_performance(operation):
                with multiprocessing.Pool(processes=process_count) as pool:
                    results = pool.map(_multiprocess_worker, args)

                    # Verify all processes completed
                    assert len(results) == process_count
                    assert all(pid in results for pid in range(process_count))

    finally:
        # Clean up using the improved safe_cleanup_directory
        safe_cleanup_directory(temp_dir, recreate=False)


def run_all_performance_tests():
    """Run all performance tests and collect metrics."""
    print("\n=== Running All Performance Tests ===\n")

    # Run tests with error handling
    tests = [
        ("File Operations", test_file_operations_performance),
        ("Transactions", test_transaction_performance),
        ("Concurrent Operations", test_concurrent_operations_performance),
        ("Tool Execution", test_tool_execution_performance),
        ("Memory Pressure", test_memory_pressure_performance),
        ("Error Recovery", test_error_recovery_performance),
        ("Multiprocessing", test_multiprocess_performance)
    ]

    results = []

    for name, test_func in tests:
        try:
            print(f"\nRunning {name} Performance Test...")
            start_time = time.time()
            test_func()
            end_time = time.time()
            results.append((name, "PASSED", end_time - start_time))
        except Exception as e:
            print(f"\n{name} test failed: {e}")
            traceback.print_exc()
            results.append((name, "FAILED", 0))

    # Print summary
    print("\n=== Performance Test Summary ===")
    print(f"{'Test':<25} {'Status':<10} {'Time (s)':<10}")
    print("-" * 45)

    for name, status, duration in results:
        print(f"{name:<25} {status:<10} {duration:<10.2f}")

    passed = sum(1 for _, status, _ in results if status == "PASSED")
    total = len(results)

    print(f"\nPassed: {passed}/{total} ({passed/total*100:.1f}%)")
    print("\n=== All Performance Tests Completed ===")


if __name__ == "__main__":
    # Run all tests
    run_all_performance_tests()
