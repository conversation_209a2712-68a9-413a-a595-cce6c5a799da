#!/usr/bin/env python3
"""
Simple Integration Demo for GenerateLargeFileTool

This script demonstrates the integration without requiring API keys,
focusing on tool registry, direct usage, and file operations.
"""

import sys
import os
import time
from pathlib import Path

# Add the project root to the path
sys.path.append('.')

try:
    from mindlink.tools.base import tool_registry
    from mindlink.tools.file_tools import (
        GenerateLargeFileTool, 
        CreateFileTool, 
        ReadFileTool, 
        ListFilesTool
    )
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("💡 Make sure you're running from the project root directory")
    sys.exit(1)

class SimpleIntegrationDemo:
    """Simple integration demonstration without API requirements."""
    
    def __init__(self):
        """Initialize the demo."""
        self.generate_tool = GenerateLargeFileTool()
        self.create_tool = CreateFileTool()
        self.read_tool = ReadFileTool()
        self.list_tool = ListFilesTool()
        
        print("🚀 Simple Integration Demo Initialized")
        print("="*50)
    
    def demo_tool_registry_integration(self):
        """Demonstrate tool registry integration."""
        print("\n🔧 TOOL REGISTRY INTEGRATION")
        print("-" * 40)
        
        # Show registered tools
        print(f"📋 Total registered tools: {len(tool_registry)}")
        
        # Check if GenerateLargeFileTool is registered
        if "generate_large_file" in tool_registry:
            print("✅ GenerateLargeFileTool is registered")
            tool_class = tool_registry["generate_large_file"]
            print(f"📄 Description: {tool_class.description[:80]}...")
            
            # Test instantiation
            try:
                tool_instance = tool_class()
                print("✅ Tool can be instantiated from registry")
                return True
            except Exception as e:
                print(f"❌ Tool instantiation failed: {e}")
                return False
        else:
            print("❌ GenerateLargeFileTool not found in registry")
            return False
    
    def demo_direct_tool_usage(self):
        """Demonstrate direct tool usage."""
        print("\n🎯 DIRECT TOOL USAGE")
        print("-" * 40)
        
        print("📄 Testing GenerateLargeFileTool...")
        
        try:
            result = self.generate_tool.execute(
                path="demo_integration.py",
                content_description="Create a Python utility with file operations and data processing",
                target_line_count=100,
                max_chunks=2
            )
            
            print(f"📊 Status: {result['status']}")
            
            if result['status'] == 'success':
                res_data = result['result']
                lines = res_data['lines_written']
                chunks = res_data['chunks_written']
                target_met = res_data['target_lines_met']
                file_path = res_data['file_path']
                
                print(f"✅ Generated {lines} lines in {chunks} chunks")
                print(f"🎯 Target met: {target_met}")
                print(f"📁 File: {file_path}")
                
                # Verify file exists
                if os.path.exists(file_path):
                    print("✅ File successfully created")
                    return True
                else:
                    print("❌ File not found after generation")
                    return False
            else:
                print(f"❌ Generation failed: {result.get('observation')}")
                return False
                
        except Exception as e:
            print(f"❌ Tool execution error: {e}")
            return False
    
    def demo_file_operations_integration(self):
        """Demonstrate integration with file operations."""
        print("\n📁 FILE OPERATIONS INTEGRATION")
        print("-" * 40)
        
        # Step 1: Generate a file
        print("📝 Step 1: Generate file...")
        gen_result = self.generate_tool.execute(
            path="integration_test.py",
            content_description="Python module with classes and functions for data processing",
            target_line_count=80,
            max_chunks=2
        )
        
        if gen_result['status'] != 'success':
            print(f"❌ Generation failed: {gen_result.get('observation')}")
            return False
        
        file_path = gen_result['result']['file_path']
        print(f"✅ Generated: {file_path}")
        
        # Step 2: Read the file
        print("📖 Step 2: Read file...")
        try:
            read_result = self.read_tool.execute(path=file_path)
            
            if read_result['status'] == 'success':
                content = read_result['observation']
                newline = '\n'
                lines = len(content.split(newline))
                print(f"✅ Read {lines} lines successfully")
            else:
                print(f"❌ Read failed: {read_result.get('observation')}")
                return False
        except Exception as e:
            print(f"❌ Read error: {e}")
            return False
        
        # Step 3: List directory
        print("📋 Step 3: List directory...")
        try:
            dir_path = str(Path(file_path).parent)
            list_result = self.list_tool.execute(directory=dir_path)
            
            if list_result['status'] == 'success':
                files = list_result['observation']
                print("✅ Directory listing successful")
                if "integration_test.py" in files:
                    print("✅ Generated file found in directory")
                else:
                    print("⚠️  Generated file not visible in listing")
            else:
                print(f"❌ List failed: {list_result.get('observation')}")
                return False
        except Exception as e:
            print(f"❌ List error: {e}")
            return False
        
        return True
    
    def demo_error_handling(self):
        """Demonstrate error handling."""
        print("\n🛡️ ERROR HANDLING")
        print("-" * 40)
        
        # Test 1: Missing parameters
        print("🧪 Test 1: Missing parameters...")
        try:
            result1 = self.generate_tool.execute()
            expected_error = result1['status'] == 'error'
            print(f"✅ Handled missing params: {expected_error}")
        except Exception as e:
            print(f"✅ Exception caught for missing params: {type(e).__name__}")
        
        # Test 2: Invalid path
        print("🧪 Test 2: Invalid path...")
        try:
            result2 = self.generate_tool.execute(
                path="/invalid/nonexistent/path/file.py",
                content_description="Test",
                target_line_count=50
            )
            expected_error = result2['status'] == 'error'
            print(f"✅ Handled invalid path: {expected_error}")
        except Exception as e:
            print(f"✅ Exception caught for invalid path: {type(e).__name__}")
        
        return True
    
    def demo_performance_test(self):
        """Demonstrate performance characteristics."""
        print("\n📊 PERFORMANCE TEST")
        print("-" * 40)
        
        test_cases = [
            {"lines": 50, "chunks": 1, "name": "Small"},
            {"lines": 150, "chunks": 3, "name": "Medium"},
            {"lines": 300, "chunks": 5, "name": "Large"}
        ]
        
        results = []
        
        for i, case in enumerate(test_cases, 1):
            print(f"\n🧪 {case['name']} test: {case['lines']} lines, {case['chunks']} chunks")
            
            start_time = time.time()
            result = self.generate_tool.execute(
                path=f"perf_test_{i}.py",
                content_description=f"Python application with comprehensive functionality - {case['name']} scale",
                target_line_count=case['lines'],
                max_chunks=case['chunks']
            )
            end_time = time.time()
            
            duration = end_time - start_time
            
            if result['status'] == 'success':
                actual_lines = result['result']['lines_written']
                efficiency = actual_lines / duration if duration > 0 else 0
                
                results.append({
                    'name': case['name'],
                    'target': case['lines'],
                    'actual': actual_lines,
                    'duration': duration,
                    'efficiency': efficiency
                })
                
                print(f"  ✅ {actual_lines} lines in {duration:.2f}s ({efficiency:.1f} lines/sec)")
            else:
                print(f"  ❌ Failed: {result.get('observation')}")
        
        if results:
            avg_efficiency = sum(r['efficiency'] for r in results) / len(results)
            print(f"\n📈 Average: {avg_efficiency:.1f} lines/second")
        
        return len(results) > 0
    
    def run_demo(self):
        """Run the complete demonstration."""
        print("🎬 STARTING INTEGRATION DEMO")
        print("=" * 50)
        
        demos = [
            ("Tool Registry", self.demo_tool_registry_integration),
            ("Direct Usage", self.demo_direct_tool_usage),
            ("File Operations", self.demo_file_operations_integration),
            ("Error Handling", self.demo_error_handling),
            ("Performance", self.demo_performance_test)
        ]
        
        results = {}
        
        for demo_name, demo_func in demos:
            try:
                success = demo_func()
                results[demo_name] = success
                status = "✅ PASSED" if success else "❌ FAILED"
                print(f"\n📊 {demo_name}: {status}")
            except Exception as e:
                results[demo_name] = False
                print(f"\n💥 {demo_name}: ERROR - {e}")
        
        # Summary
        print("\n" + "=" * 50)
        print("🏁 DEMO SUMMARY")
        print("=" * 50)
        
        passed = sum(1 for success in results.values() if success)
        total = len(results)
        
        for demo_name, success in results.items():
            status = "✅" if success else "❌"
            print(f"  {status} {demo_name}")
        
        print(f"\n📊 Result: {passed}/{total} tests passed")
        
        if passed == total:
            print("\n🎉 ALL INTEGRATION TESTS PASSED!")
            print("🚀 GenerateLargeFileTool is fully integrated!")
            print("\n✨ Integration Features Verified:")
            print("  📋 Tool registry integration")
            print("  🎯 Direct tool execution")
            print("  📁 File operations compatibility")
            print("  🛡️ Error handling")
            print("  📊 Performance characteristics")
        else:
            print("\n⚠️  Some integration issues detected.")
        
        return passed == total

def main():
    """Main entry point."""
    try:
        demo = SimpleIntegrationDemo()
        success = demo.run_demo()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"💥 Demo failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()