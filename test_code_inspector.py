import os
# Ensure API key is set
key = os.getenv("OPENROUTER_API_KEY")
if key:
    os.environ["OPENROUTER_API_KEY"] = key

from agent_capability_benchmark import initialize_agent

# Initialize agent
agent = initialize_agent()

# Define the complex inspection prompt
prompt = '''Using MindLink's plan-and-batch-execute pattern, create a Python package called code_inspector that:
1. Recursively lists all `.py` files under a given directory (using the file-tools).
2. Parses each file's AST to extract every function and class name, count its lines, and detect whether it has a docstring (using the AST tools).
3. Builds an inter-module call graph in Graphviz DOT format (using the graph tools) and renders it to call_graph.png.
4. Writes a CSV report summary.csv with columns: module,object_type,name,lines_of_code,has_docstring.
5. Wraps the entire operation in one atomic transaction so that if any file has a syntax error or parsing failure, **no** files are created or modified.
6. Runs the AST parsing step in parallel (parallel=True) for maximum performance.
7. Creates a temporary test directory named `code_inspector_test` in the current working directory with three sample modules (one with syntax errors, one missing docstrings, one calling another), runs the inspector there, then cleans up everything except the final call_graph.png and summary.csv.

Return either the paths to the generated PNG and CSV, or a clear error message if the batch aborted.'''

# Run the plan_and_execute
success, result, actions = agent.plan_and_execute(prompt, parallel=True)

# Output results
print("Success:", success)
print("Result:", result)
print("Actions:", actions) 