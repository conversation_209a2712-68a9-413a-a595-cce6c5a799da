"""
Test to verify that the circular import issue in file_tools.py has been fixed.
"""

import pytest
import sys
import importlib


def test_direct_import_file_tools():
    """Test that file_tools.py can be imported directly without circular import errors."""
    try:
        # Force reload to ensure we're testing the current state
        if 'mindlink.tools.file_tools' in sys.modules:
            del sys.modules['mindlink.tools.file_tools']
        
        # Try to import the module
        import mindlink.tools.file_tools
        
        # If we get here, the import succeeded
        assert True
    except ImportError as e:
        # If we get an ImportError, the test fails
        assert False, f"ImportError when importing file_tools.py: {str(e)}"


def test_import_through_init():
    """Test that tools can be imported through the __init__.py file without circular import errors."""
    try:
        # Force reload to ensure we're testing the current state
        if 'mindlink.tools' in sys.modules:
            del sys.modules['mindlink.tools']
        
        # Try to import through __init__
        import mindlink.tools
        
        # Check that file tools are available
        assert hasattr(mindlink.tools, 'CreateFileTool')
        assert hasattr(mindlink.tools, 'ReadFileTool')
        assert hasattr(mindlink.tools, 'ListFilesTool')
    except ImportError as e:
        # If we get an ImportError, the test fails
        assert False, f"ImportError when importing through __init__: {str(e)}"


def test_import_hover_doc_tool():
    """Test that HoverDocTool can be imported without circular import errors."""
    try:
        # Force reload to ensure we're testing the current state
        if 'mindlink.tools.doc_tools' in sys.modules:
            del sys.modules['mindlink.tools.doc_tools']
        if 'mindlink.tools.knowledge_tools' in sys.modules:
            del sys.modules['mindlink.tools.knowledge_tools']
        
        # Try to import HoverDocTool from both modules
        from mindlink.tools.doc_tools import HoverDocTool as DocHoverDocTool
        from mindlink.tools.knowledge_tools import HoverDocTool as KnowledgeHoverDocTool
        
        # If we get here, the imports succeeded
        assert DocHoverDocTool is not None
        assert KnowledgeHoverDocTool is not None
    except ImportError as e:
        # If we get an ImportError, the test fails
        assert False, f"ImportError when importing HoverDocTool: {str(e)}"


def test_import_all_tools():
    """Test that all tools can be imported without circular import errors."""
    try:
        # Force reload to ensure we're testing the current state
        for module in list(sys.modules.keys()):
            if module.startswith('mindlink.tools.'):
                del sys.modules[module]
        if 'mindlink.tools' in sys.modules:
            del sys.modules['mindlink.tools']
        
        # Try to import all tools
        from mindlink.tools import (
            CreateFileTool,
            ReadFileTool,
            ListFilesTool,
            RunShellCommandTool,
            InsertASTNodeTool,
            SemanticSuggestTool,
            RunCodeTool,
            SnapshotTool,
            GenerateGraphTool,
            HoverDocTool,
            GenerateCallGraphTool
        )
        
        # If we get here, the imports succeeded
        assert CreateFileTool is not None
        assert ReadFileTool is not None
        assert ListFilesTool is not None
        assert RunShellCommandTool is not None
        assert InsertASTNodeTool is not None
        assert SemanticSuggestTool is not None
        assert RunCodeTool is not None
        assert SnapshotTool is not None
        assert GenerateGraphTool is not None
        assert HoverDocTool is not None
        assert GenerateCallGraphTool is not None
    except ImportError as e:
        # If we get an ImportError, the test fails
        assert False, f"ImportError when importing all tools: {str(e)}"


if __name__ == "__main__":
    pytest.main(["-xvs", __file__])
