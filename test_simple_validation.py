#!/usr/bin/env python3

import sys
sys.path.append('.')

from mindlink.agent import AgentOS
from mindlink.schemas.mindlink import Action

# Create agent instance
agent = AgentOS()

# Test parameter normalization directly
print("Testing parameter normalization for generate_large_file...")

# Create action with missing path
action = Action(
    tool_name="generate_large_file",
    parameters={
        "content_description": "A Python calculator application with 300 lines of functional code"
    },
    reasoning="Test action",
    thought="Testing"
)

print(f"Original parameters: {action.parameters}")

# Test the parameter normalization logic directly
tool_name = action.tool_name
parameters = action.parameters.copy()

# Apply the same normalization logic from _execute_tool
if tool_name in ['create_file', 'write_file', 'read_file', 'delete_file', 'generate_large_file']:
    if 'path' in parameters and parameters['path']:
        # Normalize path to absolute path under D:/3/
        import os
        original_path = parameters['path']
        if not os.path.isabs(original_path):
            parameters['path'] = f"D:/3/{original_path}"
        elif not original_path.startswith("D:/3/"):
            # If it's absolute but not under D:/3/, make it relative to D:/3/
            basename = os.path.basename(original_path)
            parameters['path'] = f"D:/3/{basename}"
    elif tool_name == 'generate_large_file':
        # For generate_large_file, ensure path parameter exists
        if 'path' not in parameters or not parameters['path']:
            # Generate a default path based on content description
            content_desc = parameters.get('content_description', 'generated_file')
            # Extract a reasonable filename from content description
            import re
            filename_match = re.search(r'\b(\w+)\.(py|js|txt|md|html|css|json)\b', content_desc)
            if filename_match:
                filename = filename_match.group(0)
            else:
                # Generate filename based on content type
                if 'python' in content_desc.lower() or 'py' in content_desc.lower():
                    filename = 'generated_code.py'
                elif 'javascript' in content_desc.lower() or 'js' in content_desc.lower():
                    filename = 'generated_code.js'
                elif 'html' in content_desc.lower():
                    filename = 'generated_page.html'
                else:
                    filename = 'generated_file.txt'
            parameters['path'] = f"D:/3/{filename}"

print(f"Normalized parameters: {parameters}")
print(f"Path parameter added: {'path' in parameters}")
if 'path' in parameters:
    print(f"Generated path: {parameters['path']}")

print("\nTest completed successfully!")