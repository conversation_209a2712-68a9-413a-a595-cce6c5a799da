from mindlink.agent import Agent<PERSON>
from mindlink.models.openrouter import OpenRouterModel
from mindlink.schemas.mindlink import Action, MindLinkRequest
from mindlink.tools.file_tools import GenerateLargeFileTool

print("Testing parameter validation for generate_large_file...")

# Test the tool's parameter validation directly
tool = GenerateLargeFileTool()
print(f"Tool name: {tool.name}")
print(f"Required parameters: {[name for name, info in tool.parameters_model.model_fields.items() if getattr(info, 'required', False) or info.default == ...]}")

# Test cases
test_cases = [
    {
        'name': 'Missing path parameter',
        'params': {'content_description': 'A Python calculator with 300 lines'}
    },
    {
        'name': 'With path parameter',
        'params': {
            'content_description': 'A Python calculator with 300 lines',
            'path': 'D:/3/calculator.py'
        }
    },
    {
        'name': 'Empty path parameter',
        'params': {
            'content_description': 'A Python calculator with 300 lines',
            'path': ''
        }
    }
]

print("\nTesting parameter validation:")
for test_case in test_cases:
    print(f"\nTest: {test_case['name']}")
    print(f"Parameters: {test_case['params']}")
    
    try:
        # Try to validate parameters using the tool's model
        validated = tool.parameters_model(**test_case['params'])
        print(f"✅ Validation passed: {validated.model_dump()}")
    except Exception as e:
        print(f"❌ Validation failed: {e}")

print("\n" + "="*50)
print("Testing agent parameter normalization:")

llm = OpenRouterModel()
agent = AgentOS(llm=llm, system_prompt_template='You are a helpful AI assistant.')

# Test the agent's parameter handling
test_action = Action(tool_name='generate_large_file', parameters={
    'content_description': 'A Python calculator application with 300 lines of functional code'
})

print(f"\nOriginal action parameters: {test_action.parameters}")

# Create a request and test parameter validation
request = MindLinkRequest(action=test_action)
print(f"Testing _execute_tool with missing path...")

# This should trigger the parameter validation and show us the exact error
result = agent._execute_tool(request)
print(f"Result status: {result.status}")
print(f"Result observation: {result.observation}")
if result.error:
    print(f"Result error: {result.error}")