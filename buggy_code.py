
def calculate_average(numbers):
    total = 0
    for num in numbers:
        total += num
    # Bug: Doesn't handle empty list case
    return total / len(numbers)

def find_max(numbers):
    # Bug: Doesn't initialize with first value
    max_value = 0
    for num in numbers:
        if num > max_value:
            max_value = num
    return max_value

# Bug: Incorrect function name
def find_minium(numbers):
    if not numbers:
        return None
    min_value = numbers[0]
    for num in numbers:
        if num < min_value:
            min_value = num
    return min_value
    