import os
import time
import threading
import pytest
from mindlink.config import load_config
from mindlink.main import create_llm
from mindlink.agent import AgentOS

def test_final_complex_performance_and_connectivity():
    config = load_config()
    # Test OpenRouter connectivity
    start = time.time()
    llm_router = create_llm(config)
    response_router = llm_router.generate("System: Connectivity Test", "Ping")
    duration_router = time.time() - start
    assert isinstance(response_router, str) and response_router.strip(), "OpenRouterModel returned empty response"

    # Test OpenAI connectivity if API key is set
    api_key = os.getenv("OPENAI_API_KEY")
    if api_key:
        config["llm"]["provider"] = "openai"
        config["llm"]["model"] = config["llm"].get("model", "gpt-4-turbo")
        config["llm"]["api_key"] = api_key
        start_ai = time.time()
        llm_ai = create_llm(config)
        response_ai = llm_ai.generate("System: Connectivity Test", "Ping")
        duration_ai = time.time() - start_ai
        assert isinstance(response_ai, str) and response_ai.strip(), "OpenAIModel returned empty response"
    else:
        duration_ai = None

    # Concurrency performance test
    num_threads = 5
    results = [None] * num_threads
    def worker(i):
        try:
            model = create_llm(config)
            t0 = time.time()
            model.generate("Concurrent Test", f"Request {i}")
            results[i] = time.time() - t0
        except Exception as e:
            results[i] = e

    threads = []
    for i in range(num_threads):
        t = threading.Thread(target=worker, args=(i,))
        threads.append(t)
        t.start()
    for t in threads:
        t.join()

    errors = [r for r in results if isinstance(r, Exception)]
    assert not errors, f"Errors during concurrent requests: {errors}"
    durations = [r for r in results if isinstance(r, float)]
    avg_concurrent = sum(durations) / len(durations)

    # AgentOS functionality test
    agent = AgentOS(llm=llm_router, system_prompt_template=config["agent"]["system_prompt_template"], max_steps=config["agent"]["max_steps"])
    success, result, history = agent.run("Please provide a brief summary of this test")
    assert success, f"AgentOS.run failed with result: {result}"
    assert isinstance(result, str) and result.strip(), "AgentOS returned empty result"

    # Report
    print("=== Final Complex Performance Test Report ===")
    print(f"OpenRouter response time: {duration_router:.2f}s")
    if duration_ai is not None:
        print(f"OpenAI response time: {duration_ai:.2f}s")
    print(f"Concurrent avg response time ({num_threads} threads): {avg_concurrent:.2f}s")
    print(f"AgentOS steps executed: {len(history)}")
