"""
Simple example of using the MindLink Agent library.
"""

import os
import sys

# Add the parent directory to the path so we can import mindlink
# This is only needed when running the example without installing the package
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import from the mindlink package
from mindlink import AgentOS, OpenRouterModel, DEFAULT_SYSTEM_PROMPT


def main():
    """
    Run a simple example of the MindLink Agent.
    """
    print("=== MindLink Agent Simple Example ===\n")
    
    # Create LLM using OpenRouter
    print("Initializing OpenRouter LLM model...")
    llm = OpenRouterModel(
        model_name="deepseek-r1",  # Options: "deepseek-r1", "glm-z1-32b"
        temperature=0.7,
        max_tokens=1024,
        top_p=0.9
    )
    
    # Create Agent OS
    print("Initializing Agent OS...")
    agent = AgentOS(
        llm=llm,
        system_prompt_template=DEFAULT_SYSTEM_PROMPT,
        max_steps=15
    )
    
    # Define a simple goal
    goal = "Create a file called example.txt with the content 'Hello from MindLink Agent!'"
    
    print(f"\nGoal: {goal}")
    print("\nRunning agent...\n")
    
    # Run the agent
    success, result, history = agent.run(goal)
    
    # Print result
    print("\n" + "="*50)
    print("Result:", "Success" if success else "Incomplete")
    print(result)
    print("="*50)
    
    # Print history summary
    print("\nAgent steps:")
    for i, entry in enumerate(history):
        print(f"{i+1}. {entry['request'].action.tool_name}")
    
    # Verify the file was created
    if os.path.exists("example.txt"):
        print("\nFile example.txt was successfully created.")
        with open("example.txt", "r") as f:
            content = f.read()
        print(f"Content: {content}")
    else:
        print("\nFile example.txt was not created.")


if __name__ == "__main__":
    main()
