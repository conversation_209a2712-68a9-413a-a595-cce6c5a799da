import streamlit as st
import os
import sys
import json
from pathlib import Path
from dotenv import load_dotenv
from typing import Dict, List, Any

# Load environment variables from .env file
load_dotenv()

# Define the path for storing the API key and model configurations
API_KEY_FILE_PATH = Path(__file__).parent / ".openaikey"
MODEL_CONFIG_FILE_PATH = Path(__file__).parent / ".model_config.json"

# Helper function to load API key from file
def load_api_key_from_file(file_path: Path) -> str | None:
    if file_path.exists():
        try:
            return file_path.read_text().strip()
        except Exception:
            return None
    return None

# Helper function to save API key to file
def save_api_key_to_file(file_path: Path, api_key: str):
    try:
        file_path.write_text(api_key)
    except Exception as e:
        st.error(f"Failed to save API key: {e}")

# Helper function to delete API key file
def delete_api_key_file(file_path: Path):
    try:
        if file_path.exists():
            os.remove(file_path)
    except Exception as e:
        st.error(f"Failed to delete API key file: {e}")

# Helper functions for model configuration management
def load_model_config() -> Dict[str, Any]:
    """Load model configuration from file or return default."""
    if MODEL_CONFIG_FILE_PATH.exists():
        try:
            with open(MODEL_CONFIG_FILE_PATH, 'r') as f:
                return json.load(f)
        except Exception:
            pass
    
    # Default configuration
    return {
        "active_models": [
            "mistral-small-3.1"
        ],
        "model_priority": [
            "mistral-small-3.1"
        ],
        "custom_models": {},
        "failover_enabled": True,
        "primary_model": "mistral-small-3.1"
    }

def save_model_config(config: Dict[str, Any]):
    """Save model configuration to file."""
    try:
        with open(MODEL_CONFIG_FILE_PATH, 'w') as f:
            json.dump(config, f, indent=2)
    except Exception as e:
        st.error(f"Failed to save model configuration: {e}")

def update_system_config(model_config: Dict[str, Any]):
    """Update the system configuration files with new model settings."""
    try:
        # Update config.py with new failover configuration
        config_path = Path(__file__).parent / "mindlink" / "config.py"
        
        # Read current config
        with open(config_path, 'r') as f:
            config_content = f.read()
        
        # Generate new failover configs based on user settings
        failover_configs = []
        for model_name in model_config["model_priority"]:
            if model_name in model_config["active_models"]:
                if model_name in model_config.get("custom_models", {}):
                    # Custom model
                    custom_model = model_config["custom_models"][model_name]
                    failover_configs.append({
                        "provider": custom_model.get("provider", "openrouter"),
                        "model_name": model_name,
                        "temperature": custom_model.get("temperature", 0.1),
                        "max_tokens": custom_model.get("max_tokens", 8192),
                        "api_key": custom_model.get("api_key", os.getenv("OPENROUTER_API_KEY"))
                    })
                else:
                    # Built-in model
                    failover_configs.append({
                        "provider": "openrouter",
                        "model_name": model_name,
                        "temperature": 0.1,
                        "max_tokens": 8192,
                        "api_key": os.getenv("OPENROUTER_API_KEY")
                    })
        
        # Update the FAILOVER_CONFIGS in config.py
        import re
        failover_pattern = r'FAILOVER_CONFIGS = \[(.*?)\]'
        new_failover = f"FAILOVER_CONFIGS = {failover_configs}"
        
        updated_content = re.sub(failover_pattern, new_failover, config_content, flags=re.DOTALL)
        
        with open(config_path, 'w') as f:
            f.write(updated_content)
            
        return True
    except Exception as e:
        st.error(f"Failed to update system configuration: {e}")
        return False

# Add the current directory to the path to import mindlink
current_dir = Path(__file__).parent
sys.path.append(str(current_dir))

try:
    from mindlink.agent import AgentOS
    from mindlink.models.openrouter import OpenRouterModel, OPENROUTER_MODELS
    from mindlink.config import DEFAULT_SYSTEM_PROMPT
except ImportError:
    st.error("Failed to import MindLink. Make sure it's installed and the path is correct.")
    st.stop()

# --- Tool Styling ---
TOOL_STYLES = {
    "create_file": {"icon": "🔨", "color": "#1f77b4"},  # Muted Blue
    "update_file": {"icon": "🖊️", "color": "#ff7f0e"},  # Safety Orange
    "read_file": {"icon": "📖", "color": "#2ca02c"},  # Cooked Asparagus Green
    "delete_file": {"icon": "🗑️", "color": "#d62728"},  # Brick Red
    "run_shell_command": {"icon": "💻", "color": "#9467bd"},  # Muted Purple
    "web_search": {"icon": "🌐", "color": "#8c564b"},  # Chestnut Brown
    "generate_large_file": {"icon": "📄", "color": "#17becf"},  # Cyan
    "append_to_file": {"icon": "➕", "color": "#bcbd22"},  # Olive
    "search_in_files": {"icon": "🔍", "color": "#ff9896"},  # Light Red
    "finish": {"icon": "🏁", "color": "#e377c2"},  # Opera Mauve
    "default": {"icon": "⚙️", "color": "#7f7f7f"}  # Middle Gray
}

# --- App Title and Description ---
st.set_page_config(page_title="MindLink Agent UI", layout="wide", initial_sidebar_state="expanded")
st.title("🧠 MindLink Agent UI")
st.markdown("""
Welcome to the MindLink Agent UI! Interact with the MindLink Agent Core, 
set your goals, and see the agent plan and execute tasks.
""")

# Load model configuration
if "model_config" not in st.session_state:
    st.session_state.model_config = load_model_config()

# --- Sidebar for Configuration --- 
with st.sidebar:
    st.header("⚙️ Configuration")
    
    # LLM Model Selection & Management
    st.subheader("🤖 LLM Models")
    
    # Only allow selection of the free Mistral model
    st.session_state.model_config["active_models"] = ["mistral-small-3.1"]
    st.session_state.model_config["model_priority"] = ["mistral-small-3.1"]
    st.session_state.model_config["primary_model"] = "mistral-small-3.1"
    selected_model = "mistral-small-3.1"
    st.caption("Only the free Mistral model is available.")
    selected_builtin = st.selectbox("Built-in Models:", ["mistral-small-3.1"], key="builtin_selector", disabled=True)
    st.info("✅ Mistral Small 3.1 is active and ready to use.")
    
    # Custom model form (simplified)
    st.markdown("**Add Custom Model:**")
    with st.form("add_custom_model", clear_on_submit=True):
        col1, col2 = st.columns(2)
        with col1:
            custom_name = st.text_input("Model Name:", placeholder="My Custom Model")
            custom_provider = st.selectbox("Provider:", ["openrouter", "openai", "custom"])
        with col2:
            custom_model_id = st.text_input("Model ID:", placeholder="gpt-4-turbo")
            custom_api_key = st.text_input("API Key (optional):", type="password")
        
        # Advanced settings in columns
        col3, col4 = st.columns(2)
        with col3:
            custom_temperature = st.slider("Temperature:", 0.0, 2.0, 0.1, 0.1)
        with col4:
            custom_max_tokens = st.number_input("Max Tokens:", 1000, 32000, 8192)
        
        if custom_provider == "custom":
            custom_api_url = st.text_input("API URL:", placeholder="https://api.example.com/v1")
        else:
            custom_api_url = None
        
        if st.form_submit_button("➕ Add Custom Model", type="primary"):
            if custom_name and custom_model_id:
                custom_model_config = {
                    "provider": custom_provider,
                    "model_id": custom_model_id,
                    "temperature": custom_temperature,
                    "max_tokens": custom_max_tokens,
                    "api_key": custom_api_key or None,
                    "api_url": custom_api_url
                }
                
                st.session_state.model_config["custom_models"][custom_name] = custom_model_config
                st.session_state.model_config["active_models"].append(custom_name)
                st.session_state.model_config["model_priority"].append(custom_name)
                # Set as primary if it's the first model
                if len(st.session_state.model_config["active_models"]) == 1:
                    st.session_state.model_config["primary_model"] = custom_name
                save_model_config(st.session_state.model_config)
                update_system_config(st.session_state.model_config)
                st.success(f"✅ Added: {custom_name}")
                st.rerun()
            else:
                st.error("Please provide both model name and model ID")
    
    # Failover settings (outside the form)
    st.markdown("**Failover Settings:**")
    col1, col2 = st.columns([2, 1])
    with col1:
        failover_enabled = st.checkbox(
            "Enable automatic failover between models", 
            value=st.session_state.model_config.get("failover_enabled", True),
            help="When enabled, the system will automatically try backup models if the primary fails"
        )
    with col2:
        if st.button("🔄 Reorder", help="Click to reorder failover priority"):
            st.session_state.show_priority_editor = not st.session_state.get("show_priority_editor", False)
    
    if failover_enabled != st.session_state.model_config.get("failover_enabled", True):
        st.session_state.model_config["failover_enabled"] = failover_enabled
        save_model_config(st.session_state.model_config)
        update_system_config(st.session_state.model_config)
    
    # Priority editor (shown when requested)
    if st.session_state.get("show_priority_editor", False) and len(active_models) > 1:
        st.markdown("**Failover Priority Order:**")
        priority_list = st.session_state.model_config["model_priority"].copy()
        
        for i, model_name in enumerate(priority_list):
            col1, col2, col3 = st.columns([4, 1, 1])
            with col1:
                st.write(f"{i+1}. {model_name}")
            with col2:
                if i > 0 and st.button("⬆️", key=f"up_{i}", help="Move up"):
                    priority_list[i], priority_list[i-1] = priority_list[i-1], priority_list[i]
                    st.session_state.model_config["model_priority"] = priority_list
                    save_model_config(st.session_state.model_config)
                    update_system_config(st.session_state.model_config)
                    st.rerun()
            with col3:
                if i < len(priority_list) - 1 and st.button("⬇️", key=f"down_{i}", help="Move down"):
                    priority_list[i], priority_list[i+1] = priority_list[i+1], priority_list[i]
                    st.session_state.model_config["model_priority"] = priority_list
                    save_model_config(st.session_state.model_config)
                    update_system_config(st.session_state.model_config)
                    st.rerun()
    
    st.markdown("---")
    
    # API key input management
    # Initialize session state for API key if it doesn't exist
    if "api_key" not in st.session_state:
        # Try loading from .env file first, then from saved file, then env var, then default to empty
        env_key = os.getenv("OPENROUTER_API_KEY")
        file_key = load_api_key_from_file(API_KEY_FILE_PATH)
        st.session_state.api_key = env_key or file_key or ""

    # Initialize session state for controlling API key input visibility
    if "show_api_key_input" not in st.session_state:
        # Show input by default if no API key is set, otherwise hide it.
        st.session_state.show_api_key_input = not bool(st.session_state.api_key)

    if st.session_state.api_key and not st.session_state.show_api_key_input:
        # Display masked API key and a button to change it
        masked_key_display = f"••••••••{st.session_state.api_key[-4:]}" if len(st.session_state.api_key) >= 4 else "••••••••"
        key_status = " (ذخیره شده)" if (API_KEY_FILE_PATH.exists() and load_api_key_from_file(API_KEY_FILE_PATH) == st.session_state.api_key) else ""
        st.markdown(f"🔑 OpenRouter API Key: `{masked_key_display}`{key_status}")
        col1, col2 = st.columns(2)
        with col1:
            if st.button("تغییر کلید API"):
                st.session_state.show_api_key_input = True
                st.rerun()
        with col2:
            if API_KEY_FILE_PATH.exists() and load_api_key_from_file(API_KEY_FILE_PATH) == st.session_state.api_key:
                if st.button("حذف کلید ذخیره شده و ورود مجدد"):
                    delete_api_key_file(API_KEY_FILE_PATH)
                    st.session_state.api_key = os.getenv("OPENROUTER_API_KEY") or ""  # Fallback to .env or empty
                    st.session_state.show_api_key_input = True  # Show input for new key
                    st.rerun()
    else:
        # Show API key input field
        api_key_input_val = st.text_input(
            "🔑 OpenRouter API Key",
            type="password",
            value=st.session_state.api_key,
            help="کلید API OpenRouter خود را وارد کنید. این کلید به صورت محلی ذخیره خواهد شد. همچنید می‌توانید آن را به عنوان متغیر محیطی OPENROUTER_API_KEY تنظیم کنید."
        )
        if api_key_input_val != st.session_state.api_key:
            st.session_state.api_key = api_key_input_val
            if api_key_input_val:  # Save only if not empty
                save_api_key_to_file(API_KEY_FILE_PATH, api_key_input_val)
            else:  # If user clears the input, delete the saved key
                delete_api_key_file(API_KEY_FILE_PATH)
            # Show input if key becomes empty, otherwise hide.
            st.session_state.show_api_key_input = not bool(st.session_state.api_key)
            st.rerun()

    # Set model_name for agent initialization
    if selected_model:
        model_name = selected_model
    else:
        st.stop()
    
    st.markdown("--- ")
    st.subheader("Execution Options")
    parallel_execution = st.checkbox("🚀 Execute plan steps in parallel", value=True)
    # Agent will dynamically determine optimal steps based on goal complexity
    # Set a reasonable upper bound to prevent runaway execution
    max_steps = 25 # Upper limit, agent will use fewer steps based on goal analysis

    st.markdown("--- ")
    st.subheader("🚀 Quick Actions")
    
    # GenerateLargeFileTool Quick Access
    with st.expander("📄 Generate Large File", expanded=False):
        st.markdown("**Quick templates for generating large files:**")
        
        # Preset templates
        template_options = {
            "Python Web Application": "Create a comprehensive Python web application with Flask/FastAPI, database models, API endpoints, authentication, file handling, and comprehensive functionality",
            "Data Analysis Script": "Create a comprehensive Python data analysis script with pandas, numpy, matplotlib, data processing pipelines, statistical analysis, and visualization functions",
            "Machine Learning Pipeline": "Create a complete machine learning pipeline with data preprocessing, feature engineering, model training, evaluation, and prediction functions",
            "Game Development": "Create a comprehensive Python game using pygame with game mechanics, graphics, sound, user interface, and complete gameplay functionality",
            "Custom Description": "Enter your own description"
        }
        
        selected_template = st.selectbox("Choose a template:", list(template_options.keys()))
        
        if selected_template == "Custom Description":
            custom_desc = st.text_area("Enter custom description:", height=100)
            description = custom_desc
        else:
            description = template_options[selected_template]
            st.text_area("Description:", value=description, height=100, disabled=True)
        
        target_lines = st.number_input("Target line count:", min_value=50, max_value=2000, value=1000, step=50)
        max_chunks = st.number_input("Max chunks:", min_value=1, max_value=20, value=10, step=1)
        
        if st.button("🚀 Generate Large File", type="primary"):
            if description.strip():
                # Create the prompt for the agent with explicit parameter specification
                prompt = f"Use the generate_large_file tool with these exact parameters:\n\npath: 'generated_large_file.py'\ncontent_description: '{description}'\ntarget_line_count: {target_lines}\nmax_chunks: {max_chunks}\nchunk_size_description: 'Generate a substantial content chunk of about 200-300 lines of functional code.'\n\nPlease ensure you include the target_line_count parameter set to {target_lines} lines."
                
                # Add to chat and trigger processing
                st.session_state.messages.append({"role": "user", "content": prompt})
                st.rerun()
            else:
                st.error("Please provide a description for the file.")
    
    st.markdown("--- ")
    if st.button("Clear Chat History"): 
        st.session_state.messages = []
        st.rerun()

# --- Agent Initialization ---
# Use API key from session state, which might have been updated from input or env var
api_key = st.session_state.api_key

@st.cache_resource
def initialize_agent(api_key, model_name, max_steps, model_config):
    if not api_key:
        return None, "OpenRouter API Key is missing. Please provide it via environment variable or the sidebar."
    try:
        # Check if it's a custom model
        if model_name in model_config.get("custom_models", {}):
            custom_model = model_config["custom_models"][model_name]
            if custom_model["provider"] == "openai":
                llm = OpenAIModel(
                    api_key=custom_model.get("api_key") or api_key,
                    model=custom_model["model_id"],
                    temperature=custom_model.get("temperature", 0.1),
                    max_tokens=custom_model.get("max_tokens", 8192)
                )
            else:
                llm = OpenRouterModel(
                    api_key=custom_model.get("api_key") or api_key, 
                    model_name=custom_model["model_id"],
                    temperature=custom_model.get("temperature", 0.1),
                    max_tokens=custom_model.get("max_tokens", 8192)
                )
        else:
            # Built-in model
            llm = OpenRouterModel(api_key=api_key, model_name=model_name)
        
        # Failover is handled internally by AgentOS using FAILOVER_CONFIGS
        
        agent = AgentOS(
            llm=llm,
            system_prompt_template=DEFAULT_SYSTEM_PROMPT,
            max_steps=max_steps,
            enable_command_comprehension=True,  # Enable intelligent command preprocessing
            enable_failover=model_config.get("failover_enabled", True)
        )
        return agent, None
    except Exception as e:
        return None, f"Failed to initialize agent: {e}"

agent, error_msg = initialize_agent(api_key, model_name, max_steps, st.session_state.model_config)

if error_msg:
    st.error(error_msg)
    st.stop()

if not agent:
    st.warning("Agent could not be initialized. Please check configuration and API key.")
    st.stop()

# --- Main Chat Interface Area ---
st.header("💬 Chat with MindLink Agent")

# Initialize chat history if it doesn't exist
if "messages" not in st.session_state:
    st.session_state.messages = []

# Display chat messages from history on app rerun
for message in st.session_state.messages:
    with st.chat_message(message["role"]):
        if isinstance(message["content"], dict) and "plan" in message["content"]:
            # Custom display for plan
            st.markdown("**📝 طرح عامل:**")
            plan_data = message["content"]["plan"]
            for i, step in enumerate(plan_data):
                tool_style = TOOL_STYLES.get(step.tool_name, TOOL_STYLES["default"])
                icon = tool_style["icon"]
                color = tool_style["color"]
                with st.expander(f"{icon} مرحله {i+1}: {step.tool_name}", expanded=False):
                    st.markdown(f"**ابزار:** <span style='color:{color};'>{icon} {step.tool_name}</span>", unsafe_allow_html=True)
                    st.write("**پارامترها:**")
                    st.json(step.parameters or {})
        elif isinstance(message["content"], dict) and "execution_result" in message["content"]:
            # Custom display for execution result
            st.markdown("**⚙️ نتیجه اجرا:**")
            exec_data = message["content"]["execution_result"]
            status_icon = "✅" if exec_data.status.lower() == "success" else "❌"
            status_color = "green" if exec_data.status.lower() == "success" else "red"
            st.markdown(f"**وضعیت:** <span style='color:{status_color};'>{status_icon} {exec_data.status}</span>", unsafe_allow_html=True)
            st.code(exec_data.observation, language='text')
        else:
            st.markdown(message["content"])

# React to user input
if prompt := st.chat_input("Enter your goal for the agent..."):
    # Display user message in chat message container
    with st.chat_message("user"):
        st.markdown(prompt)
    # Add user message to chat history
    st.session_state.messages.append({"role": "user", "content": prompt})

    # Assistant response container
    with st.chat_message("assistant"):
        # Phase 1: Plan only
        with st.spinner("🧠 Thinking and planning..."):
            try:
                plan = agent.plan_once(prompt)
                if not plan:
                    st.warning("The agent could not generate a plan for this goal.")
                    st.stop()
            except Exception as e:
                st.error(f"Plan generation failed: {e}")
                st.stop()

        # Display Plan Steps and add to history
        st.markdown("**📝 طرح عامل:**")
        plan_display_data = []
        for i, action in enumerate(plan):
            plan_display_data.append(action) # Store action object for later execution
            tool_style = TOOL_STYLES.get(action.tool_name, TOOL_STYLES["default"])
            icon = tool_style["icon"]
            color = tool_style["color"]
            with st.expander(f"{icon} مرحله {i+1}: {action.tool_name}", expanded=False):
                st.markdown(f"**ابزار:** <span style='color:{color};'>{icon} {action.tool_name}</span>", unsafe_allow_html=True)
                st.write("**پارامترها:**")
                st.json(action.parameters or {})
        st.session_state.messages.append({"role": "assistant", "content": {"plan": plan_display_data}})
        
        # Auto-execute entire plan
        with st.spinner(f"⚙️ در حال اجرای {len(plan)} مرحله..."):
            try:
                resp = agent.batch_execute_plan(plan, parallel=parallel_execution)
            except Exception as e:
                st.error(f"اجرا با شکست مواجه شد: {e}")
                st.stop()
        
        st.markdown("**⚙️ نتیجه اجرا:**")
        status_icon = "✅" if resp.status.lower() == "success" else "❌"
        status_color = "green" if resp.status.lower() == "success" else "red"
        st.markdown(f"**وضعیت:** <span style='color:{status_color};'>{status_icon} {resp.status}</span>", unsafe_allow_html=True)
        st.code(resp.observation, language='text')
        # Record actual execution result in chat history
        st.session_state.messages.append({"role": "assistant", "content": {"execution_result": resp}})

# --- Optional: Display Agent Details --- 
with st.sidebar:
    st.markdown("--- ")
    with st.expander("ℹ️ Agent Details", expanded=False):
        st.write(f"**Primary Model:** {model_name}")
        if hasattr(agent, 'llm') and hasattr(agent.llm, 'model_id'):
            st.write(f"**Model ID:** {agent.llm.model_id}")
        st.write(f"**Max Steps (Upper Limit):** {agent.max_steps} - Agent dynamically determines optimal steps")
        st.write(f"**Failover Enabled:** {st.session_state.model_config.get('failover_enabled', True)}")
        st.write(f"**Active Models:** {len(st.session_state.model_config['active_models'])}")
        st.text_area("System Prompt (Preview)", value=agent.system_prompt_template, height=150, disabled=True)
        
        # Model configuration display
        st.markdown("**Current Model Configuration:**")
        st.json(st.session_state.model_config)