"""
User Experience Test for MindLink Agent.

This script tests the MindLink agent from a user's perspective,
simulating real-world usage scenarios and measuring the quality 
of interactions and results.
"""

import os
import sys
import time
import tempfile
import logging
from pathlib import Path
from typing import List, Dict, Any, Tuple

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('user_experience_test.log')
    ]
)
logger = logging.getLogger(__name__)

# Add the current directory to the path so we can import mindlink
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Try to import MindLink
try:
    from mindlink.agent import AgentOS
    from mindlink.models.openai import OpenAIModel
    from mindlink.models.openrouter import OpenRouterModel
    from mindlink.config import DEFAULT_SYSTEM_PROMPT
    
    IMPORTS_SUCCESSFUL = True
    logger.info("[SUCCESS] MindLink agent imported successfully")
except ImportError as e:
    IMPORTS_SUCCESSFUL = False
    logger.error(f"[ERROR] Failed to import MindLink: {e}")
    sys.exit(1)

def initialize_agent():
    """Initialize the MindLink agent with available API keys."""
    # Try OpenAI first, then OpenRouter
    api_key = os.getenv("OPENAI_API_KEY")
    if api_key:
        logger.info("[INFO] Using OpenAI for agent")
        llm = OpenAIModel(api_key=api_key)
    else:
        api_key = os.getenv("OPENROUTER_API_KEY")
        if api_key:
            logger.info("[INFO] Using OpenRouter for agent")
            llm = OpenRouterModel(api_key=api_key)
        else:
            logger.error("[ERROR] No API keys found for any LLM provider")
            return None
    
    agent = AgentOS(
        llm=llm,
        system_prompt_template=DEFAULT_SYSTEM_PROMPT,
        max_steps=30
    )
    
    return agent

def run_user_task(agent, task_description, evaluation_criteria):
    """Run a user task and evaluate the results."""
    logger.info(f"[TASK] {task_description}")
    
    start_time = time.time()
    success, result, history = agent.run(task_description)
    execution_time = time.time() - start_time
    
    # Log results
    logger.info(f"Task completed in {execution_time:.2f} seconds")
    logger.info(f"Success: {success}")
    logger.info(f"Result: {result[:200]}..." if len(result) > 200 else f"Result: {result}")
    
    # Evaluate against criteria
    evaluation = {}
    for criterion, check_func in evaluation_criteria.items():
        evaluation[criterion] = check_func(result, history)
        status = "PASS" if evaluation[criterion] else "FAIL"
        logger.info(f"Evaluation - {criterion}: {status}")
    
    # Calculate overall score
    overall_score = sum(1 for v in evaluation.values() if v) / len(evaluation) * 100
    logger.info(f"Overall score: {overall_score:.1f}%")
    
    return {
        "success": success,
        "execution_time": execution_time,
        "result": result,
        "evaluation": evaluation,
        "overall_score": overall_score,
        "steps": len(history)
    }

def run_user_experience_test():
    """Run the full user experience test suite."""
    print("\n=== MindLink Agent User Experience Test ===\n")
    
    # Initialize agent
    agent = initialize_agent()
    if not agent:
        logger.error("[ERROR] Failed to initialize agent")
        return False
    
    # Create a temporary directory for testing
    with tempfile.TemporaryDirectory() as temp_dir:
        logger.info(f"Created test directory: {temp_dir}")
        os.chdir(temp_dir)
        
        # Define tasks and evaluation criteria
        tasks = [
            {
                "name": "Simple File Creation",
                "description": "Create a text file called 'hello.txt' with the content 'Hello, World!'",
                "criteria": {
                    "file_created": lambda result, history: os.path.exists("hello.txt"),
                    "correct_content": lambda result, history: os.path.exists("hello.txt") and open("hello.txt").read() == "Hello, World!",
                    "clear_explanation": lambda result, history: "created" in result.lower() and "hello.txt" in result.lower()
                }
            },
            {
                "name": "Python Script Generation",
                "description": """
                Create a Python script called 'calculator.py' that:
                1. Defines functions for addition, subtraction, multiplication, and division
                2. Includes error handling for division by zero
                3. Has a main function that demonstrates all operations
                4. Includes proper docstrings
                """,
                "criteria": {
                    "file_created": lambda result, history: os.path.exists("calculator.py"),
                    "has_functions": lambda result, history: os.path.exists("calculator.py") and all(f in open("calculator.py").read() for f in ["def add", "def subtract", "def multiply", "def divide"]),
                    "has_error_handling": lambda result, history: os.path.exists("calculator.py") and "ZeroDivision" in open("calculator.py").read(),
                    "has_docstrings": lambda result, history: os.path.exists("calculator.py") and '"""' in open("calculator.py").read(),
                    "clear_explanation": lambda result, history: "created" in result.lower() and "calculator.py" in result.lower()
                }
            },
            {
                "name": "Complex Project Structure",
                "description": """
                Create a simple web application project structure with:
                1. A main directory called 'webapp'
                2. Inside 'webapp', create:
                   - app.py (a simple Flask application)
                   - templates/ directory with index.html
                   - static/ directory with style.css
                   - requirements.txt file listing Flask as a dependency
                3. Make sure app.py can serve the index.html template
                """,
                "criteria": {
                    "main_dir_created": lambda result, history: os.path.exists("webapp"),
                    "app_file_created": lambda result, history: os.path.exists(os.path.join("webapp", "app.py")),
                    "templates_dir_created": lambda result, history: os.path.exists(os.path.join("webapp", "templates")),
                    "index_created": lambda result, history: os.path.exists(os.path.join("webapp", "templates", "index.html")),
                    "static_dir_created": lambda result, history: os.path.exists(os.path.join("webapp", "static")),
                    "css_created": lambda result, history: os.path.exists(os.path.join("webapp", "static", "style.css")),
                    "requirements_created": lambda result, history: os.path.exists(os.path.join("webapp", "requirements.txt")),
                    "flask_imported": lambda result, history: os.path.exists(os.path.join("webapp", "app.py")) and "Flask" in open(os.path.join("webapp", "app.py")).read(),
                    "render_template_used": lambda result, history: os.path.exists(os.path.join("webapp", "app.py")) and "render_template" in open(os.path.join("webapp", "app.py")).read(),
                    "clear_explanation": lambda result, history: "created" in result.lower() and "webapp" in result.lower()
                }
            }
        ]
        
        # Run tasks
        results = {}
        
        for i, task in enumerate(tasks):
            print(f"\n=== Task {i+1}: {task['name']} ===\n")
            result = run_user_task(agent, task["description"], task["criteria"])
            results[task["name"]] = result
            
            # Print summary
            print(f"\nTask completed in {result['execution_time']:.2f} seconds")
            print(f"Success: {result['success']}")
            print(f"Overall score: {result['overall_score']:.1f}%")
            print(f"Steps taken: {result['steps']}")
        
        # Calculate and print overall assessment
        print("\n=== Overall User Experience Assessment ===\n")
        
        overall_scores = [result["overall_score"] for result in results.values()]
        avg_score = sum(overall_scores) / len(overall_scores)
        
        execution_times = [result["execution_time"] for result in results.values()]
        avg_execution_time = sum(execution_times) / len(execution_times)
        
        success_rate = sum(1 for result in results.values() if result["success"]) / len(results) * 100
        
        total_steps = sum(result["steps"] for result in results.values())
        avg_steps = total_steps / len(results)
        
        print(f"Average task score: {avg_score:.1f}%")
        print(f"Average execution time: {avg_execution_time:.2f} seconds")
        print(f"Success rate: {success_rate:.1f}%")
        print(f"Average steps per task: {avg_steps:.1f}")
        
        # Quality assessment
        if avg_score >= 90:
            quality = "EXCELLENT"
        elif avg_score >= 80:
            quality = "GOOD"
        elif avg_score >= 70:
            quality = "SATISFACTORY"
        elif avg_score >= 50:
            quality = "NEEDS IMPROVEMENT"
        else:
            quality = "POOR"
        
        print(f"\nOverall user experience quality: {quality}")
        
        # Create report file
        with open("user_experience_report.md", "w") as f:
            f.write("# MindLink Agent User Experience Test Report\n\n")
            f.write(f"**Test Date:** {time.strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            f.write("## Summary\n\n")
            f.write(f"- **Average Task Score:** {avg_score:.1f}%\n")
            f.write(f"- **Average Execution Time:** {avg_execution_time:.2f} seconds\n")
            f.write(f"- **Success Rate:** {success_rate:.1f}%\n")
            f.write(f"- **Average Steps Per Task:** {avg_steps:.1f}\n")
            f.write(f"- **Overall Quality:** {quality}\n\n")
            
            f.write("## Task Results\n\n")
            for task_name, result in results.items():
                f.write(f"### {task_name}\n\n")
                f.write(f"- **Success:** {result['success']}\n")
                f.write(f"- **Execution Time:** {result['execution_time']:.2f} seconds\n")
                f.write(f"- **Steps Taken:** {result['steps']}\n")
                f.write(f"- **Score:** {result['overall_score']:.1f}%\n\n")
                
                f.write("#### Evaluation Criteria\n\n")
                for criterion, passed in result['evaluation'].items():
                    status = "✓ PASS" if passed else "✗ FAIL"
                    f.write(f"- **{criterion}:** {status}\n")
                f.write("\n")
        
        logger.info("User experience test completed and report generated")
        return avg_score >= 70  # Consider the test successful if average score is at least 70%

if __name__ == "__main__":
    if run_user_experience_test():
        sys.exit(0)
    else:
        sys.exit(1) 