import sys
sys.path.append('d:/کتابخانه پایتون/2-')
from mindlink.agent import Agent<PERSON>
from mindlink.models.openrouter import OpenRouterModel
import os

# Initialize the agent
llm = OpenRouterModel(api_key=os.getenv('OPENROUTER_API_KEY'), model_name='deepseek-r1')
system_prompt = '''You are a helpful assistant that can use tools to accomplish tasks.

Available tools:
{tool_descriptions}

When you need to create large files with many lines of code, use the generate_large_file tool.
Always use the exact tool names as listed above.'''

agent = AgentOS(llm, system_prompt_template=system_prompt, max_steps=10)

# Test the planning for multiple files
goal = 'Create 2 Python files, each containing 500 lines of functional code. File 1: D:/3/test_file1.py, File 2: D:/3/test_file2.py'
plan = agent.plan_once(goal)

print(f'Plan created with {len(plan)} actions:')
for i, action in enumerate(plan):
    param_keys = list(action.parameters.keys()) if action.parameters else "None"
    print(f'- {i+1}: {action.tool_name} with params: {param_keys}')
    if action.tool_name == 'generate_large_file':
        print(f'  Path: {action.parameters.get("path", "N/A")}')
    elif action.tool_name == 'write_file' and action.parameters:
        print(f'  File: {action.parameters.get("file_path", action.parameters.get("path", "N/A"))}')
    elif action.tool_name == 'echo' and action.parameters:
        print(f'  Message: {action.parameters.get("message", "N/A")[:100]}...')