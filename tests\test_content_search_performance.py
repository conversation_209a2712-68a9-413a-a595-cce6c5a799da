import os
import sys
import time
import tempfile
import shutil
import random
import string
from contextlib import contextmanager
from pathlib import Path # Moved import to the top

# Add project root to Python path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.insert(0, project_root)

"""
Performance Test for Content Search in MindLink Agent Core.

This test evaluates the performance of searching for content within files,
simulating a real-world user scenario.
"""

import os
import time
import tempfile
import shutil
import random
import string
from contextlib import contextmanager

# Import core components
from mindlink.tools.file_tools import (
    create_file,
    read_file,
    list_files,
    path_exists,
    search_in_file # Added import
)
# Assuming SAFE_BASE_DIR is a configured path for safe file operations
# If not available directly, we might need to define a base for temp dirs
# For now, let's try to import it or define a fallback.
SAFE_BASE_DIR = getattr(__import__('mindlink.tools.file_tools', fromlist=['SAFE_BASE_DIR']), 'SAFE_BASE_DIR', Path(tempfile.gettempdir()) / "mindlink_safe_tests")


@contextmanager
def measure_time(operation_name):
    """Simple context manager to measure execution time."""
    class Timer: # Define a simple class to hold the duration
        def __init__(self):
            self.duration = 0.0
            self.start_time = 0.0

    timer_obj = Timer()
    timer_obj.start_time = time.time()
    yield timer_obj  # Yield the timer object
    end_time = time.time()
    timer_obj.duration = end_time - timer_obj.start_time
    print(f"{operation_name}: {timer_obj.duration:.4f} seconds")
    # No explicit return needed for the 'as' variable usage,
    # the yielded object (timer_obj) carries the result.


def generate_random_content(size_kb, specific_content=None):
    """Generate random content of specified size in KB, optionally embedding specific content."""
    chars = string.ascii_letters + string.digits + string.punctuation + ' '
    content_list = [random.choice(chars) for _ in range(size_kb * 1024)]
    
    if specific_content:
        if len(specific_content) < len(content_list):
            # Embed specific content at a random position
            start_index = random.randint(0, len(content_list) - len(specific_content) -1)
            for i, char in enumerate(specific_content):
                content_list[start_index + i] = char
        else:
            # If specific content is too large, just use it (or part of it)
            content_list = list(specific_content[:len(content_list)])
            
    return ''.join(content_list)


def setup_search_test_environment(base_dir, num_files, file_sizes_kb, keywords):
    """Sets up a directory with files for search testing."""
    test_env_dir = os.path.join(str(base_dir), "content_search_env")
    os.makedirs(test_env_dir, exist_ok=True)
    file_paths = []

    for i in range(num_files):
        size_kb = random.choice(file_sizes_kb)
        # Embed a keyword in some files
        embedded_keyword = random.choice(keywords + [None]) # Chance to have no keyword
        content = generate_random_content(size_kb, specific_content=embedded_keyword)
        file_path = os.path.join(test_env_dir, f"search_file_{i}_size_{size_kb}kb.txt")
        create_file(file_path, content)
        file_paths.append(file_path)
        if embedded_keyword:
            print(f"Created file {file_path} containing keyword: '{embedded_keyword}'")
        else:
            print(f"Created file {file_path} with random content.")
            
    return test_env_dir, file_paths


def perform_search_in_file(file_path, query):
    """Reads a file and searches for a query string using mindlink's search_in_file."""
    try:
        matches = search_in_file(file_path, query)
        return len(matches) > 0 # Return True if any matches are found
    except Exception as e:
        print(f"Error reading or searching file {file_path}: {e}")
        return False


def test_content_search_performance():
    """Test the performance of searching content within files."""
    # Ensure SAFE_BASE_DIR exists
    os.makedirs(str(SAFE_BASE_DIR), exist_ok=True)
    
    test_dir_base = os.path.join(str(SAFE_BASE_DIR), 'test_content_search')
    os.makedirs(test_dir_base, exist_ok=True)
    
    search_env_dir = None # Initialize to ensure it's defined for finally block

    try:
        print("\n=== Testing Content Search Performance ===")
        
        # Test parameters
        num_files = 20
        file_sizes_kb = [1, 5, 10, 50] # KB
        keywords_to_search = ["important_keyword", "special_phrase", "unique_identifier_123", "another_test_string"]
        search_iterations = 5

        search_env_dir, file_paths = setup_search_test_environment(test_dir_base, num_files, file_sizes_kb, keywords_to_search)
        
        total_search_time = 0
        found_count = 0
        searches_performed = 0

        for iteration in range(search_iterations):
            query = random.choice(keywords_to_search)
            print(f"\nIteration {iteration + 1}/{search_iterations}, Searching for: '{query}'")
            
            # timer_for_iteration will hold the Timer object yielded by measure_time
            with measure_time(f"Searching for '{query}' in {len(file_paths)} files") as t_obj:
                for file_path_in_loop in file_paths: # Renamed to avoid conflict if file_path is used outside
                    if perform_search_in_file(file_path_in_loop, query):
                        found_count += 1
                    searches_performed +=1
            # After the 'with' block, t_obj.duration contains the measured time
            current_search_time = t_obj.duration 
            total_search_time += current_search_time

        avg_search_time_per_query = total_search_time / search_iterations if search_iterations > 0 else 0
        avg_time_per_file_search = total_search_time / searches_performed if searches_performed > 0 else 0

        print(f"\n--- Search Performance Summary ---")
        print(f"Total files searched across all iterations: {searches_performed}")
        print(f"Total occurrences found: {found_count}")
        print(f"Total time spent on searches: {total_search_time:.4f} seconds")
        print(f"Average search time per query batch: {avg_search_time_per_query:.4f} seconds")
        print(f"Average time per individual file search operation: {avg_time_per_file_search:.6f} seconds")

        # Basic assertion
        assert total_search_time > 0, "Search test did not run or took no time."
        # A more robust assertion would depend on expected content and search success
        # For now, we just check that searches were performed.
        assert searches_performed == num_files * search_iterations

    finally:
        # Clean up
        if search_env_dir and os.path.exists(search_env_dir):
            shutil.rmtree(search_env_dir)
        # Clean up the base test directory if it's empty or only contains expected remnants
        if os.path.exists(test_dir_base):
             try:
                if not os.listdir(test_dir_base):
                    os.rmdir(test_dir_base)
                else:
                    # If other tests might use subdirs in test_dir_base, be more careful
                    # For now, if it was created for this test, attempt removal
                    shutil.rmtree(test_dir_base, ignore_errors=True) 
             except OSError as e:
                print(f"Error cleaning up {test_dir_base}: {e}")

def run_all_tests():
    """Run all performance tests."""
    print("\n=== Running Content Search Performance Tests ===")
    
    start_time = time.time()
    
    test_content_search_performance()
    
    end_time = time.time()
    total_time = end_time - start_time
    
    print(f"\n=== All content search tests completed in {total_time:.2f} seconds ===")


if __name__ == "__main__":
    # A bit of setup to make Path available if not already imported
    # from pathlib import Path # Removed from here as it's now at the top
    run_all_tests()