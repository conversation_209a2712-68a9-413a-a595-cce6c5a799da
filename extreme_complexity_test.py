"""
Extreme Complexity Test for MindLink Agent Core.

This test pushes the MindLink Agent to its absolute limits with a particularly complex scenario:
1. Multi-stage project generation with interdependent components
2. Adversarial prompts that require robust error handling
3. Memory-intensive parallel operations
4. Complex reasoning chains with validation logic
5. Dynamic tool selection based on contextual requirements

This test evaluates the agent's ability to handle extraordinarily complex tasks
while maintaining performance, accuracy, and resource efficiency.
"""

import os
import sys
import time
import tempfile
import shutil
import random
import string
import json
import gc
import threading
import multiprocessing
import concurrent.futures
import traceback
import statistics
import uuid
import logging
from pathlib import Path
from typing import Dict, List, Any, Tuple, Optional, Union
from dataclasses import dataclass, field
from contextlib import contextmanager
import importlib
import psutil
import numpy as np
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, TimeoutError

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('extreme_complexity_test.log')
    ]
)
logger = logging.getLogger(__name__)

# Add the current directory to the path so we can import mindlink
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import core components
try:
    from mindlink import run_agent
    from mindlink.agent import AgentOS
    from mindlink.models.openrouter import OpenRouterModel
    from mindlink.models.openai import OpenAIModel
    from mindlink.models.llm import LLMInterface
    from mindlink.config import DEFAULT_SYSTEM_PROMPT
    from mindlink.schemas.mindlink import MindLinkRequest, MindLinkResponse, Action
    from mindlink.executor import ToolExecutor
    from mindlink.tools import (
        CreateFileTool,
        ReadFileTool,
        ListFilesTool,
        RunShellCommandTool,
        InsertASTNodeTool,
        SemanticSuggestTool,
        RunCodeTool,
        SnapshotTool,
        GenerateGraphTool,
        HoverDocTool,
        GenerateCallGraphTool
    )
    from mindlink.tools.file_tools import (
        TransactionManager,
        create_file,
        read_file,
        list_files,
        path_exists
    )
    
    IMPORTS_SUCCESSFUL = True
    logger.info("[SUCCESS] All imports successful")
except ImportError as e:
    IMPORTS_SUCCESSFUL = False
    logger.error(f"[ERROR] Import error: {e}")
    traceback.print_exc()

# Test configuration
MAX_CONCURRENT_TASKS = 3
OPERATIONS_PER_TEST = 10
TEST_TIMEOUT = 180
MEMORY_LIMIT_MB = 768
MAX_RECURSION_DEPTH = 10
MAX_FILE_OPERATIONS = 50
LOG_EVERY_N_SECONDS = 5

@dataclass
class PerformanceMetrics:
    """Container for detailed performance metrics."""
    # Time metrics
    start_time: float = field(default_factory=time.time)
    end_time: float = 0.0
    operation_times: Dict[str, List[float]] = field(default_factory=dict)
    response_times: Dict[str, List[float]] = field(default_factory=dict)
    
    # Resource usage
    memory_usage: List[Tuple[str, float]] = field(default_factory=list)
    cpu_usage: List[Tuple[str, float]] = field(default_factory=list)
    peak_memory_mb: float = 0.0
    
    # Success metrics
    success_rates: Dict[str, float] = field(default_factory=dict)
    error_counts: Dict[str, int] = field(default_factory=dict)
    
    # LLM specific metrics
    token_usage: Dict[str, int] = field(default_factory=dict)
    total_tokens: int = 0
    
    # Test-specific metrics
    test_results: Dict[str, Any] = field(default_factory=dict)
    
    def add_operation_time(self, operation_name: str, time_taken: float):
        """Add operation time to metrics."""
        if operation_name not in self.operation_times:
            self.operation_times[operation_name] = []
        self.operation_times[operation_name].append(time_taken)
    
    def add_error(self, operation_name: str):
        """Increment error count for operation."""
        if operation_name not in self.error_counts:
            self.error_counts[operation_name] = 0
        self.error_counts[operation_name] += 1
    
    def calculate_success_rates(self):
        """Calculate success rates for each operation."""
        for op_name, times in self.operation_times.items():
            total_attempts = len(times)
            errors = self.error_counts.get(op_name, 0)
            success_rate = (total_attempts - errors) / total_attempts if total_attempts > 0 else 0
            self.success_rates[op_name] = success_rate
    
    def finalize(self):
        """Finalize metrics calculation."""
        self.end_time = time.time()
        self.calculate_success_rates()
        
    def get_total_time(self):
        """Get total test time in seconds."""
        if self.end_time == 0:
            return time.time() - self.start_time
        return self.end_time - self.start_time

@contextmanager
def measure_resources(operation_name: str, metrics: PerformanceMetrics):
    """Context manager to measure execution time, memory and CPU usage."""
    process = psutil.Process(os.getpid())
    start_time = time.time()
    mem_before = process.memory_info().rss / 1024 / 1024  # MB
    cpu_times_before = process.cpu_times()
    
    try:
        yield
    finally:
        # Measure final resource usage
        end_time = time.time()
        mem_after = process.memory_info().rss / 1024 / 1024  # MB
        cpu_times_after = process.cpu_times()
        
        # Calculate metrics
        execution_time = end_time - start_time
        memory_delta = mem_after - mem_before
        cpu_delta = sum(cpu_times_after) - sum(cpu_times_before)
        
        # Update metrics
        metrics.add_operation_time(operation_name, execution_time)
        metrics.memory_usage.append((operation_name, memory_delta))
        metrics.cpu_usage.append((operation_name, cpu_delta))
        
        # Update peak memory
        metrics.peak_memory_mb = max(metrics.peak_memory_mb, mem_after)
        
        logger.debug(f"{operation_name}: time={execution_time:.3f}s, mem_delta={memory_delta:.1f}MB, cpu={cpu_delta:.2f}s")

def generate_complex_project_task() -> str:
    """Generate a complex project creation task that requires multi-step planning."""
    project_types = [
        "REST API with authentication, database models, and documentation",
        "Data analysis pipeline with visualization, caching, and reporting",
        "Machine learning model with data preprocessing, training, and evaluation",
        "Web scraper with async capabilities, result storage, and error handling",
        "CLI tool with multiple commands, configuration options, and plugins"
    ]
    
    task_template = """
    Create a complete Python project for a {project_type}. The project should include:
    
    1. A proper directory structure following best practices
    2. Well-documented code with appropriate comments
    3. Unit tests for core functionality
    4. A README.md with installation and usage instructions
    5. A requirements.txt file with all dependencies
    6. Error handling for edge cases
    7. Configuration management
    8. Proper logging setup
    
    Additional requirements:
    - Use modern Python features (3.8+)
    - Implement at least one design pattern appropriate for this type of project
    - Include type hints throughout the codebase
    - Make the code extensible for future features
    """
    
    return task_template.format(project_type=random.choice(project_types))

def generate_adversarial_prompt() -> str:
    """Generate challenging prompts designed to confuse the agent."""
    prompts = [
        "Create a recursive function that has the potential to create infinite recursion but implement safeguards to prevent it",
        "Write code that appears to have a memory leak but actually doesn't",
        "Create a function that appears simple but has subtle edge cases that need handling",
        "Generate valid Python code with extremely deep nested control structures",
        "Create a module with circular imports that still works correctly",
        "Implement a function that takes inconsistent inputs but needs to normalize them safely",
        "Write code with ambiguous variable names but add clear documentation",
        "Create a multi-threaded application with potential race conditions but implement proper locks",
        "Design a class hierarchy that looks like it might cause the diamond problem but avoid it"
    ]
    return random.choice(prompts)

def test_complex_project_generation(temp_dir: str, metrics: PerformanceMetrics) -> bool:
    """Test the agent's ability to generate a complex project."""
    task = generate_complex_project_task()
    
    with measure_resources("complex_project_generation", metrics):
        logger.info(f"Starting complex project generation with goal: {task[:100]}...")
        os.chdir(temp_dir)
        
        try:
            # Run agent with only the task
            success, result, history = run_agent(task)
            metrics.token_usage["complex_project"] = sum(msg.get("tokens", 0) for msg in history if isinstance(msg, dict) and "tokens" in msg)
            metrics.total_tokens += metrics.token_usage["complex_project"]
            
            # Validate result
            validation_score = validate_project_structure(temp_dir)
            metrics.test_results["project_validation_score"] = validation_score
            
            if not success:
                metrics.add_error("complex_project_generation")
                logger.error(f"Project generation failed: {result}")
                return False
                
            logger.info(f"Project generation completed with validation score: {validation_score}/10")
            return validation_score >= 6  # Threshold for success
            
        except Exception as e:
            metrics.add_error("complex_project_generation")
            logger.error(f"Error in complex project generation: {str(e)}")
            return False

def validate_project_structure(project_dir: str) -> int:
    """Validate the generated project structure and return a score out of 10."""
    score = 0
    
    # Check for common files
    common_files = ["README.md", "requirements.txt", "setup.py"]
    for file in common_files:
        if os.path.exists(os.path.join(project_dir, file)):
            score += 1
    
    # Check for tests directory
    if os.path.exists(os.path.join(project_dir, "tests")):
        score += 1
    
    # Check for Python modules
    py_files = [f for f in os.listdir(project_dir) if f.endswith(".py")]
    if len(py_files) >= 3:  # At least a few Python files
        score += 1
    
    # Check for documentation
    if os.path.exists(os.path.join(project_dir, "docs")) or os.path.exists(os.path.join(project_dir, "README.md")):
        with open(os.path.join(project_dir, "README.md"), "r") as f:
            readme = f.read()
            if len(readme) > 500:  # Reasonable README size
                score += 1
    
    # Check for proper directory structure (more than just flat files)
    dirs = [d for d in os.listdir(project_dir) if os.path.isdir(os.path.join(project_dir, d))]
    if len(dirs) >= 2:
        score += 1
    
    # Check for code quality in Python files
    try:
        for py_file in py_files[:3]:  # Check up to 3 Python files
            with open(os.path.join(project_dir, py_file), "r") as f:
                content = f.read()
                # Check for docstrings
                if '"""' in content or "'''" in content:
                    score += 0.5
                    break
    except:
        pass
    
    # Check for error handling
    try:
        has_error_handling = False
        for py_file in py_files[:5]:
            with open(os.path.join(project_dir, py_file), "r") as f:
                content = f.read()
                if "except" in content:
                    has_error_handling = True
                    score += 1
                    break
    except:
        pass
    
    # Round the score and ensure it's at most 10
    return min(round(score), 10)

def test_adversarial_handling(temp_dir: str, metrics: PerformanceMetrics) -> bool:
    """Test the agent's ability to handle adversarial prompts."""
    task = generate_adversarial_prompt()
    
    with measure_resources("adversarial_handling", metrics):
        logger.info(f"Starting adversarial handling test with prompt: {task}")
        os.chdir(temp_dir)
        
        try:
            # Run agent with only the task
            success, result, history = run_agent(task)
            metrics.token_usage["adversarial"] = sum(msg.get("tokens", 0) for msg in history if isinstance(msg, dict) and "tokens" in msg)
            metrics.total_tokens += metrics.token_usage["adversarial"]
            
            # Check for potential execution
            if "def " in result and "def main" in result:
                # Try to execute the code safely
                code_execution_result = safe_execute_code(result)
                metrics.test_results["adversarial_code_execution"] = code_execution_result
            
            if not success:
                metrics.add_error("adversarial_handling")
                logger.error(f"Adversarial handling failed: {result}")
                return False
                
            logger.info(f"Adversarial handling completed successfully")
            return True
            
        except Exception as e:
            metrics.add_error("adversarial_handling")
            logger.error(f"Error in adversarial handling: {str(e)}")
            return False

def safe_execute_code(code: str) -> dict:
    """Safely execute Python code in a restricted environment and return results."""
    result = {
        "executed": False,
        "success": False,
        "error": None,
        "output": None,
        "execution_time": 0
    }
    
    # Create a temporary file
    with tempfile.NamedTemporaryFile(suffix='.py', delete=False) as temp_file:
        temp_filename = temp_file.name
        temp_file.write(code.encode('utf-8'))
    
    try:
        # Execute with timeout and resource limits
        start_time = time.time()
        
        # Use subprocess for isolation
        import subprocess
        proc = subprocess.Popen(
            [sys.executable, temp_filename],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        try:
            stdout, stderr = proc.communicate(timeout=10)
            execution_time = time.time() - start_time
            
            result["executed"] = True
            result["success"] = proc.returncode == 0
            result["output"] = stdout
            result["error"] = stderr if stderr else None
            result["execution_time"] = execution_time
            
        except subprocess.TimeoutExpired:
            proc.kill()
            result["executed"] = True
            result["success"] = False
            result["error"] = "Execution timed out"
    
    except Exception as e:
        result["error"] = str(e)
    
    finally:
        # Clean up
        if os.path.exists(temp_filename):
            os.unlink(temp_filename)
    
    return result

def test_concurrent_operations(temp_dir: str, metrics: PerformanceMetrics) -> bool:
    """Test the agent's ability to handle multiple concurrent operations."""
    # Use fewer tasks (only first 3)
    tasks = [
        "Create a simple Python script that calculates the Fibonacci sequence up to the 20th number",
        "Create a function to check if a string is a palindrome",
        "Write a Python class representing a basic calculator with add, subtract, multiply, and divide methods"
    ]
    
    with measure_resources("concurrent_operations", metrics):
        logger.info(f"Starting concurrent operations test with {len(tasks)} tasks")
        
        results = []
        token_counts = []
        
        with ThreadPoolExecutor(max_workers=min(3, len(tasks))) as executor:
            futures = []
            
            for i, task in enumerate(tasks):
                task_dir = os.path.join(temp_dir, f"task_{i}")
                os.makedirs(task_dir, exist_ok=True)
                
                # Create a separate function to run in the task directory
                def run_in_task_dir(task_dir, task):
                    original_dir = os.getcwd()
                    os.chdir(task_dir)
                    try:
                        return run_agent(task)
                    finally:
                        os.chdir(original_dir)
                
                futures.append(executor.submit(run_in_task_dir, task_dir, task))
            
            for i, future in enumerate(concurrent.futures.as_completed(futures)):
                try:
                    success, result, history = future.result()
                    tokens = sum(msg.get("tokens", 0) for msg in history if isinstance(msg, dict) and "tokens" in msg)
                    token_counts.append(tokens)
                    results.append((success, tasks[i][:30] + "..."))
                    
                    if not success:
                        metrics.add_error("concurrent_operations")
                        
                except Exception as e:
                    metrics.add_error("concurrent_operations")
                    results.append((False, str(e)))
        
        # Record metrics
        success_count = sum(1 for success, _ in results if success)
        success_rate = success_count / len(tasks)
        metrics.test_results["concurrent_success_rate"] = success_rate
        metrics.token_usage["concurrent"] = sum(token_counts)
        metrics.total_tokens += metrics.token_usage["concurrent"]
        
        logger.info(f"Concurrent operations completed with success rate: {success_rate:.2f}")
        return success_rate >= 0.7  # 70% success threshold

# Add a safe directory cleanup function
def safe_cleanup_directory(directory):
    """
    Safely clean up a directory without causing recursion errors.
    Uses a custom approach to avoid deep recursion.
    """
    import os
    import stat
    import shutil
    
    if not os.path.exists(directory):
        return
    
    def onerror(func, path, exc_info):
        """Error handler for permission issues"""
        if not os.access(path, os.W_OK):
            # Change permissions to allow deletion
            os.chmod(path, stat.S_IWUSR)
            func(path)  # Try again
        else:
            raise
    
    # Use shutil.rmtree with error handling
    try:
        shutil.rmtree(directory, onerror=onerror)
        logger.info(f"Successfully cleaned directory: {directory}")
    except Exception as e:
        logger.error(f"Error during directory cleanup: {e}")

def run_extreme_complexity_test():
    """Run the extreme complexity performance test."""
    if not IMPORTS_SUCCESSFUL:
        logger.error("Required imports failed, cannot run test.")
        return
    
    # Check for API keys
    api_key_found = False
    # Default key from README.md
    default_key = "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"
    
    if os.environ.get("OPENAI_API_KEY") and os.environ.get("MINDLINK_LLM_PROVIDER") == "openai":
        api_key_found = True
        logger.info("Using OpenAI provider")
    elif os.environ.get("OPENROUTER_API_KEY"):
        api_key_found = True
        logger.info("Using OpenRouter provider with environment variable API key")
    else:
        # Use default key from README
        os.environ["OPENROUTER_API_KEY"] = default_key
        api_key_found = True
        logger.info("Using OpenRouter provider with default API key")
    
    if not api_key_found:
        logger.error("No API keys found. Set OPENAI_API_KEY or OPENROUTER_API_KEY environment variables.")
        return
    
    # Create metrics collector
    metrics = PerformanceMetrics()
    
    # Create temporary directory for test
    temp_dir = tempfile.mkdtemp()
    logger.info(f"Created temporary test directory: {temp_dir}")
    
    try:
        # Run individual tests
        tests = [
            ("Complex Project Generation", lambda: test_complex_project_generation(temp_dir, metrics)),
            ("Adversarial Handling", lambda: test_adversarial_handling(temp_dir, metrics)),
            ("Concurrent Operations", lambda: test_concurrent_operations(temp_dir, metrics))
        ]
        
        overall_success = True
        
        for test_name, test_func in tests:
            logger.info(f"Starting test: {test_name}")
            success = test_func()
            overall_success = overall_success and success
            metrics.test_results[f"{test_name}_success"] = success
            logger.info(f"Test {test_name} {'succeeded' if success else 'failed'}")
    
    except Exception as e:
        logger.error(f"Test execution error: {str(e)}")
        traceback.print_exc()
        overall_success = False
    
    finally:
        # Finalize metrics
        metrics.finalize()
        
        # Generate report
        logger.info("Generating performance report...")
        print_performance_report(metrics)
        
        # Save report to file
        report_path = "extreme_complexity_report.md"
        save_performance_report(metrics, report_path)
        logger.info(f"Performance report saved to {report_path}")
        
        # Clean up temporary directory using our safe method
        logger.info(f"Cleaning up temporary directory: {temp_dir}")
        safe_cleanup_directory(temp_dir)
        
        return overall_success, metrics

def print_performance_report(metrics: PerformanceMetrics):
    """Print a detailed performance report."""
    print("\n" + "="*80)
    print(" "*30 + "PERFORMANCE TEST REPORT")
    print("="*80)
    
    print(f"\nTEST DURATION: {metrics.get_total_time():.2f} seconds")
    print(f"PEAK MEMORY USAGE: {metrics.peak_memory_mb:.2f} MB")
    print(f"TOTAL TOKEN USAGE: {metrics.total_tokens}")
    
    print("\nOPERATION PERFORMANCE:")
    for op_name, times in metrics.operation_times.items():
        avg_time = statistics.mean(times) if times else 0
        success_rate = metrics.success_rates.get(op_name, 0) * 100
        print(f"  {op_name}: avg_time={avg_time:.2f}s, success_rate={success_rate:.1f}%")
    
    print("\nTEST RESULTS:")
    for test_name, result in metrics.test_results.items():
        if isinstance(result, bool):
            print(f"  {test_name}: {'SUCCESS' if result else 'FAILED'}")
        elif isinstance(result, (int, float)):
            print(f"  {test_name}: {result}")
        else:
            print(f"  {test_name}: {str(result)[:50]}")
    
    print("\nERRORS:")
    if not metrics.error_counts:
        print("  No errors recorded")
    else:
        for op_name, count in metrics.error_counts.items():
            print(f"  {op_name}: {count} errors")
    
    print("="*80)

def save_performance_report(metrics: PerformanceMetrics, report_path: str):
    """Save the performance report to a Markdown file."""
    with open(report_path, "w") as f:
        f.write("# MindLink Agent Extreme Complexity Test Report\n\n")
        f.write(f"**Test Date:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        f.write("## Summary\n\n")
        f.write(f"* **Test Duration:** {metrics.get_total_time():.2f} seconds\n")
        f.write(f"* **Peak Memory Usage:** {metrics.peak_memory_mb:.2f} MB\n")
        f.write(f"* **Total Token Usage:** {metrics.total_tokens}\n\n")
        
        f.write("## Test Results\n\n")
        f.write("| Test | Result |\n")
        f.write("|------|--------|\n")
        for test_name, result in metrics.test_results.items():
            if isinstance(result, bool):
                f.write(f"| {test_name} | {'✅ SUCCESS' if result else '❌ FAILED'} |\n")
            elif isinstance(result, (int, float)):
                f.write(f"| {test_name} | {result} |\n")
            else:
                f.write(f"| {test_name} | {str(result)[:50]} |\n")
        
        f.write("\n## Operation Performance\n\n")
        f.write("| Operation | Avg Time (s) | Success Rate |\n")
        f.write("|-----------|--------------|-------------|\n")
        for op_name, times in metrics.operation_times.items():
            avg_time = statistics.mean(times) if times else 0
            success_rate = metrics.success_rates.get(op_name, 0) * 100
            f.write(f"| {op_name} | {avg_time:.2f} | {success_rate:.1f}% |\n")
        
        f.write("\n## Resource Usage\n\n")
        f.write("### Memory Usage\n\n")
        f.write("| Operation | Memory Delta (MB) |\n")
        f.write("|-----------|-------------------|\n")
        for op_name, mem_delta in metrics.memory_usage:
            f.write(f"| {op_name} | {mem_delta:.2f} |\n")
        
        f.write("\n### CPU Usage\n\n")
        f.write("| Operation | CPU Time (s) |\n")
        f.write("|-----------|-------------|\n")
        for op_name, cpu_time in metrics.cpu_usage:
            f.write(f"| {op_name} | {cpu_time:.2f} |\n")
        
        f.write("\n## Errors\n\n")
        if not metrics.error_counts:
            f.write("No errors recorded.\n")
        else:
            f.write("| Operation | Error Count |\n")
            f.write("|-----------|-------------|\n")
            for op_name, count in metrics.error_counts.items():
                f.write(f"| {op_name} | {count} |\n")

if __name__ == "__main__":
    print("Starting Extreme Complexity Test for MindLink Agent...")
    success, metrics = run_extreme_complexity_test()
    print(f"Test completed {'successfully' if success else 'with failures'}.")
    sys.exit(0 if success else 1) 