import os
import sys
import win32security
import ntsecuritycon as con

def set_full_control(directory):
    try:
        # Get the current user's SID
        username = os.getenv('USERNAME')
        domain = os.getenv('USERDOMAIN')
        user_sid = win32security.ConvertSidToStringSid(
            win32security.LookupAccountName(domain, username)[0]
        )
        
        # Get the security descriptor of the directory
        sd = win32security.GetNamedSecurityInfo(
            directory,
            win32security.SE_FILE_OBJECT,
            win32security.DACL_SECURITY_INFORMATION
        )
        
        # Create a new DACL
        dacl = win32security.ACL()
        
        # Add full control for the current user
        dacl.AddAccessAllowedAce(
            win32security.ACL_REVISION,
            con.FILE_GENERIC_READ | con.FILE_GENERIC_WRITE | con.FILE_GENERIC_EXECUTE,
            user_sid
        )
        
        # Add full control for SYSTEM
        dacl.AddAccessAllowedAce(
            win32security.ACL_REVISION,
            con.FILE_ALL_ACCESS,
            win32security.ConvertStringSidToSid("S-1-5-18")
        )
        
        # Apply the new DACL
        win32security.SetNamedSecurityInfo(
            directory,
            win32security.SE_FILE_OBJECT,
            win32security.DACL_SECURITY_INFORMATION,
            None, None, dacl, None
        )
        
        print(f"Successfully set full control permissions on {directory}")
        return True
        
    except Exception as e:
        print(f"Error setting permissions: {str(e)}")
        return False

if __name__ == "__main__":
    target_dir = r"D:\3"
    print(f"Setting full control permissions for {target_dir}...")
    if set_full_control(target_dir):
        print("Permissions set successfully!")
    else:
        print("Failed to set permissions.")
        sys.exit(1)
