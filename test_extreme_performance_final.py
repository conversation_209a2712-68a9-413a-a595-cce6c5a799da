"""
Extreme Performance Test for MindLink Agent Core.

This test is designed to thoroughly evaluate the performance, reliability, and 
functionality of the MindLink Agent Core under extreme conditions. It tests:

1. Circular import resolution
2. LLM integration with both OpenAI and OpenRouter
3. Concurrent file operations
4. Transaction manager rollback
5. Complex multi-step agent tasks
6. Error handling and recovery
7. Memory usage and performance metrics

Usage:
    python test_extreme_performance_final.py
"""

import os
import sys
import time
import tempfile
import shutil
import random
import string
import json
import gc
import threading
import multiprocessing
import concurrent.futures
import traceback
import statistics
from pathlib import Path
from typing import Dict, List, Any, Tuple, Optional
from dataclasses import dataclass
from contextlib import contextmanager
import importlib
import psutil
import pytest

# Add the current directory to the path so we can import mindlink
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import core components - we'll test circular import resolution
try:
    # Force reload to ensure we're testing the current state
    for module_name in list(sys.modules.keys()):
        if module_name.startswith('mindlink.tools.'):
            del sys.modules[module_name]
    if 'mindlink.tools' in sys.modules:
        del sys.modules['mindlink.tools']
    
    # Try to import all tools
    from mindlink.tools import (
        CreateFileTool,
        ReadFileTool,
        ListFilesTool,
        RunShellCommandTool,
        InsertASTNodeTool,
        SemanticSuggestTool,
        RunCodeTool,
        SnapshotTool,
        GenerateGraphTool,
        HoverDocTool,
        GenerateCallGraphTool
    )
    from mindlink.tools.file_tools import (
        TransactionManager,
        create_file,
        read_file,
        list_files,
        path_exists
    )
    from mindlink.tools.base import Tool, tool_registry
    
    # Import agent and models
    from mindlink.agent import AgentOS
    from mindlink.models.openrouter import OpenRouterModel
    from mindlink.models.openai import OpenAIModel
    from mindlink.models.llm import LLMInterface
    from mindlink.config import DEFAULT_SYSTEM_PROMPT
    from mindlink.schemas.mindlink import MindLinkRequest, MindLinkResponse, Action
    from mindlink.executor import ToolExecutor
    
    IMPORTS_SUCCESSFUL = True
    print("✅ All imports successful - no circular import issues detected")
except ImportError as e:
    IMPORTS_SUCCESSFUL = False
    print(f"❌ Import error: {e}")
    traceback.print_exc()


# Test configuration
MAX_CONCURRENT_TASKS = 10
FILE_SIZES_KB = [1, 10, 100, 1000]  # Different file sizes to test
OPERATIONS_PER_TEST = 100
TEST_TIMEOUT = 300  # 5 minutes timeout for the entire test


@dataclass
class PerformanceMetrics:
    """Container for performance metrics."""
    operation_times: Dict[str, List[float]] = None
    memory_usage: List[Tuple[str, int]] = None
    success_rates: Dict[str, float] = None
    error_counts: Dict[str, int] = None
    
    def __post_init__(self):
        self.operation_times = {}
        self.memory_usage = []
        self.success_rates = {}
        self.error_counts = {}


def generate_random_content(size_kb: int) -> str:
    """Generate random content of specified size in KB."""
    chars = string.ascii_letters + string.digits + '\n'
    return ''.join(random.choice(chars) for _ in range(size_kb * 1024))


@contextmanager
def measure_time(operation_name: str, metrics: PerformanceMetrics):
    """Context manager to measure execution time of an operation."""
    start_time = time.time()
    try:
        yield
    finally:
        execution_time = time.time() - start_time
        if operation_name not in metrics.operation_times:
            metrics.operation_times[operation_name] = []
        metrics.operation_times[operation_name].append(execution_time)


@contextmanager
def measure_memory(operation_name: str, metrics: PerformanceMetrics):
    """Context manager to measure memory usage during an operation."""
    process = psutil.Process(os.getpid())
    mem_before = process.memory_info().rss / 1024 / 1024  # MB
    try:
        yield
    finally:
        # Force garbage collection to get accurate memory usage
        gc.collect()
        mem_after = process.memory_info().rss / 1024 / 1024  # MB
        metrics.memory_usage.append((operation_name, mem_after - mem_before))


def test_file_operations_performance(temp_dir: str, metrics: PerformanceMetrics):
    """Test performance of file operations."""
    print("\n🔍 Testing file operations performance...")
    
    # Create files of different sizes
    for size_kb in FILE_SIZES_KB:
        operation_name = f"create_file_{size_kb}kb"
        content = generate_random_content(size_kb)
        
        # Test file creation
        with measure_time(operation_name, metrics):
            with measure_memory(operation_name, metrics):
                file_path = os.path.join(temp_dir, f"test_file_{size_kb}kb.txt")
                create_file(file_path, content)
        
        # Test file reading
        operation_name = f"read_file_{size_kb}kb"
        with measure_time(operation_name, metrics):
            with measure_memory(operation_name, metrics):
                read_content = read_file(file_path)
                assert len(read_content) == len(content), f"File content size mismatch for {size_kb}kb file"
    
    # Test listing files
    with measure_time("list_files", metrics):
        with measure_memory("list_files", metrics):
            files = list_files(temp_dir)
            assert len(files) >= len(FILE_SIZES_KB), "Not all test files were listed"
    
    print(f"✅ File operations performance test completed with {len(FILE_SIZES_KB)} file sizes")


def test_concurrent_file_operations(temp_dir: str, metrics: PerformanceMetrics):
    """Test performance of concurrent file operations."""
    print("\n🔍 Testing concurrent file operations...")
    
    def create_and_read_file(file_idx: int, size_kb: int) -> bool:
        """Create and read a file, return success status."""
        try:
            file_path = os.path.join(temp_dir, f"concurrent_file_{file_idx}_{size_kb}kb.txt")
            content = generate_random_content(size_kb)
            
            # Create file
            create_file(file_path, content)
            
            # Read file
            read_content = read_file(file_path)
            
            # Verify content
            return len(read_content) == len(content)
        except Exception as e:
            print(f"Error in concurrent operation {file_idx}: {e}")
            return False
    
    # Run concurrent operations
    tasks = []
    with concurrent.futures.ThreadPoolExecutor(max_workers=MAX_CONCURRENT_TASKS) as executor:
        for i in range(OPERATIONS_PER_TEST):
            size_kb = random.choice(FILE_SIZES_KB)
            tasks.append(executor.submit(create_and_read_file, i, size_kb))
    
    # Check results
    successes = sum(1 for task in concurrent.futures.as_completed(tasks) if task.result())
    success_rate = (successes / len(tasks)) * 100
    
    metrics.success_rates["concurrent_file_operations"] = success_rate
    print(f"✅ Concurrent file operations test completed with {success_rate:.1f}% success rate")


def test_transaction_manager(temp_dir: str, metrics: PerformanceMetrics):
    """Test the transaction manager's performance and reliability."""
    print("\n🔍 Testing transaction manager...")
    
    # Test successful transaction
    with measure_time("transaction_success", metrics):
        with measure_memory("transaction_success", metrics):
            with TransactionManager():
                for i in range(10):
                    file_path = os.path.join(temp_dir, f"transaction_success_{i}.txt")
                    create_file(file_path, f"Transaction test content {i}")
    
    # Verify files were created
    success_files = [f for f in os.listdir(temp_dir) if f.startswith("transaction_success_")]
    assert len(success_files) == 10, f"Expected 10 files, found {len(success_files)}"
    
    # Test transaction rollback
    error_count = 0
    with measure_time("transaction_rollback", metrics):
        with measure_memory("transaction_rollback", metrics):
            try:
                with TransactionManager():
                    for i in range(10):
                        file_path = os.path.join(temp_dir, f"transaction_rollback_{i}.txt")
                        create_file(file_path, f"Transaction test content {i}")
                        if i == 5:
                            # Simulate an error
                            raise ValueError("Simulated error to test rollback")
            except ValueError:
                error_count += 1
    
    # Verify files were rolled back
    rollback_files = [f for f in os.listdir(temp_dir) if f.startswith("transaction_rollback_")]
    assert len(rollback_files) == 0, f"Expected 0 files after rollback, found {len(rollback_files)}"
    
    metrics.error_counts["transaction_rollback"] = error_count
    print(f"✅ Transaction manager test completed with {error_count} expected errors")


def test_nested_transactions(temp_dir: str, metrics: PerformanceMetrics):
    """Test nested transactions performance and reliability."""
    print("\n🔍 Testing nested transactions...")
    
    # Test successful nested transactions
    with measure_time("nested_transactions_success", metrics):
        with measure_memory("nested_transactions_success", metrics):
            with TransactionManager():
                create_file(os.path.join(temp_dir, "outer_1.txt"), "Outer transaction 1")
                
                with TransactionManager():
                    create_file(os.path.join(temp_dir, "inner_1.txt"), "Inner transaction 1")
                    
                    with TransactionManager():
                        create_file(os.path.join(temp_dir, "innermost_1.txt"), "Innermost transaction 1")
                
                create_file(os.path.join(temp_dir, "outer_2.txt"), "Outer transaction 2")
    
    # Verify all files were created
    expected_files = ["outer_1.txt", "inner_1.txt", "innermost_1.txt", "outer_2.txt"]
    for file_name in expected_files:
        assert os.path.exists(os.path.join(temp_dir, file_name)), f"File {file_name} not found"
    
    # Test nested transaction rollback
    error_count = 0
    with measure_time("nested_transactions_rollback", metrics):
        with measure_memory("nested_transactions_rollback", metrics):
            try:
                with TransactionManager():
                    create_file(os.path.join(temp_dir, "outer_rollback_1.txt"), "Outer rollback 1")
                    
                    with TransactionManager():
                        create_file(os.path.join(temp_dir, "inner_rollback_1.txt"), "Inner rollback 1")
                        raise ValueError("Simulated error in inner transaction")
                        
                    create_file(os.path.join(temp_dir, "outer_rollback_2.txt"), "Outer rollback 2")
            except ValueError:
                error_count += 1
    
    # Verify inner transaction files were rolled back
    inner_files = [f for f in os.listdir(temp_dir) if f.startswith("inner_rollback_")]
    assert len(inner_files) == 0, f"Expected 0 inner files after rollback, found {len(inner_files)}"
    
    # Verify outer transaction files were also rolled back
    outer_files = [f for f in os.listdir(temp_dir) if f.startswith("outer_rollback_")]
    assert len(outer_files) == 0, f"Expected 0 outer files after rollback, found {len(outer_files)}"
    
    metrics.error_counts["nested_transactions_rollback"] = error_count
    print(f"✅ Nested transactions test completed with {error_count} expected errors")


def test_tool_registry(metrics: PerformanceMetrics):
    """Test the tool registry's performance and reliability."""
    print("\n🔍 Testing tool registry...")
    
    with measure_time("tool_registry_access", metrics):
        with measure_memory("tool_registry_access", metrics):
            # Verify all expected tools are registered
            expected_tools = [
                "create_file", "read_file", "list_files", "run_shell_command",
                "insert_ast_node", "semantic_suggest", "run_code", "snapshot",
                "generate_graph", "hover_doc", "generate_call_graph", "finish"
            ]
            
            missing_tools = []
            for tool_name in expected_tools:
                if tool_name not in tool_registry:
                    missing_tools.append(tool_name)
            
            if missing_tools:
                print(f"⚠️ Missing tools in registry: {', '.join(missing_tools)}")
                metrics.error_counts["missing_tools"] = len(missing_tools)
            else:
                print(f"✅ All {len(expected_tools)} expected tools found in registry")
    
    # Test tool instantiation
    success_count = 0
    error_count = 0
    
    with measure_time("tool_instantiation", metrics):
        with measure_memory("tool_instantiation", metrics):
            for tool_name, tool_class in tool_registry.items():
                try:
                    tool_instance = tool_class()
                    assert isinstance(tool_instance, Tool), f"{tool_name} instance is not a Tool"
                    success_count += 1
                except Exception as e:
                    print(f"⚠️ Error instantiating tool {tool_name}: {e}")
                    error_count += 1
    
    metrics.success_rates["tool_instantiation"] = (success_count / (success_count + error_count)) * 100 if (success_count + error_count) > 0 else 0
    metrics.error_counts["tool_instantiation"] = error_count
    
    print(f"✅ Tool registry test completed with {success_count} successful and {error_count} failed instantiations")


class MockLLM(LLMInterface):
    """Mock LLM for testing without actual API calls."""
    
    def __init__(self, responses=None):
        self.responses = responses or []
        self.response_index = 0
        self.calls = []
    
    def generate(self, system_prompt, user_prompt, history=None):
        self.calls.append((system_prompt, user_prompt, history))
        
        if self.response_index < len(self.responses):
            response = self.responses[self.response_index]
            self.response_index += 1
            return response
        
        # Default response if we run out of predefined responses
        return json.dumps({
            "action": {
                "tool_name": "finish",
                "parameters": {
                    "result": "Task completed with mock LLM"
                }
            },
            "reasoning": "All steps completed successfully"
        })


def test_agent_performance(temp_dir: str, metrics: PerformanceMetrics):
    """Test the agent's performance with a mock LLM."""
    print("\n🔍 Testing agent performance with mock LLM...")
    
    # Define a sequence of mock responses for a multi-step task
    mock_responses = [
        # Step 1: Create a directory
        json.dumps({
            "action": {
                "tool_name": "run_shell_command",
                "parameters": {
                    "command": f"mkdir -p {os.path.join(temp_dir, 'agent_test_dir')}"
                }
            },
            "reasoning": "Creating a directory for the test"
        }),
        
        # Step 2: Create a file
        json.dumps({
            "action": {
                "tool_name": "create_file",
                "parameters": {
                    "path": os.path.join(temp_dir, "agent_test_dir", "test_file.txt"),
                    "content": "This is a test file created by the agent"
                }
            },
            "reasoning": "Creating a test file in the directory"
        }),
        
        # Step 3: List files in the directory
        json.dumps({
            "action": {
                "tool_name": "list_files",
                "parameters": {
                    "directory": os.path.join(temp_dir, "agent_test_dir")
                }
            },
            "reasoning": "Listing files to verify the file was created"
        }),
        
        # Step 4: Read the file
        json.dumps({
            "action": {
                "tool_name": "read_file",
                "parameters": {
                    "path": os.path.join(temp_dir, "agent_test_dir", "test_file.txt")
                }
            },
            "reasoning": "Reading the file to verify its contents"
        }),
        
        # Step 5: Finish
        json.dumps({
            "action": {
                "tool_name": "finish",
                "parameters": {
                    "result": "Successfully created directory, created file, listed files, and read file"
                }
            },
            "reasoning": "All steps completed successfully"
        })
    ]
    
    # Create mock LLM
    mock_llm = MockLLM(responses=mock_responses)
    
    # Create agent
    with measure_time("agent_initialization", metrics):
        with measure_memory("agent_initialization", metrics):
            agent = AgentOS(
                llm=mock_llm,
                system_prompt_template=DEFAULT_SYSTEM_PROMPT,
                max_steps=10
            )
    
    # Run agent
    with measure_time("agent_execution", metrics):
        with measure_memory("agent_execution", metrics):
            goal = "Create a directory, create a file in it, list the files, and read the file"
            success, result, history = agent.run(goal)
    
    # Verify results
    assert success, f"Agent execution failed: {result}"
    assert len(history) == len(mock_responses), f"Expected {len(mock_responses)} steps, got {len(history)}"
    assert os.path.exists(os.path.join(temp_dir, "agent_test_dir", "test_file.txt")), "Test file was not created"
    
    # Calculate metrics
    steps_success = sum(1 for entry in history if entry['response'].status == "success")
    steps_error = sum(1 for entry in history if entry['response'].status == "error")
    
    metrics.success_rates["agent_steps"] = (steps_success / len(history)) * 100 if len(history) > 0 else 0
    metrics.error_counts["agent_steps"] = steps_error
    
    print(f"✅ Agent performance test completed with {steps_success}/{len(history)} successful steps")


def test_tool_executor(temp_dir: str, metrics: PerformanceMetrics):
    """Test the tool executor's performance."""
    print("\n🔍 Testing tool executor...")
    
    # Create executor
    with measure_time("executor_initialization", metrics):
        with measure_memory("executor_initialization", metrics):
            executor = ToolExecutor()
    
    # Test various tool executions
    test_requests = [
        # Create file
        MindLinkRequest(
            action=Action(
                tool_name="create_file",
                parameters={
                    "path": os.path.join(temp_dir, "executor_test_file.txt"),
                    "content": "This is a test file created by the executor"
                }
            ),
            reasoning="Testing file creation"
        ),
        
        # Read file
        MindLinkRequest(
            action=Action(
                tool_name="read_file",
                parameters={
                    "path": os.path.join(temp_dir, "executor_test_file.txt")
                }
            ),
            reasoning="Testing file reading"
        ),
        
        # List files
        MindLinkRequest(
            action=Action(
                tool_name="list_files",
                parameters={
                    "directory": temp_dir
                }
            ),
            reasoning="Testing directory listing"
        ),
        
        # Invalid tool
        MindLinkRequest(
            action=Action(
                tool_name="nonexistent_tool",
                parameters={}
            ),
            reasoning="Testing error handling with invalid tool"
        )
    ]
    
    success_count = 0
    error_count = 0
    
    with measure_time("executor_execution", metrics):
        with measure_memory("executor_execution", metrics):
            for request in test_requests:
                response = executor.execute(request)
                
                if response.status == "success":
                    success_count += 1
                else:
                    error_count += 1
                    # The last request is expected to fail
                    if request.action.tool_name != "nonexistent_tool":
                        print(f"⚠️ Unexpected error: {response.error}")
    
    # Verify file was created
    assert os.path.exists(os.path.join(temp_dir, "executor_test_file.txt")), "Executor test file was not created"
    
    metrics.success_rates["executor_execution"] = (success_count / len(test_requests)) * 100
    metrics.error_counts["executor_execution"] = error_count
    
    # We expect 3 successes and 1 error (the invalid tool)
    expected_success = 3
    expected_error = 1
    
    assert success_count == expected_success, f"Expected {expected_success} successful executions, got {success_count}"
    assert error_count == expected_error, f"Expected {expected_error} error executions, got {error_count}"
    
    print(f"✅ Tool executor test completed with {success_count} successful and {error_count} error executions")


def print_performance_report(metrics: PerformanceMetrics):
    """Print a comprehensive performance report."""
    print("\n" + "="*80)
    print("📊 PERFORMANCE TEST REPORT")
    print("="*80)
    
    # Print operation times
    print("\n⏱️ Operation Times (seconds):")
    for operation, times in metrics.operation_times.items():
        if times:
            avg_time = sum(times) / len(times)
            min_time = min(times)
            max_time = max(times)
            print(f"  {operation:<30} Avg: {avg_time:.4f}  Min: {min_time:.4f}  Max: {max_time:.4f}")
    
    # Print memory usage
    print("\n💾 Memory Usage (MB):")
    for operation, usage in metrics.memory_usage:
        print(f"  {operation:<30} {usage:.2f} MB")
    
    # Print success rates
    print("\n✅ Success Rates (%):")
    for operation, rate in metrics.success_rates.items():
        print(f"  {operation:<30} {rate:.1f}%")
    
    # Print error counts
    print("\n❌ Error Counts:")
    for operation, count in metrics.error_counts.items():
        print(f"  {operation:<30} {count}")
    
    print("\n" + "="*80)


def run_performance_test():
    """Run the comprehensive performance test suite."""
    print("🚀 Starting Extreme Performance Test for MindLink Agent Core")
    print("="*80)
    
    # Skip tests if imports failed
    if not IMPORTS_SUCCESSFUL:
        print("❌ Skipping tests due to import errors")
        return
    
    # Create metrics container
    metrics = PerformanceMetrics()
    
    # Create temporary directory for tests
    temp_dir = tempfile.mkdtemp()
    print(f"📁 Created temporary test directory: {temp_dir}")
    
    try:
        # Record start time
        start_time = time.time()
        
        # Run tests
        test_file_operations_performance(temp_dir, metrics)
        test_concurrent_file_operations(temp_dir, metrics)
        test_transaction_manager(temp_dir, metrics)
        test_nested_transactions(temp_dir, metrics)
        test_tool_registry(metrics)
        test_agent_performance(temp_dir, metrics)
        test_tool_executor(temp_dir, metrics)
        
        # Record end time
        end_time = time.time()
        total_time = end_time - start_time
        
        # Print performance report
        print_performance_report(metrics)
        
        # Print overall results
        print(f"\n🏁 Performance test completed in {total_time:.2f} seconds")
        
        # Calculate overall success rate
        total_success_rate = statistics.mean(metrics.success_rates.values()) if metrics.success_rates else 0
        print(f"📈 Overall success rate: {total_success_rate:.1f}%")
        
        # Evaluate LLM connectivity
        print("\n🔍 Evaluating LLM connectivity:")
        
        # Check if OpenRouter is available
        try:
            llm = OpenRouterModel(model_name="deepseek-r1")
            print("✅ OpenRouter model initialized successfully")
        except Exception as e:
            print(f"❌ OpenRouter initialization failed: {e}")
        
        # Check if OpenAI is available (if API key is set)
        api_key = os.getenv("OPENAI_API_KEY")
        if api_key:
            try:
                llm = OpenAIModel(api_key=api_key)
                print("✅ OpenAI model initialized successfully")
            except Exception as e:
                print(f"❌ OpenAI initialization failed: {e}")
        else:
            print("⚠️ OpenAI API key not set, skipping OpenAI connectivity test")
        
    finally:
        # Clean up
        try:
            shutil.rmtree(temp_dir)
            print(f"🧹 Cleaned up temporary test directory")
        except Exception as e:
            print(f"⚠️ Error cleaning up temporary directory: {e}")
    
    print("\n✨ Performance test suite execution complete")


if __name__ == "__main__":
    run_performance_test()
