import sys
import os
import time
import json
from datetime import datetime
sys.path.append(r'd:/کتابخانه پایتون/2')
from mindlink import run_agent

print("=" * 80)
print("MINDLINK AGENT PERFORMANCE TEST - SINGLE COMPLEX TASK")
print("=" * 80)

# Define a complex task that tests multiple capabilities
complex_task = (
    "Create a Python script that implements a simplified blockchain with the following features:\n"
    "1. A Block class with timestamp, data, previous hash, and a method to calculate its own hash\n"
    "2. A Blockchain class that manages a chain of blocks and has methods to add blocks and validate the chain\n"
    "3. A simple proof-of-work algorithm requiring a hash starting with a specific pattern\n"
    "4. Add comments explaining the code and why blockchains are secure\n"
    "5. Add a main section that creates a blockchain, adds a few blocks, and validates the chain\n"
    "Save the code to a file called 'simple_blockchain.py' and ensure it runs correctly."
)

print(f"Running performance test with the following task:\n{complex_task}\n")

# Measure execution time
start_time = time.time()
success, result, history = run_agent(complex_task)
end_time = time.time()

execution_time = end_time - start_time
step_count = len(history)

# Count token usage
total_tokens = sum(msg.get("tokens", 0) for msg in history if isinstance(msg, dict) and "tokens" in msg)

# Print performance metrics
print("\n" + "=" * 80)
print("PERFORMANCE TEST RESULTS")
print("=" * 80)
print(f"Success: {success}")
print(f"Execution time: {execution_time:.2f} seconds")
print(f"Number of steps: {step_count}")
print(f"Total tokens used: {total_tokens}")
print(f"Tokens per second: {total_tokens/execution_time:.2f}")
print(f"Seconds per step: {execution_time/step_count:.2f}")

# Print result summary
print("\n" + "=" * 80)
print("RESULT SUMMARY")
print("=" * 80)
print(result[:500] + "..." if len(result) > 500 else result)

# Save report to file
report = {
    "timestamp": datetime.now().isoformat(),
    "task": complex_task,
    "performance": {
        "success": success,
        "execution_time_seconds": execution_time,
        "step_count": step_count,
        "total_tokens": total_tokens,
        "tokens_per_second": total_tokens/execution_time,
        "seconds_per_step": execution_time/step_count
    },
    "result": result
}

with open("simple_performance_report.json", "w") as f:
    json.dump(report, f, indent=2)

print(f"\nDetailed report saved to simple_performance_report.json")
print("=" * 80) 