from mindlink.agent import AgentOS
from mindlink.models.openrouter import OpenRouterModel
from mindlink.schemas.mindlink import Action, MindLinkRequest

print("Testing batch execution fix for generate_large_file...")

# Create test actions that simulate the problematic scenario
test_actions = [
    Action(tool_name='generate_large_file', parameters={
        'content_description': 'A Python calculator application with 300 lines of functional code'
    }),
    Action(tool_name='generate_large_file', parameters={
        'content_description': 'A Python web scraper with 300 lines of functional code',
        'path': 'scraper.py'  # This should get normalized
    }),
    Action(tool_name='generate_large_file', parameters={
        'content_description': 'A Python data analyzer with 300 lines of functional code',
        'path': '/absolute/path/analyzer.py'  # This should get normalized
    })
]

llm = OpenRouterModel()
agent = AgentOS(llm=llm, system_prompt_template='You are a helpful AI assistant.')

print("\nTesting individual action execution:")
for i, action in enumerate(test_actions):
    print(f"\nAction {i+1}: {action.tool_name}")
    print(f"Original parameters: {action.parameters}")
    
    # Test the _execute_tool method directly
    request = MindLinkRequest(action=action)
    result = agent._execute_tool(request)
    
    print(f"Result status: {result.status}")
    if result.status == 'error':
        print(f"Error: {result.observation}")
    else:
        print(f"Success: {result.observation[:100]}...")

print("\n" + "="*50)
print("Testing batch execution:")
result = agent.batch_execute_plan(test_actions)
print(f"Batch result status: {result.status}")
print(f"Batch result: {result.observation}")