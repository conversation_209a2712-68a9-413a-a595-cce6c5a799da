#!/usr/bin/env python3
"""
Comprehensive Python Utility Library
A collection of utility functions for data processing, mathematical operations,
file handling, string manipulation, and algorithm implementations.
"""

import os
import sys
import json
import csv
import re
import math
import random
import hashlib
import datetime
import itertools
from typing import List, Dict, Any, Optional, Union, Tuple
from collections import defaultdict, Counter
from pathlib import Path
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DataProcessor:
    """
    A comprehensive data processing utility class.
    """
    
    def __init__(self):
        self.data_cache = {}
        self.processing_stats = defaultdict(int)
    
    def load_json_file(self, filepath: str) -> Dict[str, Any]:
        """
        Load and parse a JSON file.
        
        Args:
            filepath (str): Path to the JSON file
            
        Returns:
            Dict[str, Any]: Parsed JSON data
        """
        try:
            with open(filepath, 'r', encoding='utf-8') as file:
                data = json.load(file)
                self.processing_stats['json_files_loaded'] += 1
                logger.info(f"Successfully loaded JSON file: {filepath}")
                return data
        except FileNotFoundError:
            logger.error(f"JSON file not found: {filepath}")
            return {}
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON format in {filepath}: {e}")
            return {}
    
    def save_json_file(self, data: Dict[str, Any], filepath: str) -> bool:
        """
        Save data to a JSON file.
        
        Args:
            data (Dict[str, Any]): Data to save
            filepath (str): Output file path
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            os.makedirs(os.path.dirname(filepath), exist_ok=True)
            with open(filepath, 'w', encoding='utf-8') as file:
                json.dump(data, file, indent=2, ensure_ascii=False)
                self.processing_stats['json_files_saved'] += 1
                logger.info(f"Successfully saved JSON file: {filepath}")
                return True
        except Exception as e:
            logger.error(f"Failed to save JSON file {filepath}: {e}")
            return False
    
    def load_csv_file(self, filepath: str, delimiter: str = ',') -> List[Dict[str, str]]:
        """
        Load and parse a CSV file.
        
        Args:
            filepath (str): Path to the CSV file
            delimiter (str): CSV delimiter
            
        Returns:
            List[Dict[str, str]]: List of dictionaries representing CSV rows
        """
        try:
            with open(filepath, 'r', encoding='utf-8') as file:
                reader = csv.DictReader(file, delimiter=delimiter)
                data = list(reader)
                self.processing_stats['csv_files_loaded'] += 1
                logger.info(f"Successfully loaded CSV file: {filepath} ({len(data)} rows)")
                return data
        except FileNotFoundError:
            logger.error(f"CSV file not found: {filepath}")
            return []
        except Exception as e:
            logger.error(f"Error loading CSV file {filepath}: {e}")
            return []
    
    def save_csv_file(self, data: List[Dict[str, Any]], filepath: str, delimiter: str = ',') -> bool:
        """
        Save data to a CSV file.
        
        Args:
            data (List[Dict[str, Any]]): Data to save
            filepath (str): Output file path
            delimiter (str): CSV delimiter
            
        Returns:
            bool: True if successful, False otherwise
        """
        if not data:
            logger.warning("No data to save to CSV file")
            return False
        
        try:
            os.makedirs(os.path.dirname(filepath), exist_ok=True)
            with open(filepath, 'w', newline='', encoding='utf-8') as file:
                fieldnames = data[0].keys()
                writer = csv.DictWriter(file, fieldnames=fieldnames, delimiter=delimiter)
                writer.writeheader()
                writer.writerows(data)
                self.processing_stats['csv_files_saved'] += 1
                logger.info(f"Successfully saved CSV file: {filepath} ({len(data)} rows)")
                return True
        except Exception as e:
            logger.error(f"Failed to save CSV file {filepath}: {e}")
            return False
    
    def filter_data(self, data: List[Dict[str, Any]], filters: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Filter data based on specified criteria.
        
        Args:
            data (List[Dict[str, Any]]): Input data
            filters (Dict[str, Any]): Filter criteria
            
        Returns:
            List[Dict[str, Any]]: Filtered data
        """
        filtered_data = []
        for item in data:
            match = True
            for key, value in filters.items():
                if key not in item or item[key] != value:
                    match = False
                    break
            if match:
                filtered_data.append(item)
        
        self.processing_stats['filter_operations'] += 1
        logger.info(f"Filtered data: {len(data)} -> {len(filtered_data)} items")
        return filtered_data
    
    def group_data(self, data: List[Dict[str, Any]], group_key: str) -> Dict[str, List[Dict[str, Any]]]:
        """
        Group data by a specified key.
        
        Args:
            data (List[Dict[str, Any]]): Input data
            group_key (str): Key to group by
            
        Returns:
            Dict[str, List[Dict[str, Any]]]: Grouped data
        """
        grouped_data = defaultdict(list)
        for item in data:
            if group_key in item:
                grouped_data[item[group_key]].append(item)
        
        self.processing_stats['group_operations'] += 1
        logger.info(f"Grouped data by '{group_key}': {len(grouped_data)} groups")
        return dict(grouped_data)
    
    def aggregate_data(self, data: List[Dict[str, Any]], agg_key: str, agg_func: str = 'sum') -> float:
        """
        Aggregate numeric data.
        
        Args:
            data (List[Dict[str, Any]]): Input data
            agg_key (str): Key to aggregate
            agg_func (str): Aggregation function ('sum', 'avg', 'min', 'max', 'count')
            
        Returns:
            float: Aggregated value
        """
        values = []
        for item in data:
            if agg_key in item:
                try:
                    values.append(float(item[agg_key]))
                except (ValueError, TypeError):
                    continue
        
        if not values:
            return 0.0
        
        if agg_func == 'sum':
            result = sum(values)
        elif agg_func == 'avg':
            result = sum(values) / len(values)
        elif agg_func == 'min':
            result = min(values)
        elif agg_func == 'max':
            result = max(values)
        elif agg_func == 'count':
            result = len(values)
        else:
            result = sum(values)  # Default to sum
        
        self.processing_stats['aggregate_operations'] += 1
        logger.info(f"Aggregated {len(values)} values using {agg_func}: {result}")
        return result

class MathUtils:
    """
    Mathematical utility functions.
    """
    
    @staticmethod
    def factorial(n: int) -> int:
        """
        Calculate factorial of a number.
        
        Args:
            n (int): Input number
            
        Returns:
            int: Factorial of n
        """
        if n < 0:
            raise ValueError("Factorial is not defined for negative numbers")
        if n == 0 or n == 1:
            return 1
        return n * MathUtils.factorial(n - 1)
    
    @staticmethod
    def fibonacci(n: int) -> int:
        """
        Calculate the nth Fibonacci number.
        
        Args:
            n (int): Position in Fibonacci sequence
            
        Returns:
            int: nth Fibonacci number
        """
        if n < 0:
            raise ValueError("Fibonacci is not defined for negative numbers")
        if n == 0:
            return 0
        if n == 1:
            return 1
        
        a, b = 0, 1
        for _ in range(2, n + 1):
            a, b = b, a + b
        return b
    
    @staticmethod
    def is_prime(n: int) -> bool:
        """
        Check if a number is prime.
        
        Args:
            n (int): Number to check
            
        Returns:
            bool: True if prime, False otherwise
        """
        if n < 2:
            return False
        if n == 2:
            return True
        if n % 2 == 0:
            return False
        
        for i in range(3, int(math.sqrt(n)) + 1, 2):
            if n % i == 0:
                return False
        return True
    
    @staticmethod
    def gcd(a: int, b: int) -> int:
        """
        Calculate the Greatest Common Divisor of two numbers.
        
        Args:
            a (int): First number
            b (int): Second number
            
        Returns:
            int: GCD of a and b
        """
        while b:
            a, b = b, a % b
        return abs(a)
    
    @staticmethod
    def lcm(a: int, b: int) -> int:
        """
        Calculate the Least Common Multiple of two numbers.
        
        Args:
            a (int): First number
            b (int): Second number
            
        Returns:
            int: LCM of a and b
        """
        return abs(a * b) // MathUtils.gcd(a, b)
    
    @staticmethod
    def power_mod(base: int, exponent: int, modulus: int) -> int:
        """
        Calculate (base^exponent) % modulus efficiently.
        
        Args:
            base (int): Base number
            exponent (int): Exponent
            modulus (int): Modulus
            
        Returns:
            int: Result of (base^exponent) % modulus
        """
        result = 1
        base = base % modulus
        while exponent > 0:
            if exponent % 2 == 1:
                result = (result * base) % modulus
            exponent = exponent >> 1
            base = (base * base) % modulus
        return result
    
    @staticmethod
    def generate_primes(limit: int) -> List[int]:
        """
        Generate all prime numbers up to a given limit using Sieve of Eratosthenes.
        
        Args:
            limit (int): Upper limit
            
        Returns:
            List[int]: List of prime numbers
        """
        if limit < 2:
            return []
        
        sieve = [True] * (limit + 1)
        sieve[0] = sieve[1] = False
        
        for i in range(2, int(math.sqrt(limit)) + 1):
            if sieve[i]:
                for j in range(i * i, limit + 1, i):
                    sieve[j] = False
        
        return [i for i in range(2, limit + 1) if sieve[i]]

class StringUtils:
    """
    String manipulation utility functions.
    """
    
    @staticmethod
    def reverse_string(s: str) -> str:
        """
        Reverse a string.
        
        Args:
            s (str): Input string
            
        Returns:
            str: Reversed string
        """
        return s[::-1]
    
    @staticmethod
    def is_palindrome(s: str, ignore_case: bool = True, ignore_spaces: bool = True) -> bool:
        """
        Check if a string is a palindrome.
        
        Args:
            s (str): Input string
            ignore_case (bool): Whether to ignore case
            ignore_spaces (bool): Whether to ignore spaces and punctuation
            
        Returns:
            bool: True if palindrome, False otherwise
        """
        if ignore_spaces:
            s = re.sub(r'[^a-zA-Z0-9]', '', s)
        if ignore_case:
            s = s.lower()
        return s == s[::-1]
    
    @staticmethod
    def count_words(text: str) -> int:
        """
        Count the number of words in a text.
        
        Args:
            text (str): Input text
            
        Returns:
            int: Number of words
        """
        return len(text.split())
    
    @staticmethod
    def count_characters(text: str, include_spaces: bool = False) -> int:
        """
        Count the number of characters in a text.
        
        Args:
            text (str): Input text
            include_spaces (bool): Whether to include spaces in count
            
        Returns:
            int: Number of characters
        """
        if include_spaces:
            return len(text)
        return len(text.replace(' ', ''))
    
    @staticmethod
    def extract_emails(text: str) -> List[str]:
        """
        Extract email addresses from text.
        
        Args:
            text (str): Input text
            
        Returns:
            List[str]: List of email addresses
        """
        email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        return re.findall(email_pattern, text)
    
    @staticmethod
    def extract_urls(text: str) -> List[str]:
        """
        Extract URLs from text.
        
        Args:
            text (str): Input text
            
        Returns:
            List[str]: List of URLs
        """
        url_pattern = r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\(\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+'
        return re.findall(url_pattern, text)
    
    @staticmethod
    def generate_hash(text: str, algorithm: str = 'sha256') -> str:
        """
        Generate hash of a string.
        
        Args:
            text (str): Input text
            algorithm (str): Hash algorithm ('md5', 'sha1', 'sha256', 'sha512')
            
        Returns:
            str: Hash string
        """
        text_bytes = text.encode('utf-8')
        
        if algorithm == 'md5':
            return hashlib.md5(text_bytes).hexdigest()
        elif algorithm == 'sha1':
            return hashlib.sha1(text_bytes).hexdigest()
        elif algorithm == 'sha256':
            return hashlib.sha256(text_bytes).hexdigest()
        elif algorithm == 'sha512':
            return hashlib.sha512(text_bytes).hexdigest()
        else:
            return hashlib.sha256(text_bytes).hexdigest()  # Default to SHA256

class FileUtils:
    """
    File handling utility functions.
    """
    
    @staticmethod
    def get_file_size(filepath: str) -> int:
        """
        Get the size of a file in bytes.
        
        Args:
            filepath (str): Path to the file
            
        Returns:
            int: File size in bytes
        """
        try:
            return os.path.getsize(filepath)
        except OSError:
            return 0
    
    @staticmethod
    def get_file_extension(filepath: str) -> str:
        """
        Get the extension of a file.
        
        Args:
            filepath (str): Path to the file
            
        Returns:
            str: File extension
        """
        return os.path.splitext(filepath)[1].lower()
    
    @staticmethod
    def list_files_in_directory(directory: str, extension: str = None, recursive: bool = False) -> List[str]:
        """
        List files in a directory.
        
        Args:
            directory (str): Directory path
            extension (str): Filter by file extension
            recursive (bool): Whether to search recursively
            
        Returns:
            List[str]: List of file paths
        """
        files = []
        
        if recursive:
            for root, _, filenames in os.walk(directory):
                for filename in filenames:
                    filepath = os.path.join(root, filename)
                    if extension is None or filepath.endswith(extension):
                        files.append(filepath)
        else:
            try:
                for item in os.listdir(directory):
                    filepath = os.path.join(directory, item)
                    if os.path.isfile(filepath):
                        if extension is None or filepath.endswith(extension):
                            files.append(filepath)
            except OSError:
                pass
        
        return files
    
    @staticmethod
    def copy_file(source: str, destination: str) -> bool:
        """
        Copy a file from source to destination.
        
        Args:
            source (str): Source file path
            destination (str): Destination file path
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            import shutil
            os.makedirs(os.path.dirname(destination), exist_ok=True)
            shutil.copy2(source, destination)
            return True
        except Exception:
            return False
    
    @staticmethod
    def delete_file(filepath: str) -> bool:
        """
        Delete a file.
        
        Args:
            filepath (str): Path to the file
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            os.remove(filepath)
            return True
        except OSError:
            return False
    
    @staticmethod
    def create_directory(directory: str) -> bool:
        """
        Create a directory (including parent directories).
        
        Args:
            directory (str): Directory path
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            os.makedirs(directory, exist_ok=True)
            return True
        except OSError:
            return False

class AlgorithmUtils:
    """
    Algorithm implementation utilities.
    """
    
    @staticmethod
    def binary_search(arr: List[int], target: int) -> int:
        """
        Perform binary search on a sorted array.
        
        Args:
            arr (List[int]): Sorted array
            target (int): Target value
            
        Returns:
            int: Index of target if found, -1 otherwise
        """
        left, right = 0, len(arr) - 1
        
        while left <= right:
            mid = (left + right) // 2
            if arr[mid] == target:
                return mid
            elif arr[mid] < target:
                left = mid + 1
            else:
                right = mid - 1
        
        return -1
    
    @staticmethod
    def quick_sort(arr: List[int]) -> List[int]:
        """
        Sort an array using quicksort algorithm.
        
        Args:
            arr (List[int]): Input array
            
        Returns:
            List[int]: Sorted array
        """
        if len(arr) <= 1:
            return arr
        
        pivot = arr[len(arr) // 2]
        left = [x for x in arr if x < pivot]
        middle = [x for x in arr if x == pivot]
        right = [x for x in arr if x > pivot]
        
        return AlgorithmUtils.quick_sort(left) + middle + AlgorithmUtils.quick_sort(right)
    
    @staticmethod
    def merge_sort(arr: List[int]) -> List[int]:
        """
        Sort an array using merge sort algorithm.
        
        Args:
            arr (List[int]): Input array
            
        Returns:
            List[int]: Sorted array
        """
        if len(arr) <= 1:
            return arr
        
        mid = len(arr) // 2
        left = AlgorithmUtils.merge_sort(arr[:mid])
        right = AlgorithmUtils.merge_sort(arr[mid:])
        
        return AlgorithmUtils._merge(left, right)
    
    @staticmethod
    def _merge(left: List[int], right: List[int]) -> List[int]:
        """
        Merge two sorted arrays.
        
        Args:
            left (List[int]): Left sorted array
            right (List[int]): Right sorted array
            
        Returns:
            List[int]: Merged sorted array
        """
        result = []
        i = j = 0
        
        while i < len(left) and j < len(right):
            if left[i] <= right[j]:
                result.append(left[i])
                i += 1
            else:
                result.append(right[j])
                j += 1
        
        result.extend(left[i:])
        result.extend(right[j:])
        return result
    
    @staticmethod
    def bubble_sort(arr: List[int]) -> List[int]:
        """
        Sort an array using bubble sort algorithm.
        
        Args:
            arr (List[int]): Input array
            
        Returns:
            List[int]: Sorted array
        """
        arr = arr.copy()
        n = len(arr)
        
        for i in range(n):
            for j in range(0, n - i - 1):
                if arr[j] > arr[j + 1]:
                    arr[j], arr[j + 1] = arr[j + 1], arr[j]
        
        return arr
    
    @staticmethod
    def linear_search(arr: List[int], target: int) -> int:
        """
        Perform linear search on an array.
        
        Args:
            arr (List[int]): Input array
            target (int): Target value
            
        Returns:
            int: Index of target if found, -1 otherwise
        """
        for i, value in enumerate(arr):
            if value == target:
                return i
        return -1

def generate_random_data(size: int, data_type: str = 'int', min_val: int = 0, max_val: int = 100) -> List[Union[int, float, str]]:
    """
    Generate random data for testing purposes.
    
    Args:
        size (int): Number of elements to generate
        data_type (str): Type of data ('int', 'float', 'string')
        min_val (int): Minimum value for numeric types
        max_val (int): Maximum value for numeric types
        
    Returns:
        List[Union[int, float, str]]: Generated random data
    """
    if data_type == 'int':
        return [random.randint(min_val, max_val) for _ in range(size)]
    elif data_type == 'float':
        return [random.uniform(min_val, max_val) for _ in range(size)]
    elif data_type == 'string':
        import string
        return [''.join(random.choices(string.ascii_letters, k=random.randint(5, 15))) for _ in range(size)]
    else:
        return [random.randint(min_val, max_val) for _ in range(size)]

def benchmark_function(func, *args, **kwargs) -> Tuple[Any, float]:
    """
    Benchmark the execution time of a function.
    
    Args:
        func: Function to benchmark
        *args: Function arguments
        **kwargs: Function keyword arguments
        
    Returns:
        Tuple[Any, float]: Function result and execution time in seconds
    """
    import time
    start_time = time.time()
    result = func(*args, **kwargs)
    end_time = time.time()
    execution_time = end_time - start_time
    return result, execution_time

def main():
    """
    Main function demonstrating the utility library.
    """
    print("Python Utility Library Demo")
    print("=" * 30)
    
    # Data processing demo
    processor = DataProcessor()
    sample_data = [
        {'name': 'Alice', 'age': 30, 'city': 'New York'},
        {'name': 'Bob', 'age': 25, 'city': 'Los Angeles'},
        {'name': 'Charlie', 'age': 35, 'city': 'New York'},
        {'name': 'Diana', 'age': 28, 'city': 'Chicago'}
    ]
    
    print("\nData Processing Demo:")
    filtered_data = processor.filter_data(sample_data, {'city': 'New York'})
    print(f"Filtered data (New York): {len(filtered_data)} items")
    
    grouped_data = processor.group_data(sample_data, 'city')
    print(f"Grouped by city: {list(grouped_data.keys())}")
    
    avg_age = processor.aggregate_data(sample_data, 'age', 'avg')
    print(f"Average age: {avg_age:.1f}")
    
    # Math utilities demo
    print("\nMath Utilities Demo:")
    print(f"Factorial of 5: {MathUtils.factorial(5)}")
    print(f"10th Fibonacci number: {MathUtils.fibonacci(10)}")
    print(f"Is 17 prime? {MathUtils.is_prime(17)}")
    print(f"GCD of 48 and 18: {MathUtils.gcd(48, 18)}")
    print(f"LCM of 12 and 15: {MathUtils.lcm(12, 15)}")
    
    primes = MathUtils.generate_primes(30)
    print(f"Primes up to 30: {primes}")
    
    # String utilities demo
    print("\nString Utilities Demo:")
    test_string = "A man a plan a canal Panama"
    print(f"Original: {test_string}")
    print(f"Reversed: {StringUtils.reverse_string(test_string)}")
    print(f"Is palindrome? {StringUtils.is_palindrome(test_string)}")
    print(f"Word count: {StringUtils.count_words(test_string)}")
    print(f"Character count: {StringUtils.count_characters(test_string)}")
    
    hash_value = StringUtils.generate_hash(test_string)
    print(f"SHA256 hash: {hash_value[:16]}...")
    
    # Algorithm utilities demo
    print("\nAlgorithm Utilities Demo:")
    test_array = [64, 34, 25, 12, 22, 11, 90]
    print(f"Original array: {test_array}")
    
    sorted_quick = AlgorithmUtils.quick_sort(test_array)
    print(f"Quick sort: {sorted_quick}")
    
    sorted_merge = AlgorithmUtils.merge_sort(test_array)
    print(f"Merge sort: {sorted_merge}")
    
    sorted_bubble = AlgorithmUtils.bubble_sort(test_array)
    print(f"Bubble sort: {sorted_bubble}")
    
    target = 25
    index = AlgorithmUtils.binary_search(sorted_quick, target)
    print(f"Binary search for {target}: index {index}")
    
    # Random data generation demo
    print("\nRandom Data Generation Demo:")
    random_ints = generate_random_data(10, 'int', 1, 100)
    print(f"Random integers: {random_ints}")
    
    random_floats = generate_random_data(5, 'float', 0, 1)
    print(f"Random floats: {[f'{x:.2f}' for x in random_floats]}")
    
    random_strings = generate_random_data(3, 'string')
    print(f"Random strings: {random_strings}")
    
    # Benchmarking demo
    print("\nBenchmarking Demo:")
    large_array = generate_random_data(1000, 'int', 1, 1000)
    
    result, time_taken = benchmark_function(AlgorithmUtils.quick_sort, large_array)
    print(f"Quick sort (1000 elements): {time_taken:.4f} seconds")
    
    result, time_taken = benchmark_function(AlgorithmUtils.merge_sort, large_array)
    print(f"Merge sort (1000 elements): {time_taken:.4f} seconds")
    
    print("\nDemo completed successfully!")

if __name__ == "__main__":
    main()