[
{
  "event_id": "acedf136-7f13-4d3e-9120-37388fd62483",
  "timestamp": "2025-06-01T15:08:30.415292",
  "session_id": "8bb54de1-c140-4424-a74c-787b42663186",
  "event_type": "llm_query",
  "llm_query": {
    "model": "DeepSeek-R1 (Paid)",
    "prompt_length": 119,
    "temperature": null,
    "max_tokens": null,
    "has_system_prompt": false,
    "tools_available": 0
  }
},

{
  "event_id": "2133593d-de5b-487f-8c30-c9c98e56c4f5",
  "timestamp": "2025-06-01T15:08:39.736780",
  "session_id": "8bb54de1-c140-4424-a74c-787b42663186",
  "event_type": "llm_response",
  "llm_response": {
    "model": "DeepSeek-R1 (Paid)",
    "response_length": 0,
    "prompt_tokens": null,
    "completion_tokens": null,
    "total_tokens": null,
    "finish_reason": null,
    "latency_ms": 9313.0
  },
  "metadata": {
    "error": "All LLM providers failed. Last error: 402 Client Error: Payment Required for url: https://openrouter.ai/api/v1/chat/completions"
  }
},

{
  "event_id": "11d39662-ed19-4d8e-880d-d6e9abe0068e",
  "timestamp": "2025-06-01T15:08:39.739278",
  "session_id": "8bb54de1-c140-4424-a74c-787b42663186",
  "event_type": "error_occurred",
  "error_details": {
    "component": "LLMInterface",
    "severity": "ERROR",
    "message": "All LLM providers failed. Last error: 402 Client Error: Payment Required for url: https://openrouter.ai/api/v1/chat/completions",
    "has_stack_trace": true
  }
},

{
  "event_id": "a03ed790-5fac-4493-8b3d-a534071b5aa9",
  "timestamp": "2025-06-01T15:08:39.739792",
  "session_id": "8bb54de1-c140-4424-a74c-787b42663186",
  "event_type": "llm_query",
  "llm_query": {
    "model": "DeepSeek-R1 (Paid)",
    "prompt_length": 151,
    "temperature": null,
    "max_tokens": null,
    "has_system_prompt": false,
    "tools_available": 0
  }
},

{
  "event_id": "0564d83a-c1ce-4ddf-a68a-6eb5608e5392",
  "timestamp": "2025-06-01T15:08:48.834493",
  "session_id": "8bb54de1-c140-4424-a74c-787b42663186",
  "event_type": "llm_response",
  "llm_response": {
    "model": "DeepSeek-R1 (Paid)",
    "response_length": 0,
    "prompt_tokens": null,
    "completion_tokens": null,
    "total_tokens": null,
    "finish_reason": null,
    "latency_ms": 9094.0
  },
  "metadata": {
    "error": "All LLM providers failed. Last error: 402 Client Error: Payment Required for url: https://openrouter.ai/api/v1/chat/completions"
  }
},

{
  "event_id": "b7ec23f9-c1c1-459f-ad85-18e2392f7d7d",
  "timestamp": "2025-06-01T15:08:48.835496",
  "session_id": "8bb54de1-c140-4424-a74c-787b42663186",
  "event_type": "error_occurred",
  "error_details": {
    "component": "LLMInterface",
    "severity": "ERROR",
    "message": "All LLM providers failed. Last error: 402 Client Error: Payment Required for url: https://openrouter.ai/api/v1/chat/completions",
    "has_stack_trace": true
  }
},

{
  "event_id": "e2c2f898-3333-4300-b21a-7b85e1eaa7c7",
  "timestamp": "2025-06-01T15:08:48.836492",
  "session_id": "8bb54de1-c140-4424-a74c-787b42663186",
  "event_type": "llm_query",
  "llm_query": {
    "model": "DeepSeek-R1 (Paid)",
    "prompt_length": 184,
    "temperature": null,
    "max_tokens": null,
    "has_system_prompt": false,
    "tools_available": 0
  }
},

{
  "event_id": "ac18e6f7-28ed-43d2-8e90-4fc54f1de969",
  "timestamp": "2025-06-01T15:08:57.844010",
  "session_id": "8bb54de1-c140-4424-a74c-787b42663186",
  "event_type": "llm_response",
  "llm_response": {
    "model": "DeepSeek-R1 (Paid)",
    "response_length": 0,
    "prompt_tokens": null,
    "completion_tokens": null,
    "total_tokens": null,
    "finish_reason": null,
    "latency_ms": 9015.0
  },
  "metadata": {
    "error": "All LLM providers failed. Last error: 402 Client Error: Payment Required for url: https://openrouter.ai/api/v1/chat/completions"
  }
},

{
  "event_id": "d6fb0eaf-e967-442a-8c93-a7293aa156c8",
  "timestamp": "2025-06-01T15:08:57.845116",
  "session_id": "8bb54de1-c140-4424-a74c-787b42663186",
  "event_type": "error_occurred",
  "error_details": {
    "component": "LLMInterface",
    "severity": "ERROR",
    "message": "All LLM providers failed. Last error: 402 Client Error: Payment Required for url: https://openrouter.ai/api/v1/chat/completions",
    "has_stack_trace": true
  }
},

{
  "event_id": "111e0d59-b34e-4054-a05b-c89c96fb3da7",
  "timestamp": "2025-06-01T15:08:57.845646",
  "session_id": "8bb54de1-c140-4424-a74c-787b42663186",
  "event_type": "llm_query",
  "llm_query": {
    "model": "DeepSeek-R1 (Paid)",
    "prompt_length": 217,
    "temperature": null,
    "max_tokens": null,
    "has_system_prompt": false,
    "tools_available": 0
  }
},

{
  "event_id": "159791c4-ee06-43e1-ad99-433c850e77fd",
  "timestamp": "2025-06-01T15:09:06.666396",
  "session_id": "8bb54de1-c140-4424-a74c-787b42663186",
  "event_type": "llm_response",
  "llm_response": {
    "model": "DeepSeek-R1 (Paid)",
    "response_length": 0,
    "prompt_tokens": null,
    "completion_tokens": null,
    "total_tokens": null,
    "finish_reason": null,
    "latency_ms": 8813.0
  },
  "metadata": {
    "error": "All LLM providers failed. Last error: 402 Client Error: Payment Required for url: https://openrouter.ai/api/v1/chat/completions"
  }
},

{
  "event_id": "06f158fd-f6e0-4628-8006-9e37926321ef",
  "timestamp": "2025-06-01T15:09:06.666930",
  "session_id": "8bb54de1-c140-4424-a74c-787b42663186",
  "event_type": "error_occurred",
  "error_details": {
    "component": "LLMInterface",
    "severity": "ERROR",
    "message": "All LLM providers failed. Last error: 402 Client Error: Payment Required for url: https://openrouter.ai/api/v1/chat/completions",
    "has_stack_trace": true
  }
},

{
  "event_id": "f98ba85e-e27d-40f2-ae62-87e6ce411a3f",
  "timestamp": "2025-06-01T15:09:06.667981",
  "session_id": "8bb54de1-c140-4424-a74c-787b42663186",
  "event_type": "llm_query",
  "llm_query": {
    "model": "DeepSeek-R1 (Paid)",
    "prompt_length": 250,
    "temperature": null,
    "max_tokens": null,
    "has_system_prompt": false,
    "tools_available": 0
  }
},

{
  "event_id": "b0a45fae-181b-4523-9451-6a3d2f5adefd",
  "timestamp": "2025-06-01T15:09:15.507751",
  "session_id": "8bb54de1-c140-4424-a74c-787b42663186",
  "event_type": "llm_response",
  "llm_response": {
    "model": "DeepSeek-R1 (Paid)",
    "response_length": 0,
    "prompt_tokens": null,
    "completion_tokens": null,
    "total_tokens": null,
    "finish_reason": null,
    "latency_ms": 8828.0
  },
  "metadata": {
    "error": "All LLM providers failed. Last error: 402 Client Error: Payment Required for url: https://openrouter.ai/api/v1/chat/completions"
  }
},

{
  "event_id": "11383572-ebea-4d77-8465-df522870ce89",
  "timestamp": "2025-06-01T15:09:15.508263",
  "session_id": "8bb54de1-c140-4424-a74c-787b42663186",
  "event_type": "error_occurred",
  "error_details": {
    "component": "LLMInterface",
    "severity": "ERROR",
    "message": "All LLM providers failed. Last error: 402 Client Error: Payment Required for url: https://openrouter.ai/api/v1/chat/completions",
    "has_stack_trace": true
  }
},

{
  "event_id": "0a3f30bb-5021-4328-a9b2-7f7f9624a15f",
  "timestamp": "2025-06-01T15:09:15.508263",
  "session_id": "8bb54de1-c140-4424-a74c-787b42663186",
  "event_type": "llm_query",
  "llm_query": {
    "model": "DeepSeek-R1 (Paid)",
    "prompt_length": 283,
    "temperature": null,
    "max_tokens": null,
    "has_system_prompt": false,
    "tools_available": 0
  }
},

{
  "event_id": "7ddba23a-5a7d-4f67-890f-9d20ebf9db69",
  "timestamp": "2025-06-01T15:09:24.383863",
  "session_id": "8bb54de1-c140-4424-a74c-787b42663186",
  "event_type": "llm_response",
  "llm_response": {
    "model": "DeepSeek-R1 (Paid)",
    "response_length": 0,
    "prompt_tokens": null,
    "completion_tokens": null,
    "total_tokens": null,
    "finish_reason": null,
    "latency_ms": 8875.0
  },
  "metadata": {
    "error": "All LLM providers failed. Last error: 402 Client Error: Payment Required for url: https://openrouter.ai/api/v1/chat/completions"
  }
},

{
  "event_id": "c7a32134-5c5d-4768-bf6a-6a2e0f1bd065",
  "timestamp": "2025-06-01T15:09:24.384387",
  "session_id": "8bb54de1-c140-4424-a74c-787b42663186",
  "event_type": "error_occurred",
  "error_details": {
    "component": "LLMInterface",
    "severity": "ERROR",
    "message": "All LLM providers failed. Last error: 402 Client Error: Payment Required for url: https://openrouter.ai/api/v1/chat/completions",
    "has_stack_trace": true
  }
},

{
  "event_id": "58b287d7-56cc-480a-b460-c2f508b737df",
  "timestamp": "2025-06-01T15:09:24.429185",
  "session_id": "8bb54de1-c140-4424-a74c-787b42663186",
  "event_type": "llm_query",
  "llm_query": {
    "model": "DeepSeek-R1 (Paid)",
    "prompt_length": 316,
    "temperature": null,
    "max_tokens": null,
    "has_system_prompt": false,
    "tools_available": 0
  }
},

{
  "event_id": "b76d2ee0-adaa-44e3-b4be-dd76e3e4c3b9",
  "timestamp": "2025-06-01T15:09:33.444673",
  "session_id": "8bb54de1-c140-4424-a74c-787b42663186",
  "event_type": "llm_response",
  "llm_response": {
    "model": "DeepSeek-R1 (Paid)",
    "response_length": 0,
    "prompt_tokens": null,
    "completion_tokens": null,
    "total_tokens": null,
    "finish_reason": null,
    "latency_ms": 9016.0
  },
  "metadata": {
    "error": "All LLM providers failed. Last error: 402 Client Error: Payment Required for url: https://openrouter.ai/api/v1/chat/completions"
  }
},

{
  "event_id": "30da02f1-39d6-4257-90d1-c39c29fe22d5",
  "timestamp": "2025-06-01T15:09:33.445197",
  "session_id": "8bb54de1-c140-4424-a74c-787b42663186",
  "event_type": "error_occurred",
  "error_details": {
    "component": "LLMInterface",
    "severity": "ERROR",
    "message": "All LLM providers failed. Last error: 402 Client Error: Payment Required for url: https://openrouter.ai/api/v1/chat/completions",
    "has_stack_trace": true
  }
},

{
  "event_id": "33ed159c-c480-4241-a1c6-28050808e8d6",
  "timestamp": "2025-06-01T15:09:33.445720",
  "session_id": "8bb54de1-c140-4424-a74c-787b42663186",
  "event_type": "llm_query",
  "llm_query": {
    "model": "DeepSeek-R1 (Paid)",
    "prompt_length": 349,
    "temperature": null,
    "max_tokens": null,
    "has_system_prompt": false,
    "tools_available": 0
  }
},

{
  "event_id": "0767e9da-ad10-411d-b4da-ff47d04d7479",
  "timestamp": "2025-06-01T15:09:42.535206",
  "session_id": "8bb54de1-c140-4424-a74c-787b42663186",
  "event_type": "llm_response",
  "llm_response": {
    "model": "DeepSeek-R1 (Paid)",
    "response_length": 0,
    "prompt_tokens": null,
    "completion_tokens": null,
    "total_tokens": null,
    "finish_reason": null,
    "latency_ms": 9094.0
  },
  "metadata": {
    "error": "All LLM providers failed. Last error: 402 Client Error: Payment Required for url: https://openrouter.ai/api/v1/chat/completions"
  }
},

{
  "event_id": "167945d0-12ac-4734-9eaa-54c40d7853f4",
  "timestamp": "2025-06-01T15:09:42.535716",
  "session_id": "8bb54de1-c140-4424-a74c-787b42663186",
  "event_type": "error_occurred",
  "error_details": {
    "component": "LLMInterface",
    "severity": "ERROR",
    "message": "All LLM providers failed. Last error: 402 Client Error: Payment Required for url: https://openrouter.ai/api/v1/chat/completions",
    "has_stack_trace": true
  }
},

{
  "event_id": "68d09ff6-e0af-4e1e-b10e-367245aec42c",
  "timestamp": "2025-06-01T15:09:42.536222",
  "session_id": "8bb54de1-c140-4424-a74c-787b42663186",
  "event_type": "llm_query",
  "llm_query": {
    "model": "DeepSeek-R1 (Paid)",
    "prompt_length": 382,
    "temperature": null,
    "max_tokens": null,
    "has_system_prompt": false,
    "tools_available": 0
  }
},

{
  "event_id": "b5e28944-01e7-4237-9a5c-ccdd85edd37a",
  "timestamp": "2025-06-01T15:09:56.035930",
  "session_id": "8bb54de1-c140-4424-a74c-787b42663186",
  "event_type": "llm_response",
  "llm_response": {
    "model": "DeepSeek-R1 (Paid)",
    "response_length": 0,
    "prompt_tokens": null,
    "completion_tokens": null,
    "total_tokens": null,
    "finish_reason": null,
    "latency_ms": 13500.0
  },
  "metadata": {
    "error": "All LLM providers failed. Last error: 402 Client Error: Payment Required for url: https://openrouter.ai/api/v1/chat/completions"
  }
},

{
  "event_id": "29aa5dd9-f461-442f-a192-1e93daec9251",
  "timestamp": "2025-06-01T15:09:56.036451",
  "session_id": "8bb54de1-c140-4424-a74c-787b42663186",
  "event_type": "error_occurred",
  "error_details": {
    "component": "LLMInterface",
    "severity": "ERROR",
    "message": "All LLM providers failed. Last error: 402 Client Error: Payment Required for url: https://openrouter.ai/api/v1/chat/completions",
    "has_stack_trace": true
  }
},

{
  "event_id": "51461bd5-a381-4d15-9eff-8667377a8319",
  "timestamp": "2025-06-01T15:09:56.037283",
  "session_id": "8bb54de1-c140-4424-a74c-787b42663186",
  "event_type": "llm_query",
  "llm_query": {
    "model": "DeepSeek-R1 (Paid)",
    "prompt_length": 415,
    "temperature": null,
    "max_tokens": null,
    "has_system_prompt": false,
    "tools_available": 0
  }
},

{
  "event_id": "95055dd5-2923-4f5b-9d6b-d299db94d3de",
  "timestamp": "2025-06-01T15:10:05.949813",
  "session_id": "8bb54de1-c140-4424-a74c-787b42663186",
  "event_type": "llm_response",
  "llm_response": {
    "model": "DeepSeek-R1 (Paid)",
    "response_length": 0,
    "prompt_tokens": null,
    "completion_tokens": null,
    "total_tokens": null,
    "finish_reason": null,
    "latency_ms": 9906.0
  },
  "metadata": {
    "error": "All LLM providers failed. Last error: 402 Client Error: Payment Required for url: https://openrouter.ai/api/v1/chat/completions"
  }
},

{
  "event_id": "07b5578d-8c45-4842-9984-1203cd1d68bc",
  "timestamp": "2025-06-01T15:10:05.949891",
  "session_id": "8bb54de1-c140-4424-a74c-787b42663186",
  "event_type": "error_occurred",
  "error_details": {
    "component": "LLMInterface",
    "severity": "ERROR",
    "message": "All LLM providers failed. Last error: 402 Client Error: Payment Required for url: https://openrouter.ai/api/v1/chat/completions",
    "has_stack_trace": true
  }
},

{
  "event_id": "379cd5a1-f110-49f8-a594-948be1b262e1",
  "timestamp": "2025-06-01T15:10:05.950411",
  "session_id": "8bb54de1-c140-4424-a74c-787b42663186",
  "event_type": "llm_query",
  "llm_query": {
    "model": "DeepSeek-R1 (Paid)",
    "prompt_length": 448,
    "temperature": null,
    "max_tokens": null,
    "has_system_prompt": false,
    "tools_available": 0
  }
},

{
  "event_id": "62252f0d-422b-434c-b9a2-2e9e93f2cbd0",
  "timestamp": "2025-06-01T15:10:15.443382",
  "session_id": "8bb54de1-c140-4424-a74c-787b42663186",
  "event_type": "llm_response",
  "llm_response": {
    "model": "DeepSeek-R1 (Paid)",
    "response_length": 0,
    "prompt_tokens": null,
    "completion_tokens": null,
    "total_tokens": null,
    "finish_reason": null,
    "latency_ms": 9484.0
  },
  "metadata": {
    "error": "All LLM providers failed. Last error: 402 Client Error: Payment Required for url: https://openrouter.ai/api/v1/chat/completions"
  }
},

{
  "event_id": "12fd7c60-9bdd-4e46-85bd-94d30ee294c3",
  "timestamp": "2025-06-01T15:10:15.443414",
  "session_id": "8bb54de1-c140-4424-a74c-787b42663186",
  "event_type": "error_occurred",
  "error_details": {
    "component": "LLMInterface",
    "severity": "ERROR",
    "message": "All LLM providers failed. Last error: 402 Client Error: Payment Required for url: https://openrouter.ai/api/v1/chat/completions",
    "has_stack_trace": true
  }
},

{
  "event_id": "57216007-64ea-4fa6-a838-fcab16269d53",
  "timestamp": "2025-06-01T15:10:15.444462",
  "session_id": "8bb54de1-c140-4424-a74c-787b42663186",
  "event_type": "llm_query",
  "llm_query": {
    "model": "DeepSeek-R1 (Paid)",
    "prompt_length": 481,
    "temperature": null,
    "max_tokens": null,
    "has_system_prompt": false,
    "tools_available": 0
  }
},

{
  "event_id": "481abf69-96fb-4100-8396-172022c730c5",
  "timestamp": "2025-06-01T15:10:24.647280",
  "session_id": "8bb54de1-c140-4424-a74c-787b42663186",
  "event_type": "llm_response",
  "llm_response": {
    "model": "DeepSeek-R1 (Paid)",
    "response_length": 0,
    "prompt_tokens": null,
    "completion_tokens": null,
    "total_tokens": null,
    "finish_reason": null,
    "latency_ms": 9203.0
  },
  "metadata": {
    "error": "All LLM providers failed. Last error: 402 Client Error: Payment Required for url: https://openrouter.ai/api/v1/chat/completions"
  }
},

{
  "event_id": "cbe42a04-4fe2-401c-bccd-6ba345e50ca9",
  "timestamp": "2025-06-01T15:10:24.647280",
  "session_id": "8bb54de1-c140-4424-a74c-787b42663186",
  "event_type": "error_occurred",
  "error_details": {
    "component": "LLMInterface",
    "severity": "ERROR",
    "message": "All LLM providers failed. Last error: 402 Client Error: Payment Required for url: https://openrouter.ai/api/v1/chat/completions",
    "has_stack_trace": true
  }
},

{
  "event_id": "5fbad7b8-0a82-4c89-bb20-dcaac72bdd86",
  "timestamp": "2025-06-01T15:10:24.647797",
  "session_id": "8bb54de1-c140-4424-a74c-787b42663186",
  "event_type": "llm_query",
  "llm_query": {
    "model": "DeepSeek-R1 (Paid)",
    "prompt_length": 514,
    "temperature": null,
    "max_tokens": null,
    "has_system_prompt": false,
    "tools_available": 0
  }
},

{
  "event_id": "1d5c303e-1784-4acb-8b63-480765ae1c3b",
  "timestamp": "2025-06-01T15:10:33.813455",
  "session_id": "8bb54de1-c140-4424-a74c-787b42663186",
  "event_type": "llm_response",
  "llm_response": {
    "model": "DeepSeek-R1 (Paid)",
    "response_length": 0,
    "prompt_tokens": null,
    "completion_tokens": null,
    "total_tokens": null,
    "finish_reason": null,
    "latency_ms": 9172.0
  },
  "metadata": {
    "error": "All LLM providers failed. Last error: 402 Client Error: Payment Required for url: https://openrouter.ai/api/v1/chat/completions"
  }
},

{
  "event_id": "7e75497d-841d-4ff3-a6b3-a47ceffcfe58",
  "timestamp": "2025-06-01T15:10:33.813455",
  "session_id": "8bb54de1-c140-4424-a74c-787b42663186",
  "event_type": "error_occurred",
  "error_details": {
    "component": "LLMInterface",
    "severity": "ERROR",
    "message": "All LLM providers failed. Last error: 402 Client Error: Payment Required for url: https://openrouter.ai/api/v1/chat/completions",
    "has_stack_trace": true
  }
},

{
  "event_id": "ee10048b-42ae-47d0-8c08-c53853c3aa8b",
  "timestamp": "2025-06-01T15:10:33.820755",
  "session_id": "8bb54de1-c140-4424-a74c-787b42663186",
  "event_type": "llm_query",
  "llm_query": {
    "model": "DeepSeek-R1 (Paid)",
    "prompt_length": 547,
    "temperature": null,
    "max_tokens": null,
    "has_system_prompt": false,
    "tools_available": 0
  }
},

{
  "event_id": "33dcee29-77d0-4f5c-a975-bbbe07e6871d",
  "timestamp": "2025-06-01T15:10:42.914500",
  "session_id": "8bb54de1-c140-4424-a74c-787b42663186",
  "event_type": "llm_response",
  "llm_response": {
    "model": "DeepSeek-R1 (Paid)",
    "response_length": 0,
    "prompt_tokens": null,
    "completion_tokens": null,
    "total_tokens": null,
    "finish_reason": null,
    "latency_ms": 9094.0
  },
  "metadata": {
    "error": "All LLM providers failed. Last error: 402 Client Error: Payment Required for url: https://openrouter.ai/api/v1/chat/completions"
  }
},

{
  "event_id": "56733f3f-0aa4-40ed-9de2-14989d990c15",
  "timestamp": "2025-06-01T15:10:42.915020",
  "session_id": "8bb54de1-c140-4424-a74c-787b42663186",
  "event_type": "error_occurred",
  "error_details": {
    "component": "LLMInterface",
    "severity": "ERROR",
    "message": "All LLM providers failed. Last error: 402 Client Error: Payment Required for url: https://openrouter.ai/api/v1/chat/completions",
    "has_stack_trace": true
  }
},

{
  "event_id": "abaf71d0-e2ae-4a12-9758-9a6ff034c443",
  "timestamp": "2025-06-01T15:10:42.915532",
  "session_id": "8bb54de1-c140-4424-a74c-787b42663186",
  "event_type": "llm_query",
  "llm_query": {
    "model": "DeepSeek-R1 (Paid)",
    "prompt_length": 580,
    "temperature": null,
    "max_tokens": null,
    "has_system_prompt": false,
    "tools_available": 0
  }
},

{
  "event_id": "9d5ffc58-eb42-42a1-adc6-e977351ee8c0",
  "timestamp": "2025-06-01T15:10:52.968317",
  "session_id": "8bb54de1-c140-4424-a74c-787b42663186",
  "event_type": "llm_response",
  "llm_response": {
    "model": "DeepSeek-R1 (Paid)",
    "response_length": 0,
    "prompt_tokens": null,
    "completion_tokens": null,
    "total_tokens": null,
    "finish_reason": null,
    "latency_ms": 10062.0
  },
  "metadata": {
    "error": "All LLM providers failed. Last error: 402 Client Error: Payment Required for url: https://openrouter.ai/api/v1/chat/completions"
  }
},

{
  "event_id": "66e700e3-06e4-4888-b5ef-f02d70faa40a",
  "timestamp": "2025-06-01T15:10:52.968821",
  "session_id": "8bb54de1-c140-4424-a74c-787b42663186",
  "event_type": "error_occurred",
  "error_details": {
    "component": "LLMInterface",
    "severity": "ERROR",
    "message": "All LLM providers failed. Last error: 402 Client Error: Payment Required for url: https://openrouter.ai/api/v1/chat/completions",
    "has_stack_trace": true
  }
}