"""
Simplified Ultimate Verification Test for MindLink Agent Core.

This test focuses on the most critical aspects of dynamic module loading
and unloading to ensure the circular import issue is completely resolved.
"""

import pytest
import os
import sys
import tempfile
import shutil
import importlib
import random
from types import ModuleType


def test_dynamic_module_manipulation():
    """
    Test dynamic loading, unloading, and reloading of modules in various orders.
    
    This test verifies that the circular import issue is completely resolved
    by manipulating modules at runtime in ways that would trigger circular
    import errors if they existed.
    """
    print("\n=== Testing Dynamic Module Manipulation ===")
    
    # Track loaded modules before test
    initial_modules = set(sys.modules.keys())
    
    # List of module paths to manipulate
    module_paths = [
        'mindlink.tools.file_tools',
        'mindlink.tools.base',
        'mindlink.tools.graph_tools',
        'mindlink.tools.knowledge_tools',
        'mindlink.tools.doc_tools',
        'mindlink.tools',
    ]
    
    # Remove modules if they're already loaded
    for module_path in module_paths:
        if module_path in sys.modules:
            del sys.modules[module_path]
    
    # Function to import a module and return it
    def import_module(module_path):
        try:
            return importlib.import_module(module_path)
        except ImportError as e:
            return f"ImportError: {str(e)}"
    
    # Test 1: Sequential loading in different orders
    print("Test 1: Sequential loading in different orders")
    for _ in range(5):  # Try multiple random orders
        # Shuffle the module paths
        random_order = module_paths.copy()
        random.shuffle(random_order)
        
        # Clear modules
        for module_path in module_paths:
            if module_path in sys.modules:
                del sys.modules[module_path]
        
        # Import in random order
        results = {}
        for module_path in random_order:
            results[module_path] = import_module(module_path)
        
        # Verify all imports succeeded
        for module_path, result in results.items():
            assert not isinstance(result, str), f"Failed to import {module_path}: {result}"
            assert isinstance(result, ModuleType), f"Expected module, got {type(result)}"
    
    # Test 2: Reload modules multiple times
    print("Test 2: Reload modules multiple times")
    modules = {}
    for module_path in module_paths:
        modules[module_path] = import_module(module_path)
    
    # Reload each module multiple times
    for _ in range(3):
        for module_path in module_paths:
            modules[module_path] = importlib.reload(modules[module_path])
            assert isinstance(modules[module_path], ModuleType)
    
    # Test 3: Circular loading pattern
    print("Test 3: Circular loading pattern")
    # Clear modules
    for module_path in module_paths:
        if module_path in sys.modules:
            del sys.modules[module_path]
    
    # Import in a pattern that would trigger circular imports if they existed
    base = import_module('mindlink.tools.base')
    assert isinstance(base, ModuleType)
    
    file_tools = import_module('mindlink.tools.file_tools')
    assert isinstance(file_tools, ModuleType)
    
    # Import the package that imports both
    tools = import_module('mindlink.tools')
    assert isinstance(tools, ModuleType)
    
    # Reload base (which might trigger circular import with file_tools)
    base = importlib.reload(base)
    assert isinstance(base, ModuleType)
    
    # Reload file_tools (which might trigger circular import with base)
    file_tools = importlib.reload(file_tools)
    assert isinstance(file_tools, ModuleType)
    
    # Test 4: Import specific classes directly
    print("Test 4: Import specific classes directly")
    # Clear modules
    for module_path in module_paths:
        if module_path in sys.modules:
            del sys.modules[module_path]
    
    # Import specific classes that might be involved in circular imports
    from mindlink.tools.base import Tool, tool_registry
    from mindlink.tools.file_tools import TransactionManager, create_file, read_file
    from mindlink.tools.file_tools import CreateFileTool, ReadFileTool
    from mindlink.tools.doc_tools import HoverDocTool as DocHoverDocTool
    from mindlink.tools.knowledge_tools import HoverDocTool as KnowledgeHoverDocTool
    
    # Verify imports succeeded
    assert Tool is not None
    assert tool_registry is not None
    assert TransactionManager is not None
    assert create_file is not None
    assert read_file is not None
    assert CreateFileTool is not None
    assert ReadFileTool is not None
    assert DocHoverDocTool is not None
    assert KnowledgeHoverDocTool is not None
    
    # Test 5: Import everything from mindlink.tools
    print("Test 5: Import everything from mindlink.tools")
    # Clear modules
    for module_path in module_paths:
        if module_path in sys.modules:
            del sys.modules[module_path]
    
    # Import all tools
    from mindlink.tools import (
        CreateFileTool,
        ReadFileTool,
        ListFilesTool,
        RunShellCommandTool,
        InsertASTNodeTool,
        SemanticSuggestTool,
        RunCodeTool,
        SnapshotTool,
        GenerateGraphTool,
        HoverDocTool,
        GenerateCallGraphTool
    )
    
    # Verify imports succeeded
    assert CreateFileTool is not None
    assert ReadFileTool is not None
    assert ListFilesTool is not None
    assert RunShellCommandTool is not None
    assert InsertASTNodeTool is not None
    assert SemanticSuggestTool is not None
    assert RunCodeTool is not None
    assert SnapshotTool is not None
    assert GenerateGraphTool is not None
    assert HoverDocTool is not None
    assert GenerateCallGraphTool is not None
    
    print("All dynamic module manipulation tests passed!")
    
    # Clean up - restore initial module state
    current_modules = set(sys.modules.keys())
    new_modules = current_modules - initial_modules
    for module_name in new_modules:
        if module_name.startswith('mindlink.'):
            if module_name in sys.modules:
                del sys.modules[module_name]


def test_file_operations_with_transactions():
    """
    Test file operations with transactions.
    
    This test verifies that the file operations and transaction manager
    work correctly together.
    """
    print("\n=== Testing File Operations with Transactions ===")
    
    # Create a temporary directory
    temp_dir = tempfile.mkdtemp()
    try:
        # Import necessary components
        from mindlink.tools.file_tools import (
            TransactionManager,
            create_file,
            read_file,
            path_exists
        )
        
        # Test basic file operations with transactions
        with TransactionManager():
            # Create a file
            file_path = os.path.join(temp_dir, "test.txt")
            content = "Test content"
            create_file(file_path, content)
            
            # Verify the file was created
            assert path_exists(file_path)
            
            # Read the file
            read_content = read_file(file_path)
            assert read_content == content
        
        # Test transaction rollback
        try:
            with TransactionManager():
                # Create a file
                file_path = os.path.join(temp_dir, "rollback.txt")
                content = "This file should be rolled back"
                create_file(file_path, content)
                
                # Verify the file was created
                assert path_exists(file_path)
                
                # Raise an exception to trigger rollback
                raise ValueError("Test exception")
        except ValueError:
            # Expected exception
            pass
        
        # Verify the file was rolled back
        assert not path_exists(file_path)
        
        print("File operations with transactions tests passed!")
    
    finally:
        # Clean up
        shutil.rmtree(temp_dir)


def test_tool_classes():
    """
    Test the tool classes.
    
    This test verifies that the tool classes work correctly.
    """
    print("\n=== Testing Tool Classes ===")
    
    # Create a temporary directory
    temp_dir = tempfile.mkdtemp()
    try:
        # Import necessary components
        from mindlink.tools.file_tools import (
            CreateFileTool,
            ReadFileTool,
            ListFilesTool
        )
        
        # Create a file using CreateFileTool
        create_tool = CreateFileTool()
        file_path = os.path.join(temp_dir, "tool_test.txt")
        result = create_tool.execute(path=file_path, content="Created by tool")
        assert result["status"] == "success"
        
        # Read the file using ReadFileTool
        read_tool = ReadFileTool()
        result = read_tool.execute(path=file_path)
        assert result["status"] == "success"
        assert result["observation"] == "Created by tool"
        
        # List files using ListFilesTool
        list_tool = ListFilesTool()
        result = list_tool.execute(directory=temp_dir)
        assert result["status"] == "success"
        assert "tool_test.txt" in result["observation"]
        
        print("Tool classes tests passed!")
    
    finally:
        # Clean up
        shutil.rmtree(temp_dir)


if __name__ == "__main__":
    # Run all tests
    test_dynamic_module_manipulation()
    test_file_operations_with_transactions()
    test_tool_classes()
    print("\n=== All Ultimate Simplified Verification Tests Passed! ===")
    print("The library is 100% ready for all new tests and commands.")
