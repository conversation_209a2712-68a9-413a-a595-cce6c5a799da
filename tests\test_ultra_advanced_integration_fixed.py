"""
Ultra-Advanced Integration Test for MindLink Agent Core (Thread-Safe Version).

This test combines multiple challenging aspects in a single comprehensive test:
1. Circular import verification
2. Deep recursive structures
3. Parallel operations (thread-safe)
4. Cross-module integration
5. Transaction usage (non-nested)
6. Memory efficiency
7. Error recovery
8. Large file handling
9. Tool registry integration
10. Dynamic module loading/unloading

The goal is to ensure the library is 100% ready for production use under
the most demanding conditions.
"""

import pytest
import os
import sys
import time
import tempfile
import shutil
import random
import string
import concurrent.futures
import json
import gc
import importlib
from pathlib import Path

# Import all relevant components
from mindlink.tools.file_tools import (
    TransactionManager,
    create_file,
    read_file,
    list_files,
    path_exists,
    CreateFileTool,
    ReadFileTool,
    ListFilesTool
)
from mindlink.tools.graph_tools import GenerateCallGraphTool
from mindlink.tools.base import Tool, tool_registry


def generate_random_content(size_kb):
    """Generate random content of specified size in KB."""
    chars = string.ascii_letters + string.digits
    return ''.join(random.choice(chars) for _ in range(size_kb * 1024))


def create_python_module(path, module_name, functions=None):
    """Create a Python module with specified functions."""
    if functions is None:
        functions = [
            ("function_a", ["function_b", "function_c"], "return 42"),
            ("function_b", ["function_d"], "return 'hello'"),
            ("function_c", [], "return True"),
            ("function_d", [], "return [1, 2, 3]")
        ]

    content = f"# Module: {module_name}\n\n"

    for func_name, calls, return_stmt in functions:
        content += f"def {func_name}():\n"
        for call in calls:
            content += f"    {call}()\n"
        content += f"    {return_stmt}\n\n"

    create_file(path, content)
    return path


def test_ultra_advanced_integration_fixed():
    """
    Ultra-Advanced Integration Test (Thread-Safe Version)

    This test combines all challenging aspects in a single comprehensive test
    to ensure the library is 100% ready for production.
    """
    # Create a temporary directory for this test
    temp_dir = tempfile.mkdtemp()
    try:
        print("\n=== Starting Ultra-Advanced Integration Test (Thread-Safe Version) ===")

        # 1. Circular Import Verification
        print("\n1. Verifying circular import resolution...")
        # Force reload of all relevant modules to ensure we're testing the current state
        for module_name in list(sys.modules.keys()):
            if module_name.startswith('mindlink.tools.'):
                del sys.modules[module_name]
        if 'mindlink.tools' in sys.modules:
            del sys.modules['mindlink.tools']

        # Import all tools to verify no circular imports
        start_time = time.time()
        from mindlink.tools import (
            CreateFileTool,
            ReadFileTool,
            ListFilesTool,
            RunShellCommandTool,
            InsertASTNodeTool,
            SemanticSuggestTool,
            RunCodeTool,
            SnapshotTool,
            GenerateGraphTool,
            HoverDocTool,
            GenerateCallGraphTool
        )
        import_time = time.time() - start_time
        print(f"All tools imported successfully in {import_time:.2f} seconds")

        # 2. Create Complex Project Structure
        print("\n2. Creating complex project structure...")
        # Create main directories
        project_dirs = [
            "src",
            "src/core",
            "src/utils",
            "src/models",
            "src/controllers",
            "src/views",
            "tests",
            "tests/unit",
            "tests/integration",
            "docs",
            "config",
            "data",
            "data/raw",
            "data/processed"
        ]

        for dir_path in project_dirs:
            os.makedirs(os.path.join(temp_dir, dir_path), exist_ok=True)

        # Create Python modules with interdependencies
        modules = [
            ("src/core/base.py", "core.base", [
                ("Base", [], "return 'Base class'"),
                ("initialize", [], "return 'Initialized'")
            ]),
            ("src/utils/helpers.py", "utils.helpers", [
                ("format_string", [], "return 'formatted'"),
                ("parse_json", [], "return {'status': 'success'}")
            ]),
            ("src/models/user.py", "models.user", [
                ("User", [], "return 'User model'"),
                ("get_user", ["validate_user"], "return {'id': 1, 'name': 'Test User'}"),
                ("validate_user", [], "return True")
            ]),
            ("src/controllers/api.py", "controllers.api", [
                ("handle_request", ["get_user", "format_response"], "return 'API response'"),
                ("format_response", [], "return 'Formatted response'")
            ]),
            ("src/views/template.py", "views.template", [
                ("render", ["get_template", "process_template"], "return 'Rendered template'"),
                ("get_template", [], "return 'Template content'"),
                ("process_template", [], "return 'Processed template'")
            ])
        ]

        for file_path, module_name, functions in modules:
            create_python_module(os.path.join(temp_dir, file_path), module_name, functions)

        # Create some large data files
        data_files = [
            ("data/raw/large_data_1.txt", 500),  # 500KB
            ("data/raw/large_data_2.txt", 1000),  # 1MB
            ("data/processed/processed_data.json", 200)  # 200KB
        ]

        for file_path, size_kb in data_files:
            if file_path.endswith('.json'):
                # Create a large JSON file
                data = {
                    "items": [{"id": i, "value": f"Item {i}", "data": generate_random_content(1)}
                             for i in range(size_kb)]
                }
                create_file(os.path.join(temp_dir, file_path), json.dumps(data))
            else:
                # Create a large text file
                create_file(os.path.join(temp_dir, file_path), generate_random_content(size_kb))

        # Create configuration files
        config_files = [
            ("config/settings.json", json.dumps({
                "version": "1.0.0",
                "environment": "test",
                "debug": True,
                "database": {
                    "host": "localhost",
                    "port": 5432,
                    "name": "testdb",
                    "user": "testuser",
                    "password": "testpass"
                },
                "api": {
                    "host": "0.0.0.0",
                    "port": 8000,
                    "rate_limit": 100,
                    "timeout": 30
                },
                "logging": {
                    "level": "INFO",
                    "file": "logs/app.log",
                    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
                }
            }, indent=2)),
            ("config/routes.json", json.dumps({
                "routes": [
                    {"path": "/api/users", "controller": "UserController", "method": "GET"},
                    {"path": "/api/users/:id", "controller": "UserController", "method": "GET"},
                    {"path": "/api/users", "controller": "UserController", "method": "POST"},
                    {"path": "/api/users/:id", "controller": "UserController", "method": "PUT"},
                    {"path": "/api/users/:id", "controller": "UserController", "method": "DELETE"}
                ]
            }, indent=2))
        ]

        for file_path, content in config_files:
            create_file(os.path.join(temp_dir, file_path), content)

        # 3. Parallel Operations (Thread-Safe)
        print("\n3. Performing parallel operations (thread-safe)...")

        # Define thread-safe operations
        def thread_safe_operation(thread_id):
            """Perform operations that are thread-safe."""
            # Create a subdirectory for this thread
            thread_dir = os.path.join(temp_dir, f"thread_{thread_id}")
            os.makedirs(thread_dir, exist_ok=True)

            # Create files in the thread directory (without using nested transactions)
            for i in range(5):
                file_path = os.path.join(thread_dir, f"file_{i}.txt")
                content = f"Content for file {i} from thread {thread_id}"
                # Use direct file operations to avoid transaction manager thread safety issues
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)

            # Perform some additional operations
            # 1. Read and analyze Python files
            graph_tool = GenerateCallGraphTool()
            py_files = []
            for root, _, files in os.walk(os.path.join(temp_dir, "src")):
                for file in files:
                    if file.endswith(".py"):
                        py_files.append(os.path.join(root, file))

            if py_files:
                # Analyze a random Python file
                file_path = random.choice(py_files)
                result = graph_tool.execute(path=file_path)
                assert result["status"] == "success"

            # 2. Read large data files
            large_files = [os.path.join(temp_dir, file_path) for file_path, _ in data_files]
            if large_files:
                file_path = random.choice(large_files)
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                assert len(content) > 0

        # Execute operations in parallel
        num_threads = 8
        start_time = time.time()
        with concurrent.futures.ThreadPoolExecutor(max_workers=num_threads) as executor:
            futures = [executor.submit(thread_safe_operation, i) for i in range(num_threads)]
            # Wait for all threads to complete
            for future in concurrent.futures.as_completed(futures):
                try:
                    future.result()
                except Exception as e:
                    print(f"Thread error: {e}")
                    raise

        parallel_time = time.time() - start_time
        print(f"Parallel operations completed in {parallel_time:.2f} seconds")

        # 4. Transaction Usage (Non-Nested)
        print("\n4. Testing transaction usage (non-nested)...")

        # Create a transaction for file operations
        with TransactionManager():
            # Create some files
            for i in range(5):
                file_path = os.path.join(temp_dir, f"transaction_test_{i}.txt")
                create_file(file_path, f"Transaction test file {i}")

            # Modify a config file
            config_path = os.path.join(temp_dir, "config/settings.json")
            if path_exists(config_path):
                content = read_file(config_path)
                config = json.loads(content)
                config["modified_by"] = "transaction_test"
                config["timestamp"] = time.time()
                create_file(config_path, json.dumps(config, indent=2))

        # Verify transaction results
        for i in range(5):
            file_path = os.path.join(temp_dir, f"transaction_test_{i}.txt")
            assert path_exists(file_path), f"File transaction_test_{i}.txt should exist"
            content = read_file(file_path)
            assert f"Transaction test file {i}" == content

        # 5. Error Recovery Test
        print("\n5. Testing error recovery...")

        # Create a transaction that will partially fail
        try:
            with TransactionManager():
                # Create some valid files
                for i in range(5):
                    file_path = os.path.join(temp_dir, f"recovery_test_{i}.txt")
                    create_file(file_path, f"Recovery test file {i}")

                # Try to read a non-existent file (should fail)
                non_existent_path = os.path.join(temp_dir, "non_existent_file.txt")
                if not path_exists(non_existent_path):
                    try:
                        content = read_file(non_existent_path)
                        assert False, "Should have raised FileNotFoundError"
                    except FileNotFoundError:
                        # Expected error
                        pass

                # Continue with valid operations
                for i in range(5, 10):
                    file_path = os.path.join(temp_dir, f"recovery_test_{i}.txt")
                    create_file(file_path, f"Recovery test file {i}")
        except Exception as e:
            print(f"Expected error during recovery test: {e}")

        # Verify recovery
        for i in range(10):
            file_path = os.path.join(temp_dir, f"recovery_test_{i}.txt")
            assert path_exists(file_path), f"File recovery_test_{i}.txt should exist"

        # 6. Tool Registry Integration
        print("\n6. Testing tool registry integration...")

        # Verify all tools are registered
        assert "create_file" in tool_registry
        assert "read_file" in tool_registry
        assert "list_files" in tool_registry
        assert "generate_call_graph" in tool_registry

        # Create tools from registry and use them
        registry_tools = []
        for tool_name in ["create_file", "read_file", "list_files", "generate_call_graph"]:
            tool_class = tool_registry[tool_name]
            tool_instance = tool_class()
            registry_tools.append((tool_name, tool_instance))

        # Find Python files for analysis
        py_files = []
        for root, _, files in os.walk(os.path.join(temp_dir, "src")):
            for file in files:
                if file.endswith(".py"):
                    py_files.append(os.path.join(root, file))

        # Use each tool
        for tool_name, tool in registry_tools:
            if tool_name == "create_file":
                result = tool.execute(
                    path=os.path.join(temp_dir, "registry_test.txt"),
                    content="Created by tool from registry"
                )
                assert result["status"] == "success"
            elif tool_name == "read_file":
                result = tool.execute(path=os.path.join(temp_dir, "registry_test.txt"))
                assert result["status"] == "success"
                assert result["observation"] == "Created by tool from registry"
            elif tool_name == "list_files":
                result = tool.execute(directory=temp_dir)
                assert result["status"] == "success"
                assert "registry_test.txt" in result["observation"]
            elif tool_name == "generate_call_graph":
                # Use a Python file for analysis
                if py_files:
                    result = tool.execute(path=py_files[0])
                    assert result["status"] == "success"

        # 7. Dynamic Module Loading/Unloading
        print("\n7. Testing dynamic module loading/unloading...")

        # Create a custom module
        custom_module_dir = os.path.join(temp_dir, "custom_modules")
        os.makedirs(custom_module_dir, exist_ok=True)

        # Create __init__.py
        create_file(os.path.join(custom_module_dir, "__init__.py"), "# Custom modules package")

        # Create a module that uses mindlink tools
        custom_module_content = """
from mindlink.tools.file_tools import create_file, read_file, path_exists

def create_and_read(path, content):
    create_file(path, content)
    if path_exists(path):
        return read_file(path)
    return None
"""
        create_file(os.path.join(custom_module_dir, "file_ops.py"), custom_module_content)

        # Add the temp directory to sys.path so we can import the custom module
        sys.path.insert(0, temp_dir)

        try:
            # Import the custom module
            import custom_modules.file_ops

            # Use the module
            test_path = os.path.join(temp_dir, "dynamic_module_test.txt")
            result = custom_modules.file_ops.create_and_read(test_path, "Created by dynamic module")
            assert result == "Created by dynamic module"

            # Reload the module to ensure it still works
            importlib.reload(custom_modules.file_ops)

            # Use it again after reload
            test_path2 = os.path.join(temp_dir, "dynamic_module_test2.txt")
            result = custom_modules.file_ops.create_and_read(test_path2, "Created after reload")
            assert result == "Created after reload"
        finally:
            # Clean up sys.path
            sys.path.remove(temp_dir)
            # Clean up imported modules
            if "custom_modules.file_ops" in sys.modules:
                del sys.modules["custom_modules.file_ops"]
            if "custom_modules" in sys.modules:
                del sys.modules["custom_modules"]

        # 8. Memory Efficiency Check
        print("\n8. Checking memory efficiency...")

        # Force garbage collection
        gc.collect()

        # Perform memory-intensive operations
        large_files = []
        start_time = time.time()

        # Create several large files
        for i in range(5):
            file_path = os.path.join(temp_dir, f"memory_test_{i}.dat")
            content = generate_random_content(1000)  # 1MB each
            create_file(file_path, content)
            large_files.append(file_path)

        # Read all large files
        for file_path in large_files:
            content = read_file(file_path)
            assert len(content) >= 1000 * 1024

        # Force garbage collection again
        gc.collect()

        memory_time = time.time() - start_time
        print(f"Memory-intensive operations completed in {memory_time:.2f} seconds")

        # 9. Final Verification
        print("\n9. Performing final verification...")

        # Count total files and directories
        total_files = 0
        total_dirs = 0

        for root, dirs, files in os.walk(temp_dir):
            total_dirs += len(dirs)
            total_files += len(files)

        print(f"Project contains {total_dirs} directories and {total_files} files")

        # Verify a sample of files
        verification_paths = [
            os.path.join(temp_dir, "src/core/base.py"),
            os.path.join(temp_dir, "config/settings.json"),
            os.path.join(temp_dir, "data/raw/large_data_1.txt"),
            os.path.join(temp_dir, "registry_test.txt"),
            os.path.join(temp_dir, "dynamic_module_test.txt")
        ]

        for path in verification_paths:
            assert path_exists(path), f"Verification failed: {path} does not exist"
            content = read_file(path)
            assert len(content) > 0, f"Verification failed: {path} is empty"

        print("\n=== Ultra-Advanced Integration Test Completed Successfully ===")

        # Final assertions
        assert parallel_time < 120, f"Parallel operations took too long: {parallel_time:.2f} seconds"
        assert memory_time < 60, f"Memory operations took too long: {memory_time:.2f} seconds"
        assert total_files > 50, f"Expected more than 50 files, got {total_files}"

    finally:
        # Clean up
        shutil.rmtree(temp_dir)


if __name__ == "__main__":
    pytest.main(["-xvs", __file__])
