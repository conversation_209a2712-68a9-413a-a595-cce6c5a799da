import pytest
from pathlib import Path
from mindlink.tools.ast_tools import WrapTransaction<PERSON>rappersTool


def test_wrap_transaction_wrappers(tmp_path):
    # Create a sample Python file with bare create_file and read_file calls
    sample = tmp_path / "sample.py"
    sample.write_text(
        """
from mindlink.tools.file_tools import create_file, read_file
create_file('a.txt', 'hello')
read_file('a.txt')
"""
    )
    # Run the tool to wrap those calls in TransactionManager
    tool = WrapTransactionWrappersTool()
    result = tool.execute(path=str(sample))
    assert result['status'] == 'success'
    # Read back the modified source
    modified = Path(sample).read_text()
    # It should import TransactionManager and wrap calls in a with-block
    assert 'from mindlink.tools.file_tools import TransactionManager' in modified
    # check that each call is nested under with TransactionManager()
    lines = modified.splitlines()
    # Look for 'with TransactionManager()' preceding create_file and read_file
    assert any('with TransactionManager()' in line for line in lines), "TransactionManager wrapper missing"
    assert any('create_file' in line and line.strip().startswith('create_file') == False for line in lines), "create_file not wrapped"
    assert any('read_file' in line and line.strip().startswith('read_file') == False for line in lines), "read_file not wrapped" 