#!/usr/bin/env python3
"""
Quick speed test for the ultra-fast single-request optimization.
"""

import logging
import time
import os

# Setup minimal logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')

# Import the tool
from mindlink.tools.file_tools import GenerateLargeFileTool

def quick_speed_test():
    """
    Test the ultra-fast single-request optimization.
    """
    print("🚀 Quick Speed Test - Single Request Optimization")
    print("=" * 50)
    
    # Initialize the tool
    tool = GenerateLargeFileTool()
    
    # Test parameters
    test_file = "quick_test_500_lines.py"
    target_lines = 500
    
    # Remove existing test file if it exists
    if os.path.exists(test_file):
        os.remove(test_file)
    
    print(f"Generating {target_lines} lines of Python code...")
    
    # Record start time
    start_time = time.time()
    
    # Execute the tool
    result = tool.execute(
        path=test_file,
        content_description="A comprehensive data processing library with classes for data validation, transformation, analysis, and reporting",
        target_line_count=target_lines,
        max_chunks=1,  # Force single chunk
        chunk_size_description="Generate complete, functional Python code"
    )
    
    # Record end time
    end_time = time.time()
    execution_time = end_time - start_time
    
    print(f"\n⏱️  Execution time: {execution_time:.2f} seconds")
    
    if result.get('status') == 'success':
        result_data = result.get('result', {})
        lines_written = result_data.get('lines_written', 0)
        chunks_written = result_data.get('chunks_written', 0)
        
        print(f"✅ Success!")
        print(f"📝 Lines written: {lines_written}/{target_lines} ({lines_written/target_lines*100:.1f}% of target)")
        print(f"📦 Chunks written: {chunks_written}")
        print(f"⚡ Speed: {lines_written/execution_time:.1f} lines/second")
        
        # Check actual file
        if os.path.exists(test_file):
            with open(test_file, 'r', encoding='utf-8') as f:
                actual_lines = len(f.readlines())
            print(f"📄 Actual file lines: {actual_lines}")
            
            # Performance evaluation
            if execution_time < 10:
                print("\n🎉 EXCELLENT! Under 10 seconds - Major speed improvement!")
            elif execution_time < 30:
                print("\n✅ GREAT! Under 30 seconds - Significant improvement!")
            elif execution_time < 60:
                print("\n👍 GOOD! Under 1 minute - Noticeable improvement!")
            else:
                print("\n⚠️  Still slow - needs more optimization")
                
            # Show first few lines of generated code
            print("\n📋 First 10 lines of generated code:")
            print("-" * 40)
            with open(test_file, 'r', encoding='utf-8') as f:
                for i, line in enumerate(f):
                    if i >= 10:
                        break
                    print(f"{i+1:2d}: {line.rstrip()}")
            print("-" * 40)
        else:
            print("❌ File was not created")
    else:
        print(f"❌ Error: {result.get('observation', 'Unknown error')}")
        if 'error' in result:
            print(f"   Details: {result['error']}")
    
    return execution_time, result.get('status') == 'success'

if __name__ == "__main__":
    execution_time, success = quick_speed_test()
    
    print(f"\n{'='*50}")
    if success and execution_time < 60:
        print("🎯 OPTIMIZATION SUCCESS: Fast generation achieved!")
    elif success:
        print("✅ Working but could be faster")
    else:
        print("❌ Test failed")