import pytest
import json
from pathlib import Path
from mindlink.tools.graph_tools import GenerateCallGraphTool


def test_generate_call_graph_single_file(tmp_path):
    # Create a sample Python file with multiple functions calling each other
    module = tmp_path / "module.py"
    module.write_text(
        """

def foo():
    bar()
    baz()

def bar():
    pass

def baz():
    bar()
"""
    )
    tool = GenerateCallGraphTool()
    result = tool.execute(path=str(module))
    assert result['status'] == 'success'
    graph = json.loads(result['observation'])
    # foo calls bar and baz; bar calls nothing; baz calls bar
    assert 'foo' in graph
    assert set(graph['foo']) == {'bar', 'baz'}
    assert graph['bar'] == []
    assert graph['baz'] == ['bar']


def test_generate_call_graph_directory(tmp_path):
    # Create directory with two files
    file1 = tmp_path / "a.py"
    file1.write_text(
        """

def alpha():
    beta()

def beta():
    pass
"""
    )
    subdir = tmp_path / 'sub'
    subdir.mkdir()
    file2 = subdir / "b.py"
    file2.write_text(
        """

def gamma():
    delta()

def delta():
    pass
"""
    )
    tool = GenerateCallGraphTool()
    result = tool.execute(path=str(tmp_path))
    assert result['status'] == 'success'
    graph = json.loads(result['observation'])
    # Ensure all functions are present
    expected_funcs = {'alpha', 'beta', 'gamma', 'delta'}
    assert set(graph.keys()) == expected_funcs
    assert graph['alpha'] == ['beta']
    assert graph['beta'] == []
    assert graph['gamma'] == ['delta']
    assert graph['delta'] == [] 