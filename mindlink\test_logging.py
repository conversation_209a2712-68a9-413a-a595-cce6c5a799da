import os
import sys
import shutil
import uuid

# Ensure module path
sys.path.insert(0, os.path.dirname(__file__))

from logging_utils import log_event, close_logs, create_user_input_log, create_tool_call_start_log, LOGS_DIR

# Clean logs dir
if os.path.exists(LOGS_DIR):
    shutil.rmtree(LOGS_DIR)
os.makedirs(LOGS_DIR)

# Create and log events
session_id = str(uuid.uuid4())
ev1 = create_user_input_log(session_id, "test input", intent="test")
log_event(ev1)
ev2 = create_tool_call_start_log(ev1.session_id, "dummy_tool", {"param": 123})
log_event(ev2)

# Close all logs
close_logs()

# Print results
print("Generated files:", os.listdir(LOGS_DIR))
for fname in sorted(os.listdir(LOGS_DIR)):
    path = os.path.join(LOGS_DIR, fname)
    print(f"\n--- {fname} ---")
    with open(path, 'r', encoding='utf-8') as f:
        print(f.read())
