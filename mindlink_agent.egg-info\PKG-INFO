Metadata-Version: 2.4
Name: mindlink-agent
Version: 0.1.0
Summary: A lightweight AI agent system using LLMs with a structured JSON-based communication interface
Home-page: https://github.com/mindlink/mindlink-agent
Author: MindLink Team
Author-email: <EMAIL>
Classifier: Programming Language :: Python :: 3
Classifier: License :: OSI Approved :: MIT License
Classifier: Operating System :: OS Independent
Classifier: Development Status :: 4 - Beta
Classifier: Intended Audience :: Developers
Classifier: Topic :: Scientific/Engineering :: Artificial Intelligence
Requires-Python: >=3.7
Description-Content-Type: text/markdown
License-File: LICENSE
Requires-Dist: openai>=1.0.0
Requires-Dist: pydantic>=2.0.0
Requires-Dist: requests>=2.25.0
Requires-Dist: python-dotenv>=1.0.0
Provides-Extra: dev
Requires-Dist: pytest>=6.0.0; extra == "dev"
Requires-Dist: black>=21.5b2; extra == "dev"
Requires-Dist: isort>=5.9.1; extra == "dev"
Requires-Dist: flake8>=3.9.2; extra == "dev"
Dynamic: author
Dynamic: author-email
Dynamic: classifier
Dynamic: description
Dynamic: description-content-type
Dynamic: home-page
Dynamic: license-file
Dynamic: provides-extra
Dynamic: requires-dist
Dynamic: requires-python
Dynamic: summary

# MindLink Agent

[![PyPI version](https://badge.fury.io/py/mindlink-agent.svg)](https://badge.fury.io/py/mindlink-agent)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)

A lightweight and powerful AI agent system using LLMs with a structured JSON-based communication interface.

## Overview

MindLink Agent is a modular AI agent framework that uses Large Language Models (LLMs) as the planning and reasoning engine. It follows the Observe-Plan-Act agent loop pattern and communicates with the LLM through a structured JSON-based interface called the MindLink API.

## Installation

You can install MindLink Agent from PyPI:

```bash
pip install mindlink-agent
```

Or install directly from the repository:

```bash
git clone https://github.com/mindlink/mindlink-agent.git
cd mindlink-agent
pip install -e .
```

### Key Features

- **Lightweight and Minimal**: The core system has minimal dependencies and complexity.
- **Powerful and Extensible**: Despite its simplicity, it can handle complex tasks by combining tools.
- **Transparent and Debuggable**: The LLM's decision-making process and execution are visible and traceable.
- **Agent Loop Based**: The system operates in a cycle: Observe -> Plan (by LLM) -> Act -> New Observation.

## Architecture

The MindLink Agent Core consists of the following components:

1. **Large Language Model (LLM)**: The core reasoning engine that understands goals, plans actions, and generates structured outputs.
2. **MindLink API Contract**: A JSON-based communication format that the LLM uses to express its actions.
3. **Tools**: A collection of Python functions that perform specific tasks.
4. **Agent Operating System**: The core logic that manages the agent loop, executes tools, and maintains state.

### Supported LLM Models

MindLink Agent Core supports multiple LLM providers:

1. **OpenRouter** (Default):
   - **DeepSeek-R1**: A powerful open-source model with strong reasoning capabilities
   - **GLM-Z1-32B**: A high-performance bilingual model with excellent instruction following

2. **OpenAI**:
   - GPT-4-Turbo
   - GPT-3.5-Turbo
   - Other OpenAI models

The system is designed to be easily extensible to support additional LLM providers.

### MindLink API Format

The LLM communicates with the system using a structured JSON format:

```json
{
  "action": {
    "tool_name": "string",
    "parameters": {
      // Tool-specific parameters
    }
  },
  "reasoning": "string",
  "thought": "string"
}
```

## Installation

1. Clone the repository:
   ```
   git clone https://github.com/yourusername/mindlink.git
   cd mindlink
   ```

2. Install dependencies:
   ```
   pip install -r requirements.txt
   ```

3. Set up your environment:
   ```
   # Copy the example .env file
   cp .env.example .env

   # Edit .env if you want to use OpenAI instead of the default OpenRouter models
   ```

   Note: By default, the system uses OpenRouter with pre-configured API keys for DeepSeek-R1 and GLM-Z1-32B models.

## Usage

### Basic Usage

```python
from mindlink import AgentOS, OpenRouterModel, DEFAULT_SYSTEM_PROMPT

# Create an LLM instance
llm = OpenRouterModel(
    model_name="deepseek-r1",  # Options: "deepseek-r1", "glm-z1-32b"
    temperature=0.7,
    max_tokens=1024,
    top_p=0.9
)

# Create Agent OS
agent = AgentOS(
    llm=llm,
    system_prompt_template=DEFAULT_SYSTEM_PROMPT,
    max_steps=15
)

# Run the agent
goal = "Create a file called hello.txt with the content 'Hello, MindLink!'"
success, result, history = agent.run(goal)

print(f"Result: {result}")
```

### Using OpenAI Instead of OpenRouter

```python
from mindlink import AgentOS, OpenAIModel, DEFAULT_SYSTEM_PROMPT

# Create an OpenAI LLM instance
llm = OpenAIModel(
    model="gpt-4-turbo",
    temperature=0.2,
    max_tokens=4096
)

# Create Agent OS
agent = AgentOS(
    llm=llm,
    system_prompt_template=DEFAULT_SYSTEM_PROMPT,
    max_steps=15
)

# Run the agent
goal = "Create a file called hello.txt with the content 'Hello, MindLink!'"
success, result, history = agent.run(goal)
```

### Command Line Interface

You can also use the command line interface:

```bash
# Run with a specific goal
python -m mindlink.main "Create a file called hello.txt with the content 'Hello, MindLink!'"

# Run in interactive mode
python -m mindlink.main --interactive
```

### Creating Custom Tools

You can extend MindLink Agent with custom tools:

```python
from mindlink import Tool, register_tool
from pydantic import Field

@register_tool
class MyCustomTool(Tool):
    name = "my_custom_tool"
    description = "Description of what my tool does"

    class Parameters(Tool.parameters_model):
        param1: str = Field(..., description="Description of parameter 1")
        param2: int = Field(default=0, description="Description of parameter 2")

    parameters_model = Parameters

    def execute(self, **parameters):
        # Implement your tool logic here
        result = "Tool execution result"
        return {
            "observation": result,
            "status": "success"
        }
```

## Library Structure

The MindLink Agent library is organized as follows:

```
mindlink/
├── agent.py            # Main Agent OS implementation
├── config.py           # Configuration settings
├── models/             # LLM model implementations
│   ├── llm.py          # LLM interface
│   ├── openai.py       # OpenAI implementation
│   └── openrouter.py   # OpenRouter implementation
├── schemas/            # API schemas
│   └── mindlink.py     # MindLink API JSON schema
├── tools/              # Tool implementations
│   ├── base.py         # Base tool class and registry
│   ├── file_tools.py   # File operations tools
│   └── shell_tools.py  # Shell command execution tools
└── utils/              # Utility functions
    └── json_parser.py  # JSON parsing utilities
```

## Examples

The repository includes several examples to help you get started:

- `examples/simple_agent.py`: Basic usage of the MindLink Agent
- `examples/custom_tool.py`: Creating and using custom tools

## License

MIT License

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.
