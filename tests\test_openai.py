import unittest
from unittest.mock import MagicMock, patch, call
from mindlink.models.openai import OpenAIModel
import os

# Mock the OpenAI client and its response objects minimally
class MockDelta:
    def __init__(self, content):
        self.content = content

class MockChoice:
    def __init__(self, content_delta=None, finish_reason=None):
        self.delta = Mock<PERSON><PERSON><PERSON>(content_delta) if content_delta is not None else None
        self.finish_reason = finish_reason
        # For non-streaming message
        self.message = MockDelta(content_delta) if finish_reason == 'stop' and content_delta else None


class MockCompletionChunk:
    def __init__(self, choices, usage=None):
        self.choices = choices
        self.usage = usage # Can be None or a mock usage object

class MockUsage:
    def __init__(self, total_tokens):
        self.total_tokens = total_tokens

class TestOpenAIModelStreaming(unittest.TestCase):

    def setUp(self):
        # Ensure API key is set for initialization, even if not used by mocks
        os.environ["OPENAI_API_KEY"] = "test_api_key"
        self.model = OpenAIModel(api_key="test_api_key")

    @patch('mindlink.models.openai.openai.OpenAI') # More specific patch target
    def test_generate_streaming(self, MockOpenAIClass):
        # When self.model was created in setUp, if @patch was applied to the class or setUp,
        # self.model.client would be an instance of MockOpenAIClass.
        # However, @patch on a method applies the mock only during that method's execution.
        # So, self.model in setUp was created with the *real* openai.OpenAI.
        # We need to re-initialize the model *after* the patch is active or patch differently.

        # Option 1: Patch the instance directly if we know how it's stored.
        # self.model.client = MockOpenAIClass.return_value # Risky if client name changes

        # Option 2: Patch the 'openai.OpenAI' class such that when OpenAIModel initializes
        # its client, it gets our fully configured mock client instance.
        
        mock_client_instance_configured = MockOpenAIClass.return_value # This is the mock instance returned by OpenAI()
        mock_completions_create_method = mock_client_instance_configured.chat.completions.create

        # Simulate streamed response chunks
        chunks_data = ["Hello", ", ", "world", "!"]
        mock_stream_iterable = []
        for i, data_chunk in enumerate(chunks_data):
            is_last_chunk = i == len(chunks_data) - 1
            mock_stream_iterable.append(
                MockCompletionChunk(
                    choices=[MockChoice(content_delta=data_chunk)],
                    usage=MockUsage(total_tokens=10) if is_last_chunk else None
                )
            )
        # Add a final chunk that signals the end, often without new content but with metadata
        mock_stream_iterable.append(MockCompletionChunk(choices=[MockChoice(finish_reason='stop')], usage=MockUsage(total_tokens=10))) # Ensure usage is on a final chunk

        mock_completions_create_method.return_value = mock_stream_iterable

        callback_mock = MagicMock()
        system_prompt = "System prompt"
        user_prompt = "User prompt"
        
        # Re-initialize model *inside the test method* so it uses the patched OpenAI class
        model_under_test = OpenAIModel(api_key="test_api_key_for_this_test")

        # Test with stream=True explicitly in generate
        result = model_under_test.generate(system_prompt, user_prompt, stream=True, callback=callback_mock)

        # Verify chat.completions.create was called correctly
        mock_completions_create_method.assert_called_once_with(
            model=model_under_test.model_name, # Use model_under_test here
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ],
            temperature=model_under_test.temperature, # Use model_under_test here
            max_tokens=model_under_test.max_tokens,   # Use model_under_test here
            stream=True
        )

        # Verify callback was called for each content chunk
        expected_calls = [call(chunk_data) for chunk_data in chunks_data]
        self.assertEqual(callback_mock.call_args_list, expected_calls)

        # Verify the final concatenated result
        self.assertEqual(result, "".join(chunks_data))
        
        # Verify token usage was captured (assuming it comes in the last chunk)
        self.assertEqual(model_under_test.last_tokens_used, 10)

    @patch('mindlink.models.openai.openai.OpenAI') # More specific patch target
    def test_generate_non_streaming(self, MockOpenAIClass): 
        mock_openai_client_instance = MockOpenAIClass.return_value
        mock_completions_create_method = mock_openai_client_instance.chat.completions.create
        
        # Simulate non-streamed response
        expected_response_content = "This is a non-streaming response."
        
        mock_response_object = MagicMock() 
        mock_choice = MagicMock()
        mock_choice.message = MagicMock()
        mock_choice.message.content = expected_response_content
        mock_response_object.choices = [mock_choice]
        mock_response_object.usage = MockUsage(total_tokens=5)

        mock_completions_create_method.return_value = mock_response_object
        
        system_prompt = "System prompt"
        user_prompt = "User prompt"

        model_under_test = OpenAIModel(api_key="test_api_key_for_this_test_non_stream")
        result = model_under_test.generate(system_prompt, user_prompt, stream=False)

        mock_completions_create_method.assert_called_once_with(
            model=model_under_test.model_name,
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ],
            temperature=model_under_test.temperature,
            max_tokens=model_under_test.max_tokens,
            stream=False 
        )

        self.assertEqual(result, expected_response_content)
        self.assertEqual(model_under_test.last_tokens_used, 5) # Use model_under_test here

    def tearDown(self):
        # Clean up environment variables if necessary
        if "OPENAI_API_KEY" in os.environ:
            del os.environ["OPENAI_API_KEY"]

if __name__ == '__main__':
    unittest.main()
