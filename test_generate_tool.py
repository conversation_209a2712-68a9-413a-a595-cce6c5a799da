#!/usr/bin/env python3
"""
Test script to demonstrate the GenerateLargeFileTool capabilities
"""

import sys
import os
sys.path.append('.')

from mindlink.tools.file_tools import GenerateLargeFileTool

def test_generate_large_file():
    """Test the GenerateLargeFileTool with different scenarios"""
    tool = GenerateLargeFileTool()
    
    print("=== Testing GenerateLargeFileTool ===")
    print()
    
    # Test 1: Small file (50 lines)
    print("Test 1: Generating 50-line file...")
    result1 = tool.execute(
        path='demo_50_lines.py',
        content_description='Create a Python file with basic functions, classes, and utility code',
        target_line_count=50
    )
    print(f"Result 1: {result1}")
    print()
    
    # Test 2: Medium file (200 lines)
    print("Test 2: Generating 200-line file...")
    result2 = tool.execute(
        path='demo_200_lines.py',
        content_description='Create a comprehensive Python module with multiple classes, functions, data processing, and algorithms',
        target_line_count=200,
        max_chunks=5
    )
    print(f"Result 2: {result2}")
    print()
    
    # Test 3: Large file (500 lines)
    print("Test 3: Generating 500-line file...")
    result3 = tool.execute(
        path='demo_500_lines.py',
        content_description='Create a large Python application with web scraping, data analysis, file handling, database operations, and comprehensive functionality',
        target_line_count=500,
        max_chunks=8
    )
    print(f"Result 3: {result3}")
    print()
    
    # Test 4: Very large file (1000 lines)
    print("Test 4: Generating 1000-line file...")
    result4 = tool.execute(
        path='demo_1000_lines.py',
        content_description='Create a comprehensive Python framework with multiple modules, advanced algorithms, web services, data processing pipelines, machine learning utilities, and extensive functionality',
        target_line_count=1000,
        max_chunks=15
    )
    print(f"Result 4: {result4}")
    print()
    
    print("=== Test Summary ===")
    tests = [result1, result2, result3, result4]
    targets = [50, 200, 500, 1000]
    
    for i, (result, target) in enumerate(zip(tests, targets), 1):
        if result['status'] == 'success':
            lines = result['result']['lines_written']
            chunks = result['result']['chunks_written']
            target_met = result['result']['target_lines_met']
            print(f"Test {i}: Target {target} lines -> Generated {lines} lines in {chunks} chunks (Target met: {target_met})")
        else:
            print(f"Test {i}: FAILED - {result.get('observation', 'Unknown error')}")

if __name__ == '__main__':
    test_generate_large_file()