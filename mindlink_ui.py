import streamlit as st
import os
import json
from mindlink.agent import <PERSON><PERSON>
from mindlink.config import load_config
from mindlink.executor import ToolExecutor
from mindlink.tools.file_tools import CreateFileTool
from mindlink.tools.file_tools import GenerateLargeFileTool
from mindlink.logging_utils import log_event, close_logs
import time
import traceback
import logging
logging.basicConfig(level=logging.WARNING, force=True)

# Page configuration
st.set_page_config(
    page_title="MindLink Agent UI",
    page_icon="🧠",
    layout="wide",
    initial_sidebar_state="expanded"
)

# File paths
MODEL_CONFIG_FILE_PATH = ".model_config.json"
API_KEY_FILE_PATH = ".openaikey"

# Default model configuration
DEFAULT_MODEL_CONFIG = {
    "active_models": ["gpt-4o-mini"],
    "primary_model": "gpt-4o-mini",
    "model_priority": ["gpt-4o-mini"],
    "failover_enabled": True,
    "custom_models": {},
    "builtin_models": {
        "gpt-4o-mini": {
            "provider": "openrouter",
            "model_id": "openai/gpt-4o-mini",
            "temperature": 0.1,
            "max_tokens": 8192
        },
        "gpt-4o": {
            "provider": "openrouter",
            "model_id": "openai/gpt-4o",
            "temperature": 0.1,
            "max_tokens": 8192
        }
    }
}

def load_model_config():
    """Load model configuration from file or return default."""
    try:
        with open(MODEL_CONFIG_FILE_PATH, 'r') as f:
            config = json.load(f)
            # Ensure all required keys exist
            for key, value in DEFAULT_MODEL_CONFIG.items():
                if key not in config:
                    config[key] = value
            return config
    except (FileNotFoundError, json.JSONDecodeError):
        return DEFAULT_MODEL_CONFIG.copy()

def save_model_config(config):
    """Save model configuration to file."""
    try:
        # Ensure directory exists
        os.makedirs(os.path.dirname(MODEL_CONFIG_FILE_PATH) if os.path.dirname(MODEL_CONFIG_FILE_PATH) else '.', exist_ok=True)
        
        with open(MODEL_CONFIG_FILE_PATH, 'w') as f:
            json.dump(config, f, indent=2)
        return True
    except Exception as e:
        st.error(f"Failed to save model configuration: {e}")
        return False

def load_system_config():
    """Load system configuration."""
    config_path = "mindlink/config.py"
    if os.path.exists(config_path):
        try:
            with open(config_path, 'r') as f:
                content = f.read()
                # Extract model configuration from the file
                # This is a simplified approach - in practice, you might want to parse this more carefully
                return {}
        except Exception:
            return {}
    return {}

def update_system_config(model_config):
    """Update system configuration with model settings."""
    try:
        config_path = "mindlink/config.py"
        if os.path.exists(config_path):
            # Read current config
            with open(config_path, 'r') as f:
                content = f.read()
            
            # Update the configuration
            # This is a simplified approach - you might want to implement more sophisticated config updating
            lines = content.split('\n')
            updated_lines = []
            
            for line in lines:
                if 'ACTIVE_MODELS' in line:
                    updated_lines.append(f'ACTIVE_MODELS = {model_config["active_models"]}')
                elif 'PRIMARY_MODEL' in line:
                    updated_lines.append(f'PRIMARY_MODEL = "{model_config["primary_model"]}"')
                else:
                    updated_lines.append(line)
            
            # Write back to file
            with open(config_path, 'w') as f:
                f.write('\n'.join(updated_lines))
                
        return True
    except Exception as e:
        st.error(f"Failed to update system configuration: {e}")
        return False

def load_api_key_from_file(file_path):
    """Load API key from file."""
    try:
        if os.path.exists(file_path):
            with open(file_path, 'r') as f:
                return f.read().strip()
    except Exception:
        pass
    return None

def save_api_key_to_file(api_key, file_path):
    """Save API key to file."""
    try:
        with open(file_path, 'w') as f:
            f.write(api_key)
        return True
    except Exception as e:
        st.error(f"Failed to save API key: {e}")
        return False

# Initialize session state
if "model_config" not in st.session_state:
    st.session_state.model_config = load_model_config()

if "messages" not in st.session_state:
    st.session_state.messages = []

if "agent" not in st.session_state:
    st.session_state.agent = None

# Sidebar for model configuration
with st.sidebar:
    st.title("🧠 MindLink Agent")
    st.markdown("---")
    
    # Model selection
    st.markdown("**Active Models:**")
    available_models = list(st.session_state.model_config["builtin_models"].keys()) + list(st.session_state.model_config["custom_models"].keys())
    
    # Display current active models
    active_models = st.session_state.model_config.get("active_models", [])
    for model in active_models:
        col1, col2 = st.columns([3, 1])
        with col1:
            st.write(f"✅ {model}")
        with col2:
            if st.button("❌", key=f"remove_{model}", help=f"Remove {model}"):
                st.session_state.model_config["active_models"].remove(model)
                if model in st.session_state.model_config["model_priority"]:
                    st.session_state.model_config["model_priority"].remove(model)
                # Update primary model if removed
                if st.session_state.model_config["primary_model"] == model:
                    if st.session_state.model_config["active_models"]:
                        st.session_state.model_config["primary_model"] = st.session_state.model_config["active_models"][0]
                    else:
                        st.session_state.model_config["primary_model"] = ""
                save_model_config(st.session_state.model_config)
                update_system_config(st.session_state.model_config)
                st.rerun()
    
    # Add custom model form
    st.markdown("**Add Custom Model:**")
    with st.form("add_custom_model", clear_on_submit=True):
        col1, col2 = st.columns(2)
        with col1:
            custom_name = st.text_input("Model Name:", placeholder="My Custom Model")
            custom_provider = st.selectbox("Provider:", ["openrouter", "openai", "custom"])
        with col2:
            custom_model_id = st.text_input("Model ID:", placeholder="gpt-4-turbo")
            custom_api_key = st.text_input("API Key (optional):", type="password")
        
        # Advanced settings in columns
        col3, col4 = st.columns(2)
        with col3:
            custom_temperature = st.slider("Temperature:", 0.0, 2.0, 0.1, 0.1)
        with col4:
            custom_max_tokens = st.number_input("Max Tokens:", 1000, 32000, 8192)
        
        if custom_provider == "custom":
            custom_api_url = st.text_input("API URL:", placeholder="https://api.example.com/v1")
        else:
            custom_api_url = None
        
        if st.form_submit_button("➕ Add Custom Model", type="primary"):
            if custom_name and custom_model_id:
                custom_model_config = {
                    "provider": custom_provider,
                    "model_id": custom_model_id,
                    "temperature": custom_temperature,
                    "max_tokens": custom_max_tokens,
                    "api_key": custom_api_key or None,
                    "api_url": custom_api_url
                }
                
                st.session_state.model_config["custom_models"][custom_name] = custom_model_config
                st.session_state.model_config["active_models"].append(custom_name)
                st.session_state.model_config["model_priority"].append(custom_name)
                # Set as primary if it's the first model
                if len(st.session_state.model_config["active_models"]) == 1:
                    st.session_state.model_config["primary_model"] = custom_name
                save_model_config(st.session_state.model_config)
                update_system_config(st.session_state.model_config)
                st.success(f"✅ Added: {custom_name}")
                st.rerun()
            else:
                st.error("Please provide both model name and model ID")
    
    # Failover settings (outside the form)
    st.markdown("**Failover Settings:**")
    col1, col2 = st.columns([2, 1])
    with col1:
        failover_enabled = st.checkbox(
            "Enable automatic failover between models", 
            value=st.session_state.model_config.get("failover_enabled", True),
            help="When enabled, the system will automatically try backup models if the primary fails"
        )
    with col2:
        if st.button("🔄 Reorder", help="Click to reorder failover priority"):
            st.session_state.show_priority_editor = not st.session_state.get("show_priority_editor", False)
    
    if failover_enabled != st.session_state.model_config.get("failover_enabled", True):
        st.session_state.model_config["failover_enabled"] = failover_enabled
        save_model_config(st.session_state.model_config)
        update_system_config(st.session_state.model_config)
    
    # Priority editor (shown when requested)
    if st.session_state.get("show_priority_editor", False) and len(active_models) > 1:
        st.markdown("**Failover Priority Order:**")
        priority_list = st.session_state.model_config["model_priority"].copy()
        
        for i, model_name in enumerate(priority_list):
            col1, col2, col3 = st.columns([4, 1, 1])
            with col1:
                st.write(f"{i+1}. {model_name}")
            with col2:
                if i > 0 and st.button("⬆️", key=f"up_{i}", help="Move up"):
                    priority_list[i], priority_list[i-1] = priority_list[i-1], priority_list[i]
                    st.session_state.model_config["model_priority"] = priority_list
                    save_model_config(st.session_state.model_config)
                    update_system_config(st.session_state.model_config)
                    st.rerun()
            with col3:
                if i < len(priority_list) - 1 and st.button("⬇️", key=f"down_{i}", help="Move down"):
                    priority_list[i], priority_list[i+1] = priority_list[i+1], priority_list[i]
                    st.session_state.model_config["model_priority"] = priority_list
                    save_model_config(st.session_state.model_config)
                    update_system_config(st.session_state.model_config)
                    st.rerun()
    
    st.markdown("---")
    
    # API key input management
    # Initialize session state for API key if it doesn't exist
    if "api_key" not in st.session_state:
        # Try loading from .env file first, then from saved file, then env var, then default to empty
        env_key = os.getenv("OPENROUTER_API_KEY")
        file_key = load_api_key_from_file(API_KEY_FILE_PATH)
        st.session_state.api_key = env_key or file_key or ""

    # Initialize session state for controlling API key input visibility
    if "show_api_key_input" not in st.session_state:
        st.session_state.show_api_key_input = not bool(st.session_state.api_key)
    
    # API Key section
    st.markdown("**API Configuration:**")
    
    # Show current API key status
    if st.session_state.api_key:
        masked_key = st.session_state.api_key[:8] + "*" * (len(st.session_state.api_key) - 12) + st.session_state.api_key[-4:] if len(st.session_state.api_key) > 12 else "*" * len(st.session_state.api_key)
        st.success(f"✅ API Key: {masked_key}")
        
        col1, col2 = st.columns(2)
        with col1:
            if st.button("🔄 Change Key"):
                st.session_state.show_api_key_input = True
                st.rerun()
        with col2:
            if st.button("❌ Remove Key"):
                st.session_state.api_key = ""
                if os.path.exists(API_KEY_FILE_PATH):
                    os.remove(API_KEY_FILE_PATH)
                st.session_state.show_api_key_input = True
                st.rerun()
    else:
        st.warning("⚠️ No API key configured")
        st.session_state.show_api_key_input = True
    
    # API key input (shown when needed)
    if st.session_state.show_api_key_input:
        with st.form("api_key_form"):
            new_api_key = st.text_input(
                "OpenRouter API Key:",
                type="password",
                placeholder="sk-or-v1-...",
                help="Get your API key from https://openrouter.ai/keys"
            )
            
            col1, col2 = st.columns(2)
            with col1:
                if st.form_submit_button("💾 Save Key", type="primary"):
                    if new_api_key:
                        st.session_state.api_key = new_api_key
                        if save_api_key_to_file(new_api_key, API_KEY_FILE_PATH):
                            st.session_state.show_api_key_input = False
                            st.success("✅ API key saved!")
                            st.rerun()
                    else:
                        st.error("Please enter a valid API key")
            
            with col2:
                if st.form_submit_button("❌ Cancel"):
                    if st.session_state.api_key:  # Only hide if we have an existing key
                        st.session_state.show_api_key_input = False
                        st.rerun()
    
    st.markdown("---")
    
    # Generate Large File Tool
    with st.expander("📄 Generate Large File", expanded=False):
        st.markdown("**Generate Large File Tool**")
        
        with st.form("generate_large_file"):
            file_path = st.text_input(
                "File Path:",
                placeholder="large_file.txt",
                help="Path where the large file will be created"
            )
            
            file_size_mb = st.number_input(
                "File Size (MB):",
                min_value=1,
                max_value=1000,
                value=10,
                help="Size of the file to generate in megabytes"
            )
            
            content_type = st.selectbox(
                "Content Type:",
                ["random_text", "repeated_pattern", "numbers", "lorem_ipsum"],
                help="Type of content to generate"
            )
            
            if content_type == "repeated_pattern":
                pattern = st.text_input(
                    "Pattern:",
                    value="Hello World! ",
                    help="Pattern to repeat in the file"
                )
            else:
                pattern = None
            
            if st.form_submit_button("🚀 Generate File", type="primary"):
                if file_path:
                    try:
                        # Create the tool and execute
                        tool = GenerateLargeFileTool()
                        
                        # Prepare parameters
                        params = {
                            "file_path": file_path,
                            "size_mb": file_size_mb,
                            "content_type": content_type
                        }
                        
                        if pattern:
                            params["pattern"] = pattern
                        
                        # Show progress
                        with st.spinner(f"Generating {file_size_mb}MB file..."):
                            result = tool.execute(params)
                        
                        if result.get("success"):
                            st.success(f"✅ File generated successfully!")
                            st.info(f"📁 File: {result.get('file_path')}")
                            st.info(f"📊 Size: {result.get('actual_size_mb', 'Unknown')} MB")
                        else:
                            st.error(f"❌ Failed to generate file: {result.get('error', 'Unknown error')}")
                    
                    except Exception as e:
                        st.error(f"❌ Error: {str(e)}")
                        st.error(f"Traceback: {traceback.format_exc()}")
                else:
                    st.error("Please provide a file path")

# Main chat interface
st.title("💬 Chat with MindLink Agent")

# Display chat messages
for message in st.session_state.messages:
    with st.chat_message(message["role"]):
        st.markdown(message["content"])
        
        # Show execution steps if available
        if "steps" in message and message["steps"]:
            for i, step in enumerate(message["steps"]):
                # Defensive: handle both expected object and tuple/MindLinkResponse
                tool_name = getattr(step, 'tool_name', None)
                if tool_name is None and isinstance(step, tuple):
                    # Try to extract from tuple if possible
                    try:
                        tool_name = step[0].tool_name if hasattr(step[0], 'tool_name') else str(step[0])
                    except Exception:
                        tool_name = str(step)
                if tool_name is None and hasattr(step, 'observation'):
                    tool_name = 'observation'
                icon = "🔧" if tool_name != "final_answer" else "✅"
                with st.expander(f"{icon} مرحله {i+1}: {tool_name}", expanded=False):
                    tool_input = getattr(step, 'tool_input', None)
                    if tool_input is None and hasattr(step, 'parameters'):
                        tool_input = step.parameters
                    st.code(tool_input, language="json")
                    if hasattr(step, 'tool_output') and step.tool_output:
                        st.markdown("**خروجی:**")
                        if isinstance(step.tool_output, dict):
                            st.json(step.tool_output)
                        else:
                            st.text(step.tool_output)

# Chat input
if prompt := st.chat_input("پیام خود را اینجا بنویسید..."):
    # Check if API key is available
    if not st.session_state.api_key:
        st.error("⚠️ لطفاً ابتدا API Key خود را در نوار کناری وارد کنید.")
        st.stop()
    
    # Add user message to chat history
    st.session_state.messages.append({"role": "user", "content": prompt})
    with st.chat_message("user"):
        st.markdown(prompt)
    
    # Generate assistant response
    with st.chat_message("assistant"):
        with st.spinner("🧠 Thinking and planning..."):
            try:
                # Initialize agent if not already done
                if st.session_state.agent is None:
                    # Set environment variable for API key
                    os.environ["OPENROUTER_API_KEY"] = st.session_state.api_key
                    # Load default config and create LLM
                    config = load_config(None)
                    # Inject API key into config for create_llm
                    if "llm" not in config:
                        config["llm"] = {}
                    config["llm"]["api_key"] = st.session_state.api_key
                    from mindlink.main import create_llm
                    llm = create_llm(config)
                    
                    # Create AgentOS instance
                    st.session_state.agent = AgentOS(
                        llm=llm,
                        system_prompt_template=config["agent"]["system_prompt_template"],
                        max_steps=config["agent"]["max_steps"],
                        max_parsing_retries=config["agent"]["max_parsing_retries"],
                        retry_delay=config["agent"]["retry_delay"]
                    )
                
                # Execute the prompt using AgentOS run method
                with st.spinner(f"⚙️ در حال اجرای وظیفه..."):
                    success, result, history = st.session_state.agent.run(prompt)
                
                # Display the result
                if success and result:
                    response_content = result
                    st.markdown(response_content)
                    
                    # Add assistant message to chat history
                    st.session_state.messages.append({
                        "role": "assistant", 
                        "content": response_content,
                        "steps": history if history else []
                    })
                else:
                    error_msg = result if result else "خطا در دریافت پاسخ از Agent"
                    st.error(f"❌ {error_msg}")
                    
            except Exception as e:
                st.error(f"❌ خطا: {str(e)}")
                st.error(f"جزئیات: {traceback.format_exc()}")

# Sidebar footer
with st.sidebar:
    st.markdown("---")
    with st.expander("ℹ️ Agent Details", expanded=False):
        if st.session_state.agent:
            st.json({
                "Active Models": st.session_state.model_config["active_models"],
                "Primary Model": st.session_state.model_config["primary_model"],
                "Failover Enabled": st.session_state.model_config["failover_enabled"],
                "Model Priority": st.session_state.model_config["model_priority"]
            })
        else:
            st.info("Agent not initialized yet")