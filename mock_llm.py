from typing import List, Dict, Any, Optional
from mindlink.models.llm import LLMInterface

class MockLLM:
    """Mock LLM for testing purposes."""
    
    def __init__(self, model_name: str = "mock-model"):
        self.model_name = model_name
        self.params = {
            "temperature": 0.7,
            "max_tokens": 1000,
            "top_p": 1.0
        }
        self.responses = []
        
    def set_responses(self, responses: List[str]):
        """Set the responses to be returned in order."""
        self.responses = responses.copy()
        
    def generate(self, system_prompt: str, user_prompt: str, history: List[Dict[str, str]] = None) -> str:
        """Generate a mock response."""
        print(f"\n[DEBUG] MockLLM.generate called with:")
        print(f"System prompt: {system_prompt[:200]}...")
        print(f"User prompt: {user_prompt}")
        
        if not self.responses:
            print("No more test responses available, using fallback")
            return '{"action": {"tool_name": "noop", "parameters": {}}, "reasoning": "No test response configured"}'
        
        response = self.responses.pop(0)
        print(f"[DEBUG] Returning mock response: {response}")
        return response
    
    def stream(self, system_prompt: str, user_prompt: str, history: List[Dict[str, str]] = None) -> str:
        """Stream a mock response."""
        return self.generate(system_prompt, user_prompt, history)
